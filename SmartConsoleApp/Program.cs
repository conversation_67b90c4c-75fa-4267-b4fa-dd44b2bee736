using System.Text.Json;
using PuppeteerSharp;

namespace SmartConsoleApp
{
    class Program
    {
        private static IBrowser? browser;
        private static IPage? page;

        static async Task Main(string[] args)
        {
            Console.WriteLine("开始初始化程序...");

            // 创建必要的文件夹
            CreateDirectories();

            Console.WriteLine("开始初始化浏览器...");

            try
            {
                Console.WriteLine("开始并行处理两个网站...");

                // 创建两个并行任务
                var aveTask = Task.Run(async () =>
                {
                    IBrowser? aveBrowser = null;
                    IPage? avePage = null;
                    try
                    {
                        Console.WriteLine("[Ave.ai] 初始化浏览器...");
                        (aveBrowser, avePage) = await InitializeBrowserInstance();
                        Console.WriteLine("[Ave.ai] 开始处理...");
                        await ScrapeAveSmartPage(avePage);
                    }
                    finally
                    {
                        await CleanupBrowserInstance(avePage, aveBrowser);
                    }
                });

                var gmgnTask = Task.Run(async () =>
                {
                    IBrowser? gmgnBrowser = null;
                    IPage? gmgnPage = null;
                    try
                    {
                        Console.WriteLine("[GMGN] 初始化浏览器...");
                        (gmgnBrowser, gmgnPage) = await InitializeBrowserInstance();
                        Console.WriteLine("[GMGN] 开始处理...");
                        await ScrapeGmgnPage(gmgnPage);
                    }
                    finally
                    {
                        await CleanupBrowserInstance(gmgnPage, gmgnBrowser);
                    }
                });

                // 等待两个任务都完成
                await Task.WhenAll(aveTask,gmgnTask);

                Console.WriteLine("两个网站处理完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        static void CreateDirectories()
        {
            try
            {
                // 创建txt文件夹
                string txtFolder = Path.Combine(Directory.GetCurrentDirectory(), "txt");
                Directory.CreateDirectory(txtFolder);
                Console.WriteLine($"确保txt文件夹存在: {txtFolder}");

                // 创建images文件夹
                string imagesFolder = Path.Combine(Directory.GetCurrentDirectory(), "images");
                Directory.CreateDirectory(imagesFolder);
                Console.WriteLine($"确保images文件夹存在: {imagesFolder}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建文件夹失败: {ex.Message}");
            }
        }

        static async Task<(IBrowser browser, IPage page)> InitializeBrowserInstance()
        {
            // 下载Chromium（如果需要）
            await new BrowserFetcher().DownloadAsync();

            // 启动浏览器
            var browserInstance = await Puppeteer.LaunchAsync(new LaunchOptions
            {
                Headless = false,
                Args = new[]
                {
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-accelerated-2d-canvas",
                    "--no-first-run",
                    "--no-zygote",
                    "--disable-gpu"
                }
            });

            // 创建新页面
            var pageInstance = await browserInstance.NewPageAsync();

            // 设置用户代理
            await pageInstance.SetUserAgentAsync(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");

            return (browserInstance, pageInstance);
        }

        static async Task CleanupBrowserInstance(IPage? pageInstance, IBrowser? browserInstance)
        {
            try
            {
                if (pageInstance != null)
                {
                    await pageInstance.CloseAsync();
                }

                if (browserInstance != null)
                {
                    await browserInstance.CloseAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理浏览器实例时出错: {ex.Message}");
            }
        }

        static async Task ScrapeAveSmartPage(IPage page)
        {
            if (page == null)
                throw new InvalidOperationException("浏览器未初始化");

            try
            {
                Console.WriteLine("正在访问 https://ave.ai/smart ...");

                // 设置请求拦截
                var apiResponses = new List<string>();

                await page.SetRequestInterceptionAsync(true);
                page.Request += async (sender, e) =>
                {
                    // 放行所有请求
                    await e.Request.ContinueAsync();
                };

                page.Response += async (sender, e) =>
                {
                    var response = e.Response;
                    var url = response.Url;

                    // 拦截API请求
                    if (url.Contains("/v1api/v4/tokens/kol/list") || url.Contains("/v1api/v4/tokens/smart_wallet/list"))
                    {
                        try
                        {
                            var responseText = await response.TextAsync();
                            Console.WriteLine($"拦截到API请求: {url}");
                            Console.WriteLine($"响应状态: {response.Status}");

                            if (!string.IsNullOrEmpty(responseText))
                            {
                                apiResponses.Add($"URL: {url}\nResponse: {responseText}\n{new string('=', 50)}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"读取API响应失败: {ex.Message}");
                        }
                    }
                };

                // 导航到目标页面
                var response = await page.GoToAsync("https://ave.ai/smart", new NavigationOptions
                {
                    WaitUntil = new[] { WaitUntilNavigation.Networkidle2 },
                    Timeout = 60000
                });

                if (response?.Ok != true)
                {
                    throw new Exception($"页面加载失败，状态码: {response?.Status}");
                }

                Console.WriteLine("页面加载成功，等待页面元素加载...");
                await Task.Delay(3000);

                // 按顺序点击三个按钮
                bool allButtonsClicked = false;

                // 1. 首先点击第一个按钮 (button[1])
                Console.WriteLine("正在点击第一个按钮 (button[1])...");
                if (await ClickButtonByXPath(page, "//*[@id=\"app\"]/div/div/div[2]/main/section/div[1]/button[1]",
                        "第一个按钮"))
                {
                    await Task.Delay(2000); // 等待页面响应

                    // 2. 然后点击第二个按钮 (button[2])
                    Console.WriteLine("正在点击第二个按钮 (button[2])...");
                    if (await ClickButtonByXPath(page, "//*[@id=\"app\"]/div/div/div[2]/main/section/div[1]/button[2]",
                            "第二个按钮"))
                    {
                        Console.WriteLine("第二个按钮点击成功，等待页面加载完成...");

                        // 等待页面加载完成
                        await WaitForPageLoad(page);

                        // 3. 最后点击第三个按钮 (button[3])
                        Console.WriteLine("正在点击第三个按钮 (button[3])...");
                        if (await ClickButtonByXPath(page,
                                "//*[@id=\"app\"]/div/div/div[2]/main/section/div[1]/button[3]", "第三个按钮"))
                        {
                            allButtonsClicked = true;
                        }
                    }
                }

                if (!allButtonsClicked)
                {
                    Console.WriteLine("未能成功点击所有按钮，尝试通过URL导航...");
                    // 如果找不到按钮，尝试直接导航到可能的URL
                    var possibleUrls = new[]
                    {
                        "https://ave.ai/smart?tab=kol",
                        "https://ave.ai/smart/kol",
                        "https://ave.ai/kol"
                    };

                    foreach (var url in possibleUrls)
                    {
                        try
                        {
                            Console.WriteLine($"尝试导航到: {url}");
                            await page.GoToAsync(url, new NavigationOptions
                            {
                                WaitUntil = new[] { WaitUntilNavigation.Networkidle2 },
                                Timeout = 30000
                            });
                            break;
                        }
                        catch
                        {
                            Console.WriteLine($"导航到 {url} 失败");
                        }
                    }
                }

                // 等待API请求完成
                Console.WriteLine("等待API请求完成...");
                await Task.Delay(5000);

                // 保存拦截到的API响应
                if (apiResponses.Count > 0)
                {
                    Console.WriteLine($"拦截到 {apiResponses.Count} 个API响应");

                    string txtFolder = Path.Combine(Directory.GetCurrentDirectory(), "txt");
                    string fileName = $"ave_api_responses_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                    string filePath = Path.Combine(txtFolder, fileName);

                    await File.WriteAllLinesAsync(filePath, apiResponses);

                    Console.WriteLine($"API响应数据已保存到: {filePath}");
                    Console.WriteLine($"文件大小: {new FileInfo(filePath).Length} 字节");

                    // 尝试解析JSON并提取钱包地址
                    await ExtractWalletDataFromApiResponses(apiResponses);
                }
                else
                {
                    Console.WriteLine("未拦截到API请求，保存页面截图用于调试...");

                    string imagesFolder = Path.Combine(Directory.GetCurrentDirectory(), "images");
                    string screenshotFileName = $"ave_smart_debug_{DateTime.Now:yyyyMMdd_HHmmss}.png";
                    string screenshotPath = Path.Combine(imagesFolder, screenshotFileName);

                    await page.ScreenshotAsync(screenshotPath, new ScreenshotOptions
                    {
                        FullPage = true
                    });

                    Console.WriteLine($"页面截图已保存到: {screenshotPath}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"页面抓取失败: {ex.Message}");
                throw;
            }
        }

        static async Task ExtractWalletDataFromApiResponses(List<string> apiResponses)
        {
            try
            {
                var allWalletData = new List<string>();

                foreach (var apiResponse in apiResponses)
                {
                    try
                    {
                        // 提取JSON部分
                        var jsonStart = apiResponse.IndexOf("{");
                        var jsonEnd = apiResponse.LastIndexOf("}");

                        if (jsonStart >= 0 && jsonEnd > jsonStart)
                        {
                            var jsonContent = apiResponse.Substring(jsonStart, jsonEnd - jsonStart + 1);
                            var jsonDocument = JsonDocument.Parse(jsonContent);

                            // 尝试提取钱包数据
                            if (jsonDocument.RootElement.TryGetProperty("data", out var dataProperty))
                            {
                                JsonElement dataArray;

                                // 处理不同的数据结构
                                if (dataProperty.ValueKind == JsonValueKind.Array)
                                {
                                    dataArray = dataProperty;
                                }
                                else if (dataProperty.TryGetProperty("rank", out var rankProperty))
                                {
                                    dataArray = rankProperty;
                                }
                                else
                                {
                                    continue;
                                }

                                foreach (var item in dataArray.EnumerateArray())
                                {
                                    if (item.TryGetProperty("wallet_address", out var walletProperty))
                                    {
                                        var walletAddress = walletProperty.GetString() ?? "";
                                        var remark = "";
                                        var name = "";

                                        // 尝试获取remark或name
                                        if (item.TryGetProperty("remark", out var remarkProperty))
                                        {
                                            remark = remarkProperty.GetString() ?? "";
                                        }

                                        if (item.TryGetProperty("name", out var nameProperty) &&
                                            nameProperty.ValueKind != JsonValueKind.Null)
                                        {
                                            name = nameProperty.GetString() ?? "";
                                        }

                                        var displayName = !string.IsNullOrEmpty(name) ? name : remark;
                                        allWalletData.Add($"{walletAddress} {displayName}");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"解析API响应失败: {ex.Message}");
                    }
                }

                if (allWalletData.Count > 0)
                {
                    // 去重
                    var uniqueWalletData = allWalletData.Distinct().ToList();

                    string txtFolder = Path.Combine(Directory.GetCurrentDirectory(), "txt");
                    string fileName = $"extracted_wallet_data_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                    string filePath = Path.Combine(txtFolder, fileName);

                    await File.WriteAllLinesAsync(filePath, uniqueWalletData);

                    Console.WriteLine($"提取的钱包数据已保存到: {filePath}");
                    Console.WriteLine($"钱包地址数量: {uniqueWalletData.Count}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"提取钱包数据失败: {ex.Message}");
            }
        }

        static async Task<bool> ClickButtonByXPath(IPage page, string xpath, string buttonName)
        {
            if (page == null)
                throw new InvalidOperationException("浏览器未初始化");

            try
            {
                Console.WriteLine($"尝试点击{buttonName}，XPath: {xpath}");

                // 等待按钮出现
                await page.WaitForXPathAsync(xpath, new WaitForSelectorOptions
                {
                    Timeout = 10000
                });

                // 获取按钮元素
                var elements = await page.XPathAsync(xpath);

                if (elements.Length > 0)
                {
                    var button = elements[0];

                    // 滚动到元素位置（确保可见）
                    await page.EvaluateFunctionAsync("element => element.scrollIntoView()", button);

                    // 等待一下确保元素可点击
                    await Task.Delay(1000);

                    // 点击按钮
                    await button.ClickAsync();

                    Console.WriteLine($"成功点击{buttonName}");
                    return true;
                }
                else
                {
                    Console.WriteLine($"{buttonName}的XPath未找到对应元素");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"点击{buttonName}失败: {ex.Message}");

                // 备用方案：尝试其他可能的XPath
                var buttonNumber = xpath.Contains("button[1]") ? "1" :
                    xpath.Contains("button[2]") ? "2" :
                    xpath.Contains("button[3]") ? "3" : "unknown";

                var backupXPaths = new[]
                {
                    $"//*[@id='app']//button[{buttonNumber}]",
                    $"//section//div[1]//button[{buttonNumber}]",
                    $"//div[contains(@class, 'tab')]//button[{buttonNumber}]",
                    $"//button[{buttonNumber}]"
                };

                foreach (var backupXPath in backupXPaths)
                {
                    try
                    {
                        Console.WriteLine($"尝试{buttonName}备用XPath: {backupXPath}");

                        var elements = await page.XPathAsync(backupXPath);
                        if (elements.Length > 0)
                        {
                            await elements[0].ClickAsync();
                            Console.WriteLine($"成功使用备用XPath点击{buttonName}: {backupXPath}");
                            return true;
                        }
                    }
                    catch (Exception backupEx)
                    {
                        Console.WriteLine($"{buttonName}备用XPath {backupXPath} 失败: {backupEx.Message}");
                    }
                }

                return false;
            }
        }

        static async Task WaitForPageLoad(IPage page)
        {
            if (page == null)
                throw new InvalidOperationException("浏览器未初始化");

            try
            {
                Console.WriteLine("等待网络请求完成...");

                // 使用标准的延迟方法等待
                try
                {
                    await Task.Delay(2000); // 先等待2秒

                    // 等待没有网络请求活动
                    await page.WaitForFunctionAsync(@"
                        () => {
                            return window.performance &&
                                   window.performance.getEntriesByType &&
                                   window.performance.getEntriesByType('navigation').length > 0;
                        }
                    ", new WaitForFunctionOptions { Timeout = 5000 });
                }
                catch
                {
                    Console.WriteLine("网络等待超时，继续执行");
                }

                Console.WriteLine("网络请求完成，额外等待页面渲染...");

                // 额外等待，确保页面完全渲染
                await Task.Delay(3000);

                // 尝试等待可能的加载指示器消失
                try
                {
                    // 等待常见的加载指示器消失
                    await page.WaitForFunctionAsync(@"
                        () => {
                            // 检查是否有加载指示器
                            const loadingElements = document.querySelectorAll('[class*=""loading""], [class*=""spinner""], [class*=""loader""]');
                            const isLoading = Array.from(loadingElements).some(el =>
                                el.offsetParent !== null &&
                                getComputedStyle(el).display !== 'none'
                            );
                            return !isLoading;
                        }
                    ", new WaitForFunctionOptions
                    {
                        Timeout = 5000
                    });
                    Console.WriteLine("加载指示器已消失");
                }
                catch
                {
                    Console.WriteLine("未检测到加载指示器或等待超时，继续执行");
                }

                Console.WriteLine("页面加载等待完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"等待页面加载时出错: {ex.Message}，继续执行");
            }
        }

        static async Task ScrapeGmgnPage(IPage page)
        {
            if (page == null)
                throw new InvalidOperationException("浏览器未初始化");

            try
            {
                // 添加反检测代码
                const string js = @"Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });";

                await page.EvaluateExpressionAsync(js);
                Console.WriteLine("已添加反检测代码");

                // 设置请求拦截 - 用于拦截GMGN API
                var smartDegenApiResponses = new List<string>();
                var renownedApiResponses = new List<string>();

                await page.SetRequestInterceptionAsync(true);
                page.Request += async (sender, e) =>
                {
                    // 放行所有请求
                    await e.Request.ContinueAsync();
                };

                page.Response += async (sender, e) =>
                {
                    var response = e.Response;
                    var url = response.Url;

                    // 拦截GMGN API请求（相同URL，不同参数）
                    if (url.Contains("/defi/quotation/v1/rank/sol/wallets/"))
                    {
                        try
                        {
                            var responseText = await response.TextAsync();
                            Console.WriteLine($"拦截到GMGN API请求: {url}");
                            Console.WriteLine($"响应状态: {response.Status}");

                            if (!string.IsNullOrEmpty(responseText))
                            {
                                // 根据URL参数判断数据类型
                                if (url.Contains("tag=smart_degen"))
                                {
                                    smartDegenApiResponses.Add(
                                        $"URL: {url}\nResponse: {responseText}\n{new string('=', 50)}");
                                    Console.WriteLine("保存为Smart Degen数据");
                                }
                                else if (url.Contains("tag=renowned"))
                                {
                                    renownedApiResponses.Add(
                                        $"URL: {url}\nResponse: {responseText}\n{new string('=', 50)}");
                                    Console.WriteLine("保存为Renowned数据");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"读取GMGN API响应失败: {ex.Message}");
                        }
                    }
                };

                // 1. 访问Smart Degen页面
                Console.WriteLine("正在访问 Smart Degen 页面...");
                var smartDegenResponse = await page.GoToAsync("https://gmgn.ai/trade/solscan?chain=sol&tab=smart_degen",
                    new NavigationOptions
                    {
                        WaitUntil = new[] { WaitUntilNavigation.Networkidle2 },
                        Timeout = 60000
                    });

                if (smartDegenResponse?.Ok != true)
                {
                    throw new Exception($"GMGN Smart Degen页面加载失败，状态码: {smartDegenResponse?.Status}");
                }

                Console.WriteLine("Smart Degen页面加载成功，等待API请求完成...");
                //await Task.Delay(5000); // 等待API请求完成
                await WaitForPageLoad(page);


                // 2. 访问Renowned页面
                Console.WriteLine("正在访问 Renowned 页面...");
                var renownedResponse = await page.GoToAsync("https://gmgn.ai/trade/solscan?chain=sol&tab=renowned",
                    new NavigationOptions
                    {
                        WaitUntil = new[] { WaitUntilNavigation.Networkidle2 },
                        Timeout = 60000
                    });

                if (renownedResponse?.Ok != true)
                {
                    throw new Exception($"GMGN Renowned页面加载失败，状态码: {renownedResponse?.Status}");
                }

                Console.WriteLine("Renowned页面加载成功，等待API请求完成...");
                await Task.Delay(5000); // 等待API请求完成

                // 处理拦截到的API响应，只保存处理结果
                bool hasApiData = false;

                // 处理Smart Degen API响应
                if (smartDegenApiResponses.Count > 0)
                {
                    Console.WriteLine($"拦截到 {smartDegenApiResponses.Count} 个Smart Degen API响应，正在提取钱包数据...");
                    await ExtractGmgnWalletDataFromApiResponses(smartDegenApiResponses, "gmgn_smart_degen");
                    hasApiData = true;
                }

                // 处理Renowned API响应
                if (renownedApiResponses.Count > 0)
                {
                    Console.WriteLine($"拦截到 {renownedApiResponses.Count} 个Renowned API响应，正在提取钱包数据...");
                    await ExtractGmgnWalletDataFromApiResponses(renownedApiResponses, "gmgn_renowned");
                    hasApiData = true;
                }

                // 如果没有拦截到任何API数据，保存截图
                if (!hasApiData)
                {
                    Console.WriteLine("未拦截到任何GMGN API请求，保存页面截图用于调试...");

                    string imagesFolder = Path.Combine(Directory.GetCurrentDirectory(), "images");
                    string screenshotFileName = $"gmgn_debug_{DateTime.Now:yyyyMMdd_HHmmss}.png";
                    string screenshotPath = Path.Combine(imagesFolder, screenshotFileName);

                    await page.ScreenshotAsync(screenshotPath, new ScreenshotOptions
                    {
                        FullPage = true
                    });

                    Console.WriteLine($"GMGN页面截图已保存到: {screenshotPath}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GMGN页面抓取失败: {ex.Message}");
                throw;
            }
        }

        static async Task ExtractGmgnWalletDataFromApiResponses(List<string> apiResponses, string dataType)
        {
            try
            {
                var allWalletData = new List<string>();

                foreach (var apiResponse in apiResponses)
                {
                    try
                    {
                        // 提取JSON部分
                        var jsonStart = apiResponse.IndexOf("{");
                        var jsonEnd = apiResponse.LastIndexOf("}");

                        if (jsonStart >= 0 && jsonEnd > jsonStart)
                        {
                            var jsonContent = apiResponse.Substring(jsonStart, jsonEnd - jsonStart + 1);
                            var jsonDocument = JsonDocument.Parse(jsonContent);

                            // GMGN API的数据结构：data.rank
                            if (jsonDocument.RootElement.TryGetProperty("data", out var dataProperty) &&
                                dataProperty.TryGetProperty("rank", out var rankProperty))
                            {
                                foreach (var item in rankProperty.EnumerateArray())
                                {
                                    if (item.TryGetProperty("wallet_address", out var walletProperty))
                                    {
                                        var walletAddress = walletProperty.GetString() ?? "";
                                        var name = "";

                                        // 尝试获取name
                                        if (item.TryGetProperty("name", out var nameProperty) &&
                                            nameProperty.ValueKind != JsonValueKind.Null)
                                        {
                                            name = nameProperty.GetString() ?? "";
                                        }

                                        allWalletData.Add($"{walletAddress} {name}");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"解析GMGN API响应失败: {ex.Message}");
                    }
                }

                if (allWalletData.Count > 0)
                {
                    // 去重
                    var uniqueWalletData = allWalletData.Distinct().ToList();

                    string txtFolder = Path.Combine(Directory.GetCurrentDirectory(), "txt");
                    string fileName = $"extracted_{dataType}_wallet_data_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                    string filePath = Path.Combine(txtFolder, fileName);

                    await File.WriteAllLinesAsync(filePath, uniqueWalletData);

                    Console.WriteLine($"提取的{dataType}钱包数据已保存到: {filePath}");
                    Console.WriteLine($"{dataType}钱包地址数量: {uniqueWalletData.Count}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"提取GMGN钱包数据失败: {ex.Message}");
            }
        }
    }
}