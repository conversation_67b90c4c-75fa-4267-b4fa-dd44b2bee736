using Flurl.Http;
using System.Text.Json;
using Flurl;

namespace SmartConsoleApp
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("开始请求API数据...");

            try
            {
                // 请求Smart Wallet数据
                await RequestSmartWalletData();

                Console.WriteLine();

                // 请求KOL数据
                await RequestKolData();

                Console.WriteLine();

                // 请求GMGN KOL数据
                await RequestGmgnKolData();
            }
            catch (FlurlHttpException ex)
            {
                Console.WriteLine($"HTTP请求错误: {ex.Message}");
                if (ex.Call?.Response != null)
                {
                    Console.WriteLine($"状态码: {ex.Call.Response.StatusCode}");
                    Console.WriteLine($"响应内容: {await ex.GetResponseStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        static async Task RequestSmartWalletData()
        {
            try
            {
                Console.WriteLine("正在请求Smart Wallet数据...");

                // API请求地址
                string apiUrl = "https://api.gejbckf.com/v1api/v4/tokens/smart_wallet/list";

                // 发送GET请求并获取响应
                var response = await apiUrl
                    .SetQueryParams(new
                    {
                        chain = "solana",
                        sort = "rank_score",
                        sort_dir = "desc",
                        interval = "7D"
                    })
                    .WithHeader("x-auth", "98db851d5ffc1ca19b3dcfe126aeb8e11751083955929493582")
                    .WithHeader("User-Agent", "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)")
                    .WithHeader("Origin", "https://ave.ai")
                    .WithHeader("Referer", "https://ave.ai/")
                    .GetStringAsync();

                Console.WriteLine("Smart Wallet API请求成功，正在解析数据...");

                // 解析JSON响应
                var jsonDocument = JsonDocument.Parse(response);
                var data = jsonDocument.RootElement.GetProperty("data");

                // 提取wallet_address和remark字段
                var extractedData = new List<string>();

                foreach (var item in data.EnumerateArray())
                {
                    var walletAddress = item.GetProperty("wallet_address").GetString() ?? "";
                    var remark = item.GetProperty("remark").GetString() ?? "";
                    extractedData.Add($"{walletAddress} {remark}");
                }

                // 生成文件名（包含时间戳）
                string fileName = $"smart_wallet_data_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                string filePath = Path.Combine(Directory.GetCurrentDirectory(), fileName);

                // 将结果写入txt文件
                await File.WriteAllLinesAsync(filePath, extractedData);

                Console.WriteLine($"Smart Wallet数据已成功保存到: {filePath}");
                Console.WriteLine($"文件大小: {new FileInfo(filePath).Length} 字节");
                Console.WriteLine($"记录数量: {extractedData.Count}");
            }
            catch (FlurlHttpException ex)
            {
                Console.WriteLine($"Smart Wallet HTTP请求错误: {ex.Message}");
                if (ex.Call?.Response != null)
                {
                    Console.WriteLine($"状态码: {ex.Call.Response.StatusCode}");
                    Console.WriteLine($"响应内容: {await ex.GetResponseStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Smart Wallet请求发生错误: {ex.Message}");
            }
        }

        static async Task RequestKolData()
        {
            try
            {
                Console.WriteLine("正在请求KOL数据...");

                // KOL API请求地址
                string apiUrl = "https://mayeas023.com/v1api/v4/tokens/kol/list";

                // 发送GET请求并获取响应
                var response = await apiUrl
                    .SetQueryParams(new
                    {
                        chain = "solana",
                        sort = "rank_score",
                        sort_dir = "desc",
                        interval = "7D"
                    })
                    .WithHeader("x-auth", "98db851d5ffc1ca19b3dcfe126aeb8e11751083955929493582")
                    .WithHeader("User-Agent", "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)")
                    .WithHeader("Origin", "https://ave.ai")
                    .WithHeader("Referer", "https://ave.ai/")
                    .GetStringAsync();

                Console.WriteLine("KOL API请求成功，正在解析数据...");

                // 解析JSON响应
                var jsonDocument = JsonDocument.Parse(response);
                var data = jsonDocument.RootElement.GetProperty("data");

                // 提取wallet_address和remark字段
                var extractedData = new List<string>();

                foreach (var item in data.EnumerateArray())
                {
                    var walletAddress = item.GetProperty("wallet_address").GetString() ?? "";
                    var remark = item.GetProperty("remark").GetString() ?? "";
                    extractedData.Add($"{walletAddress} {remark}");
                }

                // 生成文件名（包含时间戳）
                string fileName = $"kol_data_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                string filePath = Path.Combine(Directory.GetCurrentDirectory(), fileName);

                // 将结果写入txt文件
                await File.WriteAllLinesAsync(filePath, extractedData);

                Console.WriteLine($"KOL数据已成功保存到: {filePath}");
                Console.WriteLine($"文件大小: {new FileInfo(filePath).Length} 字节");
                Console.WriteLine($"记录数量: {extractedData.Count}");
            }
            catch (FlurlHttpException ex)
            {
                Console.WriteLine($"KOL HTTP请求错误: {ex.Message}");
                if (ex.Call?.Response != null)
                {
                    Console.WriteLine($"状态码: {ex.Call.Response.StatusCode}");
                    Console.WriteLine($"响应内容: {await ex.GetResponseStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"KOL请求发生错误: {ex.Message}");
            }
        }

        static async Task RequestGmgnKolData()
        {
            try
            {
                Console.WriteLine("正在请求GMGN KOL数据...");

                // GMGN API请求地址
                string apiUrl = "https://gmgn.ai/defi/quotation/v1/rank/sol/wallets/7d";

                // 发送GET请求并获取响应
                var response = await apiUrl
                    .SetQueryParams(new
                    {
                        tag = new[] { "smart_degen", "pump_smart" },
                        device_id = "cc9961c7-3456-47e2-8a88-01297344b209",
                        client_id = "gmgn_web_20250627-482-8dff1e2",
                        from_app = "gmgn",
                        app_ver = "20250627-482-8dff1e2",
                        tz_name = "Asia/Shanghai",
                        tz_offset = "28800",
                        app_lang = "zh-CN",
                        fp_did = "c75d3716917038cfc33dbeb6f3fae35c",
                        os = "web",
                        orderby = "pnl_7d",
                        direction = "desc"
                    })
                    .WithHeader("User-Agent", "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)")
                    .WithHeader("Origin", "https://gmgn.ai")
                    .WithHeader("Referer", "https://gmgn.ai/trade/solscan?chain=sol&tab=smart_degen")
                    .GetStringAsync();

                Console.WriteLine("GMGN KOL API请求成功，正在解析数据...");

                // 解析JSON响应
                var jsonDocument = JsonDocument.Parse(response);
                var data = jsonDocument.RootElement.GetProperty("data").GetProperty("rank");

                // 提取wallet_address和name字段
                var extractedData = new List<string>();

                foreach (var item in data.EnumerateArray())
                {
                    var walletAddress = item.GetProperty("wallet_address").GetString() ?? "";
                    var name = "";

                    // name字段可能为null，需要安全处理
                    if (item.TryGetProperty("name", out var nameProperty) && nameProperty.ValueKind != JsonValueKind.Null)
                    {
                        name = nameProperty.GetString() ?? "";
                    }

                    extractedData.Add($"{walletAddress} {name}");
                }

                // 生成文件名（包含时间戳）
                string fileName = $"gmgn_kol_data_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                string filePath = Path.Combine(Directory.GetCurrentDirectory(), fileName);

                // 将结果写入txt文件
                await File.WriteAllLinesAsync(filePath, extractedData);

                Console.WriteLine($"GMGN KOL数据已成功保存到: {filePath}");
                Console.WriteLine($"文件大小: {new FileInfo(filePath).Length} 字节");
                Console.WriteLine($"记录数量: {extractedData.Count}");
            }
            catch (FlurlHttpException ex)
            {
                Console.WriteLine($"GMGN KOL HTTP请求错误: {ex.Message}");
                if (ex.Call?.Response != null)
                {
                    Console.WriteLine($"状态码: {ex.Call.Response.StatusCode}");
                    Console.WriteLine($"响应内容: {await ex.GetResponseStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GMGN KOL请求发生错误: {ex.Message}");
            }
        }
    }
}