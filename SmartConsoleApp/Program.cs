using Flurl.Http;
using System.Text.Json;
using Flurl;

namespace SmartConsoleApp
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("开始请求API数据...");

            try
            {
                // API请求地址
                string apiUrl = "https://api.gejbckf.com/v1api/v4/tokens/smart_wallet/list";

                // 发送GET请求并获取响应
                var response = await apiUrl
                    .SetQueryParams(new
                    {
                        chain = "solana",
                        sort = "rank_score",
                        sort_dir = "desc",
                        interval = "7D"
                    })
                    .WithHeader("x-auth", "98db851d5ffc1ca19b3dcfe126aeb8e11751083955929493582")
                    .WithHeader("User-Agent", "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)")
                    .WithHeader("Origin", "https://ave.ai")
                    .WithHeader("Referer", "https://ave.ai/")
                    .GetStringAsync();

                Console.WriteLine("API请求成功，正在保存到文件...");

                // 格式化JSON响应（可选，让输出更美观）
                var jsonDocument = JsonDocument.Parse(response);
                var formattedJson = JsonSerializer.Serialize(jsonDocument, new JsonSerializerOptions
                {
                    WriteIndented = true
                });

                // 生成文件名（包含时间戳）
                string fileName = $"smart_wallet_tokens_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                string filePath = Path.Combine(Directory.GetCurrentDirectory(), fileName);

                // 将结果写入txt文件
                await File.WriteAllTextAsync(filePath, formattedJson);

                Console.WriteLine($"数据已成功保存到: {filePath}");
                Console.WriteLine($"文件大小: {new FileInfo(filePath).Length} 字节");
            }
            catch (FlurlHttpException ex)
            {
                Console.WriteLine($"HTTP请求错误: {ex.Message}");
                if (ex.Call?.Response != null)
                {
                    Console.WriteLine($"状态码: {ex.Call.Response.StatusCode}");
                    Console.WriteLine($"响应内容: {await ex.GetResponseStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}