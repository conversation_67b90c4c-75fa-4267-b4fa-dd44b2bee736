using System.Text.Json;
using PuppeteerSharp;

namespace SmartConsoleApp
{
    class Program
    {
        private static IBrowser? browser;
        private static IPage? page;
        static async Task Main(string[] args)
        {
            Console.WriteLine("开始初始化程序...");

            // 创建必要的文件夹
            CreateDirectories();

            Console.WriteLine("开始初始化浏览器...");

            try
            {
                // 初始化浏览器
                await InitializeBrowser();

                Console.WriteLine("浏览器初始化完成，开始访问网页...");

                // 访问ave.ai/smart页面
                await ScrapeAveSmartPage();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
            }
            finally
            {
                // 清理浏览器资源
                await CleanupBrowser();
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        static void CreateDirectories()
        {
            try
            {
                // 创建txt文件夹
                string txtFolder = Path.Combine(Directory.GetCurrentDirectory(), "txt");
                Directory.CreateDirectory(txtFolder);
                Console.WriteLine($"确保txt文件夹存在: {txtFolder}");

                // 创建images文件夹
                string imagesFolder = Path.Combine(Directory.GetCurrentDirectory(), "images");
                Directory.CreateDirectory(imagesFolder);
                Console.WriteLine($"确保images文件夹存在: {imagesFolder}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建文件夹失败: {ex.Message}");
            }
        }

        static async Task InitializeBrowser()
        {
            // 下载Chromium（如果需要）
            await new BrowserFetcher().DownloadAsync();

            // 启动浏览器
            browser = await Puppeteer.LaunchAsync(new LaunchOptions
            {
                Headless = true,
                Args = new[]
                {
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-accelerated-2d-canvas",
                    "--no-first-run",
                    "--no-zygote",
                    "--disable-gpu"
                }
            });

            // 创建新页面
            page = await browser.NewPageAsync();

            // 设置用户代理
            await page.SetUserAgentAsync("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        }

        static async Task CleanupBrowser()
        {
            if (page != null)
            {
                await page.CloseAsync();
            }

            if (browser != null)
            {
                await browser.CloseAsync();
            }
        }

        static async Task ScrapeAveSmartPage()
        {
            if (page == null)
                throw new InvalidOperationException("浏览器未初始化");

            try
            {
                Console.WriteLine("正在访问 https://ave.ai/smart ...");

                // 设置请求拦截
                var apiResponses = new List<string>();

                await page.SetRequestInterceptionAsync(true);
                page.Request += async (sender, e) =>
                {
                    // 放行所有请求
                    await e.Request.ContinueAsync();
                };

                page.Response += async (sender, e) =>
                {
                    var response = e.Response;
                    var url = response.Url;

                    // 拦截API请求
                    if (url.Contains("/v1api/v4/tokens/kol/") || url.Contains("//v1api/v4/tokens/smart_wallet//") )
                    {
                        try
                        {
                            var responseText = await response.TextAsync();
                            Console.WriteLine($"拦截到API请求: {url}");
                            Console.WriteLine($"响应状态: {response.Status}");

                            if (!string.IsNullOrEmpty(responseText))
                            {
                                apiResponses.Add($"URL: {url}\nResponse: {responseText}\n{new string('=', 50)}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"读取API响应失败: {ex.Message}");
                        }
                    }
                };

                // 导航到目标页面
                var response = await page.GoToAsync("https://ave.ai/smart", new NavigationOptions
                {
                    WaitUntil = new[] { WaitUntilNavigation.Networkidle2 },
                    Timeout = 60000
                });

                if (response?.Ok != true)
                {
                    throw new Exception($"页面加载失败，状态码: {response?.Status}");
                }

                Console.WriteLine("页面加载成功，等待页面元素加载...");
                await Task.Delay(3000);

                // 使用XPath直接点击"牛人榜"按钮
                Console.WriteLine("正在使用XPath查找'牛人榜'按钮...");

                bool buttonClicked = false;

                try
                {
                    // 使用您提供的XPath
                    string xpath = "//*[@id=\"app\"]/div/div/div[2]/main/section/div[1]/button[2]";

                    Console.WriteLine($"尝试XPath: {xpath}");

                    // 等待元素出现
                    await page.WaitForXPathAsync(xpath, new WaitForSelectorOptions
                    {
                        Timeout = 10000
                    });

                    // 获取元素
                    var elements = await page.XPathAsync(xpath);

                    if (elements.Length > 0)
                    {
                        var button = elements[0];

                        // 滚动到元素位置（确保可见）
                        await page.EvaluateFunctionAsync("element => element.scrollIntoView()", button);

                        // 等待一下确保元素可点击
                        await Task.Delay(1000);

                        // 点击按钮
                        await button.ClickAsync();

                        Console.WriteLine("成功使用XPath点击第一个按钮（牛人榜）");
                        buttonClicked = true;

                        // 等待页面响应第一个按钮点击
                        await Task.Delay(2000);

                        // 点击第二个按钮
                        await ClickSecondButton();
                    }
                    else
                    {
                        Console.WriteLine("XPath未找到对应元素");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"XPath点击失败: {ex.Message}");

                    // 备用方案：尝试其他可能的XPath
                    var backupXPaths = new[]
                    {
                        "//button[contains(text(), 'Top Traders')]",
                        "//button[contains(text(), 'KOL')]",
                        "//button[contains(text(), '牛人榜')]",
                        "//*[@id='app']//button[2]",
                        "//div[contains(@class, 'tab')]//button[2]",
                        "//section//div[1]//button[2]"
                    };

                    foreach (var backupXPath in backupXPaths)
                    {
                        try
                        {
                            Console.WriteLine($"尝试备用XPath: {backupXPath}");

                            var elements = await page.XPathAsync(backupXPath);
                            if (elements.Length > 0)
                            {
                                await elements[0].ClickAsync();
                                Console.WriteLine($"成功使用备用XPath点击第一个按钮: {backupXPath}");
                                buttonClicked = true;

                                // 等待页面响应
                                await Task.Delay(2000);

                                // 点击第二个按钮
                                await ClickSecondButton();
                                break;
                            }
                        }
                        catch (Exception backupEx)
                        {
                            Console.WriteLine($"备用XPath {backupXPath} 失败: {backupEx.Message}");
                        }
                    }
                }

                if (!buttonClicked)
                {
                    Console.WriteLine("未找到'牛人榜'按钮，尝试通过URL导航...");
                    // 如果找不到按钮，尝试直接导航到可能的URL
                    var possibleUrls = new[]
                    {
                        "https://ave.ai/smart?tab=kol",
                        "https://ave.ai/smart/kol",
                        "https://ave.ai/kol"
                    };

                    foreach (var url in possibleUrls)
                    {
                        try
                        {
                            Console.WriteLine($"尝试导航到: {url}");
                            await page.GoToAsync(url, new NavigationOptions
                            {
                                WaitUntil = new[] { WaitUntilNavigation.Networkidle2 },
                                Timeout = 30000
                            });
                            break;
                        }
                        catch
                        {
                            Console.WriteLine($"导航到 {url} 失败");
                        }
                    }
                }

                // 等待API请求完成
                Console.WriteLine("等待API请求完成...");
                await Task.Delay(5000);

                // 保存拦截到的API响应
                if (apiResponses.Count > 0)
                {
                    Console.WriteLine($"拦截到 {apiResponses.Count} 个API响应");

                    string txtFolder = Path.Combine(Directory.GetCurrentDirectory(), "txt");
                    string fileName = $"ave_api_responses_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                    string filePath = Path.Combine(txtFolder, fileName);

                    await File.WriteAllLinesAsync(filePath, apiResponses);

                    Console.WriteLine($"API响应数据已保存到: {filePath}");
                    Console.WriteLine($"文件大小: {new FileInfo(filePath).Length} 字节");

                    // 尝试解析JSON并提取钱包地址
                    await ExtractWalletDataFromApiResponses(apiResponses);
                }
                else
                {
                    Console.WriteLine("未拦截到API请求，保存页面截图用于调试...");

                    string imagesFolder = Path.Combine(Directory.GetCurrentDirectory(), "images");
                    string screenshotFileName = $"ave_smart_debug_{DateTime.Now:yyyyMMdd_HHmmss}.png";
                    string screenshotPath = Path.Combine(imagesFolder, screenshotFileName);

                    await page.ScreenshotAsync(screenshotPath, new ScreenshotOptions
                    {
                        FullPage = true
                    });

                    Console.WriteLine($"页面截图已保存到: {screenshotPath}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"页面抓取失败: {ex.Message}");
                throw;
            }
        }

        static async Task ExtractWalletDataFromApiResponses(List<string> apiResponses)
        {
            try
            {
                var allWalletData = new List<string>();

                foreach (var apiResponse in apiResponses)
                {
                    try
                    {
                        // 提取JSON部分
                        var jsonStart = apiResponse.IndexOf("{");
                        var jsonEnd = apiResponse.LastIndexOf("}");

                        if (jsonStart >= 0 && jsonEnd > jsonStart)
                        {
                            var jsonContent = apiResponse.Substring(jsonStart, jsonEnd - jsonStart + 1);
                            var jsonDocument = JsonDocument.Parse(jsonContent);

                            // 尝试提取钱包数据
                            if (jsonDocument.RootElement.TryGetProperty("data", out var dataProperty))
                            {
                                JsonElement dataArray;

                                // 处理不同的数据结构
                                if (dataProperty.ValueKind == JsonValueKind.Array)
                                {
                                    dataArray = dataProperty;
                                }
                                else if (dataProperty.TryGetProperty("rank", out var rankProperty))
                                {
                                    dataArray = rankProperty;
                                }
                                else
                                {
                                    continue;
                                }

                                foreach (var item in dataArray.EnumerateArray())
                                {
                                    if (item.TryGetProperty("wallet_address", out var walletProperty))
                                    {
                                        var walletAddress = walletProperty.GetString() ?? "";
                                        var remark = "";
                                        var name = "";

                                        // 尝试获取remark或name
                                        if (item.TryGetProperty("remark", out var remarkProperty))
                                        {
                                            remark = remarkProperty.GetString() ?? "";
                                        }

                                        if (item.TryGetProperty("name", out var nameProperty) && nameProperty.ValueKind != JsonValueKind.Null)
                                        {
                                            name = nameProperty.GetString() ?? "";
                                        }

                                        var displayName = !string.IsNullOrEmpty(name) ? name : remark;
                                        allWalletData.Add($"{walletAddress} {displayName}");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"解析API响应失败: {ex.Message}");
                    }
                }

                if (allWalletData.Count > 0)
                {
                    // 去重
                    var uniqueWalletData = allWalletData.Distinct().ToList();

                    string txtFolder = Path.Combine(Directory.GetCurrentDirectory(), "txt");
                    string fileName = $"extracted_wallet_data_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                    string filePath = Path.Combine(txtFolder, fileName);

                    await File.WriteAllLinesAsync(filePath, uniqueWalletData);

                    Console.WriteLine($"提取的钱包数据已保存到: {filePath}");
                    Console.WriteLine($"钱包地址数量: {uniqueWalletData.Count}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"提取钱包数据失败: {ex.Message}");
            }
        }

        static async Task ClickSecondButton()
        {
            if (page == null)
                throw new InvalidOperationException("浏览器未初始化");

            try
            {
                Console.WriteLine("正在点击第二个按钮...");

                // 第二个按钮的XPath
                string secondButtonXPath = "//*[@id=\"app\"]/div/div/div[2]/main/section/div[1]/button[3]";

                Console.WriteLine($"尝试第二个按钮XPath: {secondButtonXPath}");

                // 等待第二个按钮出现
                await page.WaitForXPathAsync(secondButtonXPath, new WaitForSelectorOptions
                {
                    Timeout = 10000
                });

                // 获取第二个按钮元素
                var elements = await page.XPathAsync(secondButtonXPath);

                if (elements.Length > 0)
                {
                    var button = elements[0];

                    // 滚动到元素位置（确保可见）
                    await page.EvaluateFunctionAsync("element => element.scrollIntoView()", button);

                    // 等待一下确保元素可点击
                    await Task.Delay(1000);

                    // 点击第二个按钮
                    await button.ClickAsync();

                    Console.WriteLine("成功点击第二个按钮");
                }
                else
                {
                    Console.WriteLine("第二个按钮XPath未找到对应元素");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"点击第二个按钮失败: {ex.Message}");

                // 备用方案：尝试其他可能的第二个按钮XPath
                var backupXPaths = new[]
                {
                    "//*[@id='app']//button[3]",
                    "//section//div[1]//button[3]",
                    "//div[contains(@class, 'tab')]//button[3]",
                    "//button[3]"
                };

                foreach (var backupXPath in backupXPaths)
                {
                    try
                    {
                        Console.WriteLine($"尝试第二个按钮备用XPath: {backupXPath}");

                        var elements = await page.XPathAsync(backupXPath);
                        if (elements.Length > 0)
                        {
                            await elements[0].ClickAsync();
                            Console.WriteLine($"成功使用备用XPath点击第二个按钮: {backupXPath}");
                            break;
                        }
                    }
                    catch (Exception backupEx)
                    {
                        Console.WriteLine($"第二个按钮备用XPath {backupXPath} 失败: {backupEx.Message}");
                    }
                }
            }
        }
    }
}