using System.Text.Json;
using PuppeteerSharp;

namespace SmartConsoleApp
{
    class Program
    {
        private static IBrowser? browser;
        private static IPage? page;
        static async Task Main(string[] args)
        {
            Console.WriteLine("开始初始化浏览器...");

            try
            {
                // 初始化浏览器
                await InitializeBrowser();

                Console.WriteLine("浏览器初始化完成，开始访问网页...");

                // 访问ave.ai/smart页面
                await ScrapeAveSmartPage();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
            }
            finally
            {
                // 清理浏览器资源
                await CleanupBrowser();
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        static async Task InitializeBrowser()
        {
            // 下载Chromium（如果需要）
            await new BrowserFetcher().DownloadAsync();

            // 启动浏览器
            browser = await Puppeteer.LaunchAsync(new LaunchOptions
            {
                Headless = true,
                Args = new[]
                {
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-accelerated-2d-canvas",
                    "--no-first-run",
                    "--no-zygote",
                    "--disable-gpu"
                }
            });

            // 创建新页面
            page = await browser.NewPageAsync();

            // 设置用户代理
            await page.SetUserAgentAsync("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        }

        static async Task CleanupBrowser()
        {
            if (page != null)
            {
                await page.CloseAsync();
            }

            if (browser != null)
            {
                await browser.CloseAsync();
            }
        }

        static async Task ScrapeAveSmartPage()
        {
            if (page == null)
                throw new InvalidOperationException("浏览器未初始化");

            try
            {
                Console.WriteLine("正在访问 https://ave.ai/smart ...");

                // 导航到目标页面
                var response = await page.GoToAsync("https://ave.ai/smart", new NavigationOptions
                {
                    WaitUntil = new[] { WaitUntilNavigation.Networkidle2 },
                    Timeout = 60000
                });

                if (response?.Ok != true)
                {
                    throw new Exception($"页面加载失败，状态码: {response?.Status}");
                }

                Console.WriteLine("页面加载成功，等待数据加载...");

                // 等待页面完全加载，可能需要等待AJAX请求完成
                await Task.Delay(5000);

                // 尝试等待表格或数据容器加载
                try
                {
                    await page.WaitForSelectorAsync("table, .table, [class*='table'], [class*='list']", new WaitForSelectorOptions
                    {
                        Timeout = 10000
                    });
                }
                catch
                {
                    Console.WriteLine("未找到表格元素，继续尝试提取数据...");
                }

                Console.WriteLine("开始提取钱包地址数据...");

                // 提取钱包地址数据
                var walletData = await page.EvaluateFunctionAsync<string[]>(@"
                    () => {
                        const results = [];

                        // 尝试多种选择器来查找钱包地址
                        const selectors = [
                            'td', 'div', 'span', 'p', 'a'
                        ];

                        for (const selector of selectors) {
                            const elements = document.querySelectorAll(selector);
                            for (const element of elements) {
                                const text = element.textContent || element.innerText || '';
                                // 匹配Solana钱包地址格式（Base58编码，通常44个字符）
                                const walletMatch = text.match(/[1-9A-HJ-NP-Za-km-z]{32,44}/g);
                                if (walletMatch) {
                                    walletMatch.forEach(wallet => {
                                        if (wallet.length >= 32 && wallet.length <= 44) {
                                            results.push(wallet);
                                        }
                                    });
                                }
                            }
                        }

                        // 去重
                        return [...new Set(results)];
                    }
                ");

                if (walletData != null && walletData.Length > 0)
                {
                    Console.WriteLine($"找到 {walletData.Length} 个钱包地址");

                    // 生成文件名（包含时间戳）
                    string fileName = $"ave_smart_wallets_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                    string filePath = Path.Combine(Directory.GetCurrentDirectory(), fileName);

                    // 将结果写入txt文件
                    await File.WriteAllLinesAsync(filePath, walletData);

                    Console.WriteLine($"钱包地址数据已成功保存到: {filePath}");
                    Console.WriteLine($"文件大小: {new FileInfo(filePath).Length} 字节");
                    Console.WriteLine($"钱包地址数量: {walletData.Length}");
                }
                else
                {
                    Console.WriteLine("未找到钱包地址数据，尝试获取页面HTML进行调试...");

                    // 保存页面HTML用于调试
                    var htmlContent = await page.GetContentAsync();
                    string debugFileName = $"ave_smart_debug_{DateTime.Now:yyyyMMdd_HHmmss}.html";
                    string debugFilePath = Path.Combine(Directory.GetCurrentDirectory(), debugFileName);
                    await File.WriteAllTextAsync(debugFilePath, htmlContent);

                    Console.WriteLine($"页面HTML已保存到: {debugFilePath}");
                    Console.WriteLine("请检查HTML文件以了解页面结构");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"页面抓取失败: {ex.Message}");
                throw;
            }
        }

        static async Task RequestSmartWalletData()
        {
            try
            {
                Console.WriteLine("正在请求Smart Wallet数据...");

                // 构建完整的URL
                string baseUrl = "https://api.gejbckf.com/v1api/v4/tokens/smart_wallet/list";
                string queryParams = "?chain=solana&sort=rank_score&sort_dir=desc&interval=7D";
                string fullUrl = baseUrl + queryParams;

                // 设置请求头
                var headers = new Dictionary<string, string>
                {
                    { "x-auth", "98db851d5ffc1ca19b3dcfe126aeb8e11751083955929493582" },
                    { "ave-udid", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0--1748832071126--279d9824-cc3d-4df7-8052-0bdd0dbd3066" },
                    { "Origin", "https://ave.ai" },
                    { "Referer", "https://ave.ai/" }
                };

                // 发送请求
                var response = await MakeApiRequest(fullUrl, headers);

                Console.WriteLine("Smart Wallet API请求成功，正在解析数据...");

                // 解析JSON响应
                var jsonDocument = JsonDocument.Parse(response);
                var data = jsonDocument.RootElement.GetProperty("data");

                // 提取wallet_address和remark字段
                var extractedData = new List<string>();

                foreach (var item in data.EnumerateArray())
                {
                    var walletAddress = item.GetProperty("wallet_address").GetString() ?? "";
                    var remark = item.GetProperty("remark").GetString() ?? "";
                    extractedData.Add($"{walletAddress} {remark}");
                }

                // 生成文件名（包含时间戳）
                string fileName = $"smart_wallet_data_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                string filePath = Path.Combine(Directory.GetCurrentDirectory(), fileName);

                // 将结果写入txt文件
                await File.WriteAllLinesAsync(filePath, extractedData);

                Console.WriteLine($"Smart Wallet数据已成功保存到: {filePath}");
                Console.WriteLine($"文件大小: {new FileInfo(filePath).Length} 字节");
                Console.WriteLine($"记录数量: {extractedData.Count}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Smart Wallet请求发生错误: {ex.Message}");
            }
        }

        static async Task RequestKolData()
        {
            try
            {
                Console.WriteLine("正在请求KOL数据...");

                // 构建完整的URL
                string baseUrl = "https://mayeas023.com/v1api/v4/tokens/kol/list";
                string queryParams = "?chain=solana&sort=rank_score&sort_dir=desc&interval=7D";
                string fullUrl = baseUrl + queryParams;

                // 设置请求头
                var headers = new Dictionary<string, string>
                {
                    { "x-auth", "98db851d5ffc1ca19b3dcfe126aeb8e11751083955929493582" },
                    { "ave-udid", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0--1748832071126--279d9824-cc3d-4df7-8052-0bdd0dbd3066" },
                    { "Origin", "https://ave.ai" },
                    { "Referer", "https://ave.ai/" }
                };

                // 发送请求
                var response = await MakeApiRequest(fullUrl, headers);

                Console.WriteLine("KOL API请求成功，正在解析数据...");

                // 解析JSON响应
                var jsonDocument = JsonDocument.Parse(response);
                var data = jsonDocument.RootElement.GetProperty("data");

                // 提取wallet_address和remark字段
                var extractedData = new List<string>();

                foreach (var item in data.EnumerateArray())
                {
                    var walletAddress = item.GetProperty("wallet_address").GetString() ?? "";
                    var remark = item.GetProperty("remark").GetString() ?? "";
                    extractedData.Add($"{walletAddress} {remark}");
                }

                // 生成文件名（包含时间戳）
                string fileName = $"kol_data_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                string filePath = Path.Combine(Directory.GetCurrentDirectory(), fileName);

                // 将结果写入txt文件
                await File.WriteAllLinesAsync(filePath, extractedData);

                Console.WriteLine($"KOL数据已成功保存到: {filePath}");
                Console.WriteLine($"文件大小: {new FileInfo(filePath).Length} 字节");
                Console.WriteLine($"记录数量: {extractedData.Count}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"KOL请求发生错误: {ex.Message}");
            }
        }

        static async Task RequestGmgnKolData()
        {
            try
            {
                Console.WriteLine("正在请求GMGN KOL数据...");

                // 构建完整的URL
                string baseUrl = "https://gmgn.ai/defi/quotation/v1/rank/sol/wallets/7d";
                string queryParams = "?tag=smart_degen&tag=pump_smart&device_id=cc9961c7-3456-47e2-8a88-01297344b209&client_id=gmgn_web_20250627-482-8dff1e2&from_app=gmgn&app_ver=20250627-482-8dff1e2&tz_name=Asia%2FShanghai&tz_offset=28800&app_lang=zh-CN&fp_did=c75d3716917038cfc33dbeb6f3fae35c&os=web&orderby=pnl_7d&direction=desc";
                string fullUrl = baseUrl + queryParams;

                // 设置请求头
                var headers = new Dictionary<string, string>
                {
                    { "Origin", "https://gmgn.ai" },
                    { "Referer", "https://gmgn.ai/trade/solscan?chain=sol&tab=smart_degen" }
                };

                // 发送请求
                var response = await MakeApiRequest(fullUrl, headers);

                Console.WriteLine("GMGN KOL API请求成功，正在解析数据...");

                // 解析JSON响应
                var jsonDocument = JsonDocument.Parse(response);
                var data = jsonDocument.RootElement.GetProperty("data").GetProperty("rank");

                // 提取wallet_address和name字段
                var extractedData = new List<string>();

                foreach (var item in data.EnumerateArray())
                {
                    var walletAddress = item.GetProperty("wallet_address").GetString() ?? "";
                    var name = "";

                    // name字段可能为null，需要安全处理
                    if (item.TryGetProperty("name", out var nameProperty) && nameProperty.ValueKind != JsonValueKind.Null)
                    {
                        name = nameProperty.GetString() ?? "";
                    }

                    extractedData.Add($"{walletAddress} {name}");
                }

                // 生成文件名（包含时间戳）
                string fileName = $"gmgn_kol_data_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                string filePath = Path.Combine(Directory.GetCurrentDirectory(), fileName);

                // 将结果写入txt文件
                await File.WriteAllLinesAsync(filePath, extractedData);

                Console.WriteLine($"GMGN KOL数据已成功保存到: {filePath}");
                Console.WriteLine($"文件大小: {new FileInfo(filePath).Length} 字节");
                Console.WriteLine($"记录数量: {extractedData.Count}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GMGN KOL请求发生错误: {ex.Message}");
            }
        }

        static async Task RequestGmgnRenownedData()
        {
            try
            {
                Console.WriteLine("正在请求GMGN Renowned数据...");

                // 构建完整的URL
                string baseUrl = "https://gmgn.ai/defi/quotation/v1/rank/sol/wallets/7d";
                string queryParams = "?tag=renowned&device_id=cc9961c7-3456-47e2-8a88-01297344b209&client_id=gmgn_web_20250627-482-8dff1e2&from_app=gmgn&app_ver=20250627-482-8dff1e2&tz_name=Asia%2FShanghai&tz_offset=28800&app_lang=zh-CN&fp_did=c75d3716917038cfc33dbeb6f3fae35c&os=web&orderby=pnl_7d&direction=desc";
                string fullUrl = baseUrl + queryParams;

                // 设置请求头
                var headers = new Dictionary<string, string>
                {
                    { "Origin", "https://gmgn.ai" },
                    { "Referer", "https://gmgn.ai/trade/solscan?chain=sol&tab=smart_degen" }
                };

                // 发送请求
                var response = await MakeApiRequest(fullUrl, headers);

                Console.WriteLine("GMGN Renowned API请求成功，正在解析数据...");

                // 解析JSON响应
                var jsonDocument = JsonDocument.Parse(response);
                var data = jsonDocument.RootElement.GetProperty("data").GetProperty("rank");

                // 提取wallet_address和name字段
                var extractedData = new List<string>();

                foreach (var item in data.EnumerateArray())
                {
                    var walletAddress = item.GetProperty("wallet_address").GetString() ?? "";
                    var name = "";

                    // name字段可能为null，需要安全处理
                    if (item.TryGetProperty("name", out var nameProperty) && nameProperty.ValueKind != JsonValueKind.Null)
                    {
                        name = nameProperty.GetString() ?? "";
                    }

                    extractedData.Add($"{walletAddress} {name}");
                }

                // 生成文件名（包含时间戳）
                string fileName = $"gmgn_renowned_data_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                string filePath = Path.Combine(Directory.GetCurrentDirectory(), fileName);

                // 将结果写入txt文件
                await File.WriteAllLinesAsync(filePath, extractedData);

                Console.WriteLine($"GMGN Renowned数据已成功保存到: {filePath}");
                Console.WriteLine($"文件大小: {new FileInfo(filePath).Length} 字节");
                Console.WriteLine($"记录数量: {extractedData.Count}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GMGN Renowned请求发生错误: {ex.Message}");
            }
        }
    }
}