using Jaina.EventBus;
using Tran.Abp.Ws.Core;
using Tron.Abp.Caching.FreeRedis;
using Tron.Abp.Domain;
using Tron.Abp.Multiplex;
using Volo.Abp.Autofac;
using Volo.Abp.Modularity;

namespace Tran.Abp.Ws.PumpFun;

[DependsOn(typeof(AbpAutofacModule),
    typeof(AbpJainaEventBusModule),
    typeof(CachingFreeRedisModule),
    typeof(AbpDomainModule),
    typeof(AbpMultiplexModule),
    typeof(AbpBackgroundWorkersModule))]
public class AppModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        base.ConfigureServices(context);

        context.Services.AddEventBus();
        var configuration = context.Services.GetConfiguration();

        var solanaOption = configuration.GetSection("Solana").Get<SolanaOptions>();
        Configure<SolanaOptions>(options =>
        {
            options.MainWsrRpc = solanaOption.MainWsrRpc;
            options.MainWsUri = solanaOption.MainWsUri;
            options.MaingRpc = solanaOption.MaingRpc;
            options.MainUri = solanaOption.MainUri;
        });

        var grpcConf = configuration.GetSection("Grpc").Get<GrpcOptions>();
        Configure<GrpcOptions>(options =>
        {
            options.Commitment = grpcConf.Commitment;
            options.Endpoint = grpcConf.Endpoint;
        });
        var channelOptions = new GrpcChannelOptions
        {
            MaxReceiveMessageSize = 1024 * 1024 * 1024, // 128MB，匹配 Yellowstone 的需求
        };
        GrpcChannel channel = GrpcChannel.ForAddress(grpcConf.Endpoint, channelOptions);
        Geyser.GeyserClient client = new Geyser.GeyserClient(channel);
        context.Services.AddSingleton<Geyser.GeyserClient>(client);
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        base.OnApplicationInitialization(context);
        //context.AddBackgroundWorkerAsync<PumpFunPriceWorker>();

        context.AddBackgroundWorkerAsync<PingWorker>();
        context.AddBackgroundWorkerAsync<GRpcPumpFunPriceWorker>();
    }
}