using FreeRedis;
using Tron.Abp.Domain;
using Tron.Abp.Multiplex.Contracts;
using Tron.Abp.Multiplex.Solana;
using Tron.Abp.SqlSugar;
using Volo.Abp.DependencyInjection;

namespace Tran.Abp.MarketCap.Services;


/// <summary>
/// 交易更新
/// </summary>
public record AllTradeUpdateAmountEvent(int Id, string Sign, string Mint, string DexType, bool IsBuy, string TradeType, 
    string Owner, int Percentage);

public interface IAllTradesSolAmountTokenAmount
{
    Task<(bool,decimal,decimal)> UpdateAllTrades_SolAmount_TokenAmount(AllTradeUpdateAmountEvent sw);
}

public class AllTradesSolAmountTokenAmount: IAllTradesSolAmountTokenAmount,IScopedDependency
{
    private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;
    private ISolanaApi _solanaApi;
    private IRedisClient _redisClient;
    public AllTradesSolAmountTokenAmount(SqlSugarRepository<UserAllTraderLog> userAllTraderLogRepository, ISolanaApi solanaApi, IRedisClient redisClient)
    {
        _userAllTraderLogRepository = userAllTraderLogRepository;
        _solanaApi = solanaApi;
        _redisClient = redisClient;
    }
    
    public async Task<(bool, decimal, decimal)> UpdateAllTrades_SolAmount_TokenAmount(AllTradeUpdateAmountEvent sw)
    {
        var log =await _userAllTraderLogRepository.AsQueryable().Where(it=>it.Id==sw.Id).FirstAsync();
        if(log == null) return (false,0,0);
        //查询交易
        var (solAmount, tokenAmount) = (log.SolAmount, log.TokenAmount);
        var i = 0;
        while (solAmount == 0 && tokenAmount == 0)
        {
            if (i > 10) break;
            await Task.Delay(1500);
            (solAmount, tokenAmount) = await _solanaApi.ParserTransaction(sw.Sign, sw.Mint, sw.DexType, sw.IsBuy);
            i++;
           
        }
        var wsolStr = await _redisClient.GetAsync(RedisKey.SolanaWSOLPrice) ?? "0";
        var wsol = Decimal.Parse(wsolStr);
        var isSuccessful = !(solAmount == 0 && tokenAmount == 0);
        var tokenPrice =isSuccessful?solAmount / tokenAmount:0m;
        var tokenUsdcPrice = isSuccessful?solAmount / tokenAmount*wsol:0m;
        var Status = isSuccessful ? $"{sw.TradeType}-成功" : $"{sw.TradeType}-失败";
        
        
        //更新记录
        await _userAllTraderLogRepository.CopyNew().AsUpdateable()
            .SetColumns(it => it.TokenAmount == tokenAmount)
            .SetColumns(it => it.SolAmount == solAmount)
            .SetColumns(it=>it.Signature==sw.Sign)
            .SetColumns(it=>it.CompleteTime==DateTime.Now)
            .SetColumns(it=>it.TokenSolPrice == tokenPrice)
            .SetColumns(it=>it.TokenUsdtPrice == tokenUsdcPrice)
            .SetColumns(it=>it.Status==Status)
            .Where(it => it.Id == log.Id)
            .ExecuteCommandAsync();
        return (isSuccessful,solAmount,tokenAmount);
    }
}