using Jaina;
using SqlSugar;
using Tran.Abp.MarketCap.Model;
using Tran.Abp.MarketCap.Services;
using Tron.Abp.Domain;
using Tron.Abp.Multiplex.Solana;
using Tron.Abp.SqlSugar;
using Volo.Abp.DependencyInjection;

namespace Tran.Abp.MarketCap.EventHandlers;
public record SetSubTaskStatusEvent(int TaskId);
public record SetSubTaskBrushCountEvent(int TaskId);
public class DbUpdataEventSubscriber: IEventSubscriber, ISingletonDependency
{
    private readonly ISolanaApi _solanaApi;
    private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;
    private readonly SqlSugarRepository<UserMarketCapSubTask> _userMarketCapSubTaskRepository;
    private readonly SqlSugarRepository<UserMarketCapLogCount> _userMarketCapLogCountRepository;
    private IAllTradesSolAmountTokenAmount _allTradesSolAmountTokenAmount;
    public DbUpdataEventSubscriber(IAllTradesSolAmountTokenAmount allTradesSolAmountTokenAmount, SqlSugarRepository<UserMarketCapSubTask> userMarketCapSubTaskRepository, 
        SqlSugarRepository<UserMarketCapLogCount> userMarketCapLogCountRepository)
    {
        _allTradesSolAmountTokenAmount = allTradesSolAmountTokenAmount;
        _userMarketCapSubTaskRepository = userMarketCapSubTaskRepository;
        _userMarketCapLogCountRepository = userMarketCapLogCountRepository;
    }

    #region 私有方法
    /// <summary>
    /// 更新价格 及 刷单次数
    /// </summary>
    /// <param name="taskId"></param>
    /// <param name="price"></param>
    private async Task Update_MarketCapSubTask(int taskId,decimal price)
    {
        
        await _userMarketCapSubTaskRepository.AsUpdateable()
            .SetColumns(it => it.CurrentPrice == price)
            .SetColumns(it=>it.CurrentBrushCount==it.CurrentBrushCount+1)
            .Where(it => it.TaskId == taskId)
            .ExecuteCommandAsync();
    }
    /// <summary>
    /// 统计 任务 钱包买入代币数
    /// </summary>
    /// <param name="uid"></param>
    /// <param name="taskId"></param>
    /// <param name="pubKey"></param>
    /// <param name="secretKey"></param>
    /// <param name="tokenAmount"></param>
    private async Task Update_MarketCapLogCount(int uid,int taskId,string pubKey,string secretKey,decimal tokenAmount)
    {
        var marketCapLog = await _userMarketCapLogCountRepository.AsQueryable()
            .Where(it => it.UId == uid && it.SubTaskId == taskId && it.PubKey == pubKey)
            .FirstAsync();
        if (marketCapLog == null)
        {
            marketCapLog = new UserMarketCapLogCount()
            {
                UId = uid,
                SubTaskId = taskId,
                PubKey = pubKey,
                SecretKey = secretKey,
                TokenAmount = tokenAmount,
                CreateTime = DateTime.Now,
                Mark = ""
            };
            marketCapLog.Id = await _userMarketCapLogCountRepository.CopyNew().AsInsertable(marketCapLog)
                .ExecuteReturnIdentityAsync();
        }
        else
        {
            await _userMarketCapLogCountRepository.CopyNew().AsUpdateable()
                .SetColumns(it => it.TokenAmount == it.TokenAmount + tokenAmount)
                .Where(it => it.Id == marketCapLog.Id)
                .ExecuteCommandAsync();
        }
    }
    #endregion
    
    #region 交易记录相关 操作

    [EventSubscribe(EventConsts.DBUpdata_AllTrades_SolAmount_TokenAmount)]
    public async Task<bool> AllTrades_SolAmount_TokenAmount(EventHandlerExecutingContext context)
    {
        var sw = (UpdataMCTModel)context.Source.Payload;
        
        var (flag, solAmount, tokenAmount) = await _allTradesSolAmountTokenAmount.UpdateAllTrades_SolAmount_TokenAmount(sw.TradeEvent);
        //交易成功
        if (flag)
        {
            var price = solAmount / tokenAmount;
            await Update_MarketCapSubTask(sw.TaskItem.TaskId, price);
            await Update_MarketCapLogCount(sw.TaskItem.UId, sw.TaskItem.TaskId, sw.TaskItem.PublicKey, sw.TaskItem.SecretKey, tokenAmount);
        }
        
        return flag;
    }

    
    
    #endregion
    /// <summary>
    /// 设置任务状态
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe(EventConsts.SetSubTaskStatus)]
    public async Task<bool> SetSubTaskStatusAction(EventHandlerExecutingContext context)
    {
        var ev = (SetSubTaskStatusEvent)context.Source.Payload;
        await _userMarketCapSubTaskRepository.CopyNew().AsUpdateable()
            .SetColumns(it => it.Status == -1)
            .Where(it => it.Id == ev.TaskId)
            .ExecuteCommandAsync();
        return true;
    }
    
    [EventSubscribe(EventConsts.SetSubTaskBrushCount)]
    public async Task<bool> SetSubTaskBrushCountAction(EventHandlerExecutingContext context)
    {
        var ev = (SetSubTaskBrushCountEvent)context.Source.Payload;
        await _userMarketCapSubTaskRepository.CopyNew().AsUpdateable()
            .SetColumns(it => it.CurrentBrushCount ==it.CurrentBrushCount+1)
            .SetColumns(it=>it.CurrentCycleCount==it.CurrentCycleCount+1)
            .SetColumns(it=>it.Status==SqlFunc.IIF(it.CurrentBrushCount+1>=it.BrushCount,-1,it.Status))
            .Where(it => it.Id == ev.TaskId)
            .ExecuteCommandAsync();
        return true;
    }
}