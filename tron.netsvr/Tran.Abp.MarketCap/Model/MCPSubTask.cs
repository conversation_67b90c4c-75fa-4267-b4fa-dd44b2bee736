namespace Tran.Abp.MarketCap.Model;

public class MCPSubTask
{
    public int TaskId { get; set; }
    public int UId { get; set; }
    public string Chain { get; set; }
    public string TokenAddress { get; set; }
    public string TokenName { get; set; }
    public string TokenSymbol { get; set; }
    public string TokenIcon { get; set; }
    public int Wid { get; set; }
    public string PublicKey { get; set; }
    public string SecretKey { get; set; }
    public string WalletName { get; set; }
    public int Model { get; set; }
    public decimal? MinAmount { get; set; }
    public decimal? MaxAmount { get; set; }
    public int SellPercent { get; set; }
    public int StartTime { get; set; }
    public int EndTime { get; set; }
    public int WaitTime { get; set; }
    public int Slippage { get; set; }
    public bool IsMev { get; set; }
    public decimal Gas { get; set; }
    public decimal Mev { get; set; }
    public decimal TargetPrice { get; set; }
    public decimal CurrentPrice { get; set; }
    public int BrushCount { get; set; }
    public int CurrentBrushCount { get; set; }
    public int Status { get; set; }
    public bool IsOpen { get; set; }
    public decimal? AmountAll { get; set; }
    public decimal? CurrentAmountAll { get; set; }
    public int Decimals { get; set; }
    public int CycleCount { get; set; }
    public int CurrentCycleCount { get; set; }
    public List<int> MevType { get; set; } = new List<int>();
}