namespace Tran.Abp.MarketCap.Model;

public class CTask
{
    public int TaskId { get; set; }
    public string TokenAddress { get; set; }
    public string DexType { get; set; }
    public CancellationTokenSource Cts { get; set; } = new CancellationTokenSource();
}


public class CcTask
{
    public string DexType { get; set; }
    public int Uid { get; set; }
    public string Mint { get; set; }
    public string SecretKey { get; set; }
    public int Slippage { get; set; }
    public decimal Gas { get; set; }
    public decimal Mev { get; set; }
    public bool IsMev { get; set; }
    public List<int> MevType { get; set; } = new List<int>();
}