<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <None Update="appsettings.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\Cryptography.Obfuscation\Cryptography.Obfuscation.csproj" />
        <ProjectReference Include="..\Jaina.EventBus\Jaina.EventBus.csproj" />
        <ProjectReference Include="..\Tron.Abp.Core\Tron.Abp.Core.csproj" />
        <ProjectReference Include="..\Tron.Abp.Domain.Contracts\Tron.Abp.Domain.Contracts.csproj" />
        <ProjectReference Include="..\Tron.Abp.Domain\Tron.Abp.Domain.csproj" />
        <ProjectReference Include="..\Tron.Abp.Multiplex.Contracts\Tron.Abp.Multiplex.Contracts.csproj" />
        <ProjectReference Include="..\Tron.Abp.Multiplex\Tron.Abp.Multiplex.csproj" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Jaina" Version="4.4.0" />
    </ItemGroup>
</Project>
