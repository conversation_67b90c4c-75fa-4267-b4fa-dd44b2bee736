using System.Collections.Concurrent;
using FreeRedis;
using FreeRedis.Internal;
using Jaina;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Tran.Abp.MarketCap.Model;
using Tran.Abp.MarketCap.Services;
using Tran.Abp.MonitorServer;
using Tron.Abp.Domain;
using Tron.Abp.Multiplex.Contracts;
using Tron.Abp.Multiplex.Solana;
using Tron.Abp.SqlSugar;
using Volo.Abp.BackgroundWorkers;

namespace Tran.Abp.MarketCap.BackgroundWorker;

public class MarketCapClearTokenWorker : BackgroundWorkerBase
{
    public ILogger<MarketCapClearTokenWorker> Logger { get; set; }
    private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;
    private IRedisClient _redisClient;
    private SubscribeStreamObject subscribeStream;
    private ISolanaApi _solanaApi;
    private IEventPublisher _eventPublisher;
    Dictionary<string, WorkQueue<CcTask>> dictionary = new Dictionary<string, WorkQueue<CcTask>>();
    private IAllTradesSolAmountTokenAmount _allTradesSolAmountTokenAmount;
    private readonly SqlSugarRepository<UserWallet> _userWalletRepository;
    public MarketCapClearTokenWorker(IRedisClient redisClient, ISolanaApi solanaApi,
        SqlSugarRepository<UserAllTraderLog> userAllTraderLogRepository,
        IAllTradesSolAmountTokenAmount allTradesSolAmountTokenAmount, SqlSugarRepository<UserWallet> userWalletRepository)
    {
        _redisClient = redisClient;
        _solanaApi = solanaApi;
        _userAllTraderLogRepository = userAllTraderLogRepository;
        _allTradesSolAmountTokenAmount = allTradesSolAmountTokenAmount;
        _userWalletRepository = userWalletRepository;
        Logger = NullLogger<MarketCapClearTokenWorker>.Instance;
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        Logger.LogDebug($"市值管理 清仓 服务 启动");

        subscribeStream =
            _redisClient.SubscribeStream(StreamKey.MarketCapClearTokenStream, onMessage: OnRedisSubscribeStreamMessage);

        await base.StartAsync(cancellationToken);
    }

    private async void OnRedisSubscribeStreamMessage(Dictionary<string, string> obj)
    {
        /*
         *  ["uid"] = curUid.ToString(),
            ["tokenAddress"] = input.TokenAddress,
            ["walletList"] = string.Join(",", walletList),
            ["slippage"] = input.Slippage.ToString(),
            ["gas"] = (input.Gas ?? 0).ToString(),
            ["isMev"] = input.IsMev.ToString(),
            ["mev"] = (input.Mev ?? 0).ToString(),
            ["action"] = "clear"
         */

        var uid = int.Parse(obj["uid"]);
        var mint = obj["tokenAddress"];
        var walletList = obj["walletList"].Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).ToList();
        var slippage = int.Parse(obj["slippage"]);
        var gas = decimal.Parse(obj["gas"]);
        var isMev = bool.Parse(obj["isMev"]);
        var mev = decimal.Parse(obj["mev"]);
        //var action = obj["action"];
        
        //本站所有持仓
        var wallets = await _userWalletRepository.AsQueryable().Where(it => it.UId==uid)
            .ToListAsync();
        ConcurrentDictionary<string, string> tokenHolds = new ConcurrentDictionary<string, string>();
        await Parallel.ForEachAsync(wallets, async (walletAddress, cancellationToken) =>
        {
            var tokenAmount = await _solanaApi.GetTokenAccountBalanceAsync(walletAddress.PubKey, mint);
            if (tokenAmount >= 0) 
                tokenHolds[walletAddress.PubKey] = walletAddress.SecretKey;
        });
        foreach (var tokenHold in tokenHolds)
        {
            if (walletList.Contains(tokenHold.Value)) continue;
            walletList.Add(tokenHold.Value);
        }
        
        Logger.LogDebug($"收到市值管理 清仓 消息 {uid} {mint} {tokenHold.Count} {slippage} {gas} {isMev} {mev}");
        var size = walletList.Count > 100 ? 100 : walletList.Count;
        var workQueue = QueueManager.CreateQueue<CcTask>(WorkQueueAction, size);
        dictionary[$"{uid}_{mint}"] = workQueue;
        var dexType = await _solanaApi.GetMintDexType(mint);
        for (int i = 0; i < size; i++)
        {
            var ctask = new CcTask()
            {
                Uid = uid, Mint = mint, DexType = dexType, SecretKey = walletList[i], Slippage = slippage, Gas = gas,
                Mev = mev, IsMev = isMev
            };
            workQueue.Enqueue(ctask);
        }
    }

    private async Task WorkQueueAction(CcTask item)
    {
        var account = Solnet.Wallet.Account.FromSecretKey(item.SecretKey);
        var (successful, sign, errormsg) =
            await _solanaApi.Sell(item.DexType, account, item.Mint, 100, item.Gas, item.Mev, item.Slippage);
        Logger.LogDebug($"市值管理 清仓 代币:{item.Mint} 钱包:{account.PublicKey} 交易返回 {successful}=>{sign}=>{errormsg}");
        if (!(successful && !string.IsNullOrWhiteSpace(sign)))
        {
            Logger.LogDebug($"市值管理 清仓 代币:{item.Mint} 钱包:{account.PublicKey} 交易失败 {errormsg}");
            return;
        }

        var tokenInfo = await _solanaApi.GetMint(item.Mint);

        //写交易日志
        var logModel = new UserAllTraderLog()
        {
            TraderType = TraderType.Trader_06,
            //TaskId = modelTask.TaskId,
            UId = item.Uid,
            Chain = tokenInfo.Chain,
            IsBuy = false,
            SolAmount = 0,
            TokenAmount = 0,
            Gas = item.Gas,
            Mev = item.Mev,
            MyWalletAddress = account.PublicKey,
            TokenAddress = item.Mint,
            TokenName = tokenInfo.Name,
            TokenSymbol = tokenInfo.Symbol,
            TokenIcon = tokenInfo.Icon,
            Signature = sign,
            Status = "市值管理清仓-成功",
            CreateTime = DateTime.Now,
        };
        logModel.Id = await _userAllTraderLogRepository.CopyNew()
            .AsInsertable(logModel)
            .ExecuteReturnIdentityAsync();
        var atuae = new AllTradeUpdateAmountEvent(logModel.Id, sign, item.Mint, item.DexType, false, "市值管理清仓",
            logModel.MyWalletAddress, 0);

        var (flag, solAmount, tokenAmount) =
            await _allTradesSolAmountTokenAmount.UpdateAllTrades_SolAmount_TokenAmount(atuae);
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        Logger.LogDebug($"市值管理 清仓 服务 停止");
        subscribeStream.Dispose();
        await base.StopAsync(cancellationToken);
    }
}