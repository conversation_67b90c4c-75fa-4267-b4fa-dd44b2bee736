using System.Collections.Concurrent;
using System.Security.Cryptography;
using System.Text.Json;
using FreeRedis;
using FreeRedis.Internal;
using Jaina;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using SqlSugar;
using Tran.Abp.MarketCap.EventHandlers;
using Tran.Abp.MarketCap.Model;
using Tran.Abp.MarketCap.Services;
using Tran.Abp.MonitorServer;
using Tron.Abp.Core;
using Tron.Abp.Domain;
using Tron.Abp.Multiplex.Contracts;
using Tron.Abp.Multiplex.Solana;
using Tron.Abp.SqlSugar;
using Volo.Abp.BackgroundWorkers;

namespace Tran.Abp.MarketCap.BackgroundWorker;

public class MarketCapActionWorker : BackgroundWorkerBase
{
    public ILogger<MarketCapActionWorker> Logger { get; set; }
    private IRedisClient _redisClient;
    private SubscribeStreamObject subscribeStream;
    private ISolanaApi _solanaApi;
    private IEventPublisher _eventPublisher;
    private readonly SqlSugarRepository<UserMarketCapTask> _userMarketCapTaskRepository;
    private readonly SqlSugarRepository<UserMarketCapSubTask> _userMarketCapSubTaskRepository;
    private readonly SqlSugarRepository<UserWallet> _userWalletRepository;
    private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;
    Dictionary<string, WorkQueue<CTask>> dictionary = new Dictionary<string, WorkQueue<CTask>>();

    ConcurrentDictionary<int, CancellationTokenSource> dictionaryLock =
        new ConcurrentDictionary<int, CancellationTokenSource>();

    public MarketCapActionWorker(ISolanaApi solanaApi, IRedisClient redisClient, IEventPublisher eventPublisher,
        SqlSugarRepository<UserMarketCapSubTask> userMarketCapSubTaskRepository,
        SqlSugarRepository<UserWallet> userWalletRepository,
        SqlSugarRepository<UserAllTraderLog> userAllTraderLogRepository,
        SqlSugarRepository<UserMarketCapTask> userMarketCapTaskRepository)
    {
        _solanaApi = solanaApi;
        _redisClient = redisClient;
        _eventPublisher = eventPublisher;
        _userMarketCapSubTaskRepository = userMarketCapSubTaskRepository;
        _userWalletRepository = userWalletRepository;
        _userAllTraderLogRepository = userAllTraderLogRepository;
        _userMarketCapTaskRepository = userMarketCapTaskRepository;
        Logger = NullLogger<MarketCapActionWorker>.Instance;
    }

    private int GenerateRandomInt(int? min, int? max)
    {
        // 情况 1：min 和 max 都为 null
        if (!min.HasValue && !max.HasValue)
        {
            throw new ArgumentException("min 和 max 不能同时为 null");
        }

        // 情况 2：min 为 null，max 非 null
        if (!min.HasValue && max.HasValue)
        {
            return RandomHelper.GetRandom(0, max.Value + 1); // [0, max]
        }

        // 情况 3：min 非 null，max 为 null
        if (min.HasValue && !max.HasValue)
        {
            return min.Value; // 返回 min
        }

        // 情况 4：min 和 max 都非 null
        int actualMin = Math.Min(min.Value, max.Value);
        int actualMax = Math.Max(min.Value, max.Value);

        if (actualMin == actualMax)
        {
            return actualMin; // min == max，直接返回
        }

        return RandomHelper.GetRandom(actualMin, actualMax);
    }


    public decimal GenerateRandomDecimal(decimal? min, decimal? max, int num = 3)
    {
        // 情况 1：min 和 max 都为 null
        if (!min.HasValue && !max.HasValue)
        {
            throw new ArgumentException("min 和 max 不能同时为 null");
        }

        // 情况 2：min 为 null，max 非 null
        if (!min.HasValue && max.HasValue)
        {
            //return RandomHelper.GenerateRandomDecimalInRange(0, max.Value);
            min = 0m;
        }

        // 情况 3：min 非 null，max 为 null
        if (min.HasValue && !max.HasValue)
        {
            return min.Value; // 返回 min
        }

        // 情况 4：min 和 max 都非 null
        decimal actualMin = Math.Min(min.Value, max.Value);
        decimal actualMax = Math.Max(min.Value, max.Value);

        if (actualMin == actualMax)
        {
            return actualMin; // min == max，直接返回
        }

        if (num >= 3)
        {
            num = RandomHelper.GetDecimalPlaces(min.Value);
        }

        var decimals = (int)Math.Pow(10, num);
        return RandomHelper.GenerateRandomDecimalInRange(actualMin, actualMax, decimals);
    }

    /// <summary>
    /// 载入 运行 市值 管理 任务 
    /// </summary>
    private async Task InitSubscribeStream(int taskId = 0)
    {
        //读取数据库
        var list = await _userMarketCapSubTaskRepository.AsQueryable()
            .WhereIF(taskId > 0, it => it.Id == taskId)
            .Where(it => it.Status == 1)
            .ToListAsync();
        foreach (var capSubTask in list)
        {
            await CreateSubTask(capSubTask);

            await Task.Delay(200);
        }
    }

    private async Task CreateSubTask(UserMarketCapSubTask capSubTask)
    {
        var isexist = await _redisClient.ExistsAsync($"{RedisKey.MarketCapQueue}:{capSubTask.Id}");
        if (!isexist)
        {
            var wallets = await _userWalletRepository.AsQueryable()
                .Where(it => capSubTask.GroupId.Contains(it.GId))
                .ToListAsync();
            //// 在内存中按数组顺序排序
            //var result = data.OrderBy(x => Array.IndexOf(idArray, x.Id)).ToList();
            var list = wallets.OrderBy(it => Array.IndexOf(capSubTask.GroupId.ToArray(), it.GId))
                .ToList(); /*new List<UserWallet>();*/
            /*for (int i = 0; i < capSubTask.GroupId.Count; i++)
            {
                var gid=capSubTask.GroupId[i];
                var wallet =  wallets.Where(it => it.GId == gid)
                    .ToList();
                list.AddRange(wallet);
            }*/
            Logger.LogDebug($"市场管理 {capSubTask.TokenAddress} 任务:{capSubTask.Id} 钱包:{wallets.Count} 创建任务 开始");
            //生成 任务
            foreach (var userWallet in list)
            {
                var modelTask = new Model.MCPSubTask()
                {
                    TaskId = capSubTask.Id,
                    UId = capSubTask.UId,
                    Chain = capSubTask.Chain,
                    TokenAddress = capSubTask.TokenAddress,
                    TokenName = capSubTask.TokenName,
                    TokenSymbol = capSubTask.TokenSymbol,
                    TokenIcon = capSubTask.TokenIcon,
                    Wid = userWallet.Id,
                    PublicKey = userWallet.PubKey,
                    SecretKey = userWallet.SecretKey,
                    WalletName = userWallet.Name,
                    Model = capSubTask.Mode,
                    MinAmount = capSubTask.AmountData[0],
                    MaxAmount = capSubTask.AmountData[1],
                    Decimals = capSubTask.AmountData.Count >= 3 ? (int)(capSubTask.AmountData[2] ?? 3) : 3,
                    SellPercent = capSubTask.SellPercent ?? 0,
                    StartTime = capSubTask.IntervalData[0],
                    EndTime = capSubTask.IntervalData[1],
                    //WaitTime = capSubTask.WaitTime,
                    Slippage = capSubTask.Slippage,
                    IsMev = capSubTask.IsMev,
                    Gas = capSubTask.Gas ?? 0,
                    Mev = capSubTask.Mev ?? 0,
                    TargetPrice = capSubTask.TargetPrice ?? 0,
                    CurrentPrice = capSubTask.CurrentPrice ?? 0,
                    BrushCount = capSubTask.BrushCount ?? 0,
                    CurrentBrushCount = capSubTask.CurrentBrushCount ?? 0,
                    Status = capSubTask.Status,
                    IsOpen = capSubTask.IsOpen,
                    CurrentAmountAll = capSubTask.CurrentAmountAll,
                    AmountAll = capSubTask.AmountAll,
                    CurrentCycleCount = capSubTask.CurrentCycleCount ?? 0,
                    CycleCount = capSubTask.CycleCount ?? 0,
                    MevType = capSubTask.MevType,
                };
                //写入 循环队列
                await _redisClient.LPushAsync($"{RedisKey.MarketCapQueue}:{capSubTask.Id}", modelTask);
            }
        }

        var dexType = await _solanaApi.GetMintDexType(capSubTask.TokenAddress);
        var workQueue = QueueManager.CreateQueue<CTask>(WorkQueueAction, 1);
        dictionary[$"{capSubTask.UId}_{capSubTask.Id}"] = workQueue;
        var ctask = new CTask() { TaskId = capSubTask.Id, DexType = dexType, TokenAddress = capSubTask.TokenAddress };
        workQueue.Enqueue(ctask);
        dictionaryLock[ctask.TaskId] = ctask.Cts;
        Logger.LogDebug($"市场管理 {capSubTask.TokenAddress} 任务:{capSubTask.Id}  创建任务 完成 载入队列");
    }

    private async Task WorkQueueAction(CTask taskitem)
    {
        do
        {
            var modelTask =
                await _redisClient.RPopAsync<Model.MCPSubTask>($"{RedisKey.MarketCapQueue}:{taskitem.TaskId}");
            if (modelTask == null)
            {
                Logger.LogWarning($"市场管理 {taskitem.TokenAddress} 任务:{taskitem.TaskId} 没有任务 退出");
                //结束任务
                await _eventPublisher.PublishAsync(EventConsts.SetSubTaskStatus,
                    new SetSubTaskStatusEvent(taskitem.TaskId));
                break;
            }

            //todo: 池子信息 需要处理
            var mintCurPrice = await _solanaApi.GetMintPrice(taskitem.TokenAddress, taskitem.DexType, "");
            Logger.LogDebug($"市场管理 {taskitem.TokenAddress} 任务:{taskitem.TaskId} 当前价格:{mintCurPrice}");


            var account = Solnet.Wallet.Account.FromSecretKey(modelTask.SecretKey);
            //上链交易 
            var (successful, sign, errormsg) = (false, string.Empty, string.Empty);
            //模式 1拉盘/买入，2砸盘/卖出，3防夹刷量
            if (modelTask.Model == 3)
            {
                var item = await _userMarketCapSubTaskRepository.AsQueryable().Where(it => it.Id == modelTask.TaskId)
                    .FirstAsync();
                if (item == null) break;
                if (item.CurrentBrushCount >= item.BrushCount && item.BrushCount > 0 || item.Status != 1)
                {
                    Logger.LogDebug(
                        $"市场管理 {taskitem.TokenAddress} 任务:{taskitem.TaskId} 当前刷量次数:{modelTask.CurrentBrushCount} 达到目标次数:{modelTask.BrushCount} 退出");
                    break;
                }

                var buySol = GenerateRandomDecimal(modelTask.MinAmount, modelTask.MaxAmount);
                (successful, sign, errormsg) = await _solanaApi.BuySell(taskitem.DexType, account,
                    taskitem.TokenAddress,
                    buySol, modelTask.Slippage, modelTask.Gas, modelTask.Mev, isMev: modelTask.IsMev,
                    mevList: modelTask.MevType);

                Logger.LogDebug(
                    $"市场管理 {taskitem.TokenAddress} 任务:{taskitem.TaskId} 钱包:{modelTask.PublicKey} 开始执行 刷量 {modelTask.MinAmount}");
                await _eventPublisher.PublishAsync(EventConsts.SetSubTaskBrushCount,
                    new SetSubTaskBrushCountEvent(taskitem.TaskId));
            }
            else
            {
                var item = await _userMarketCapSubTaskRepository.AsQueryable().Where(it => it.Id == modelTask.TaskId)
                    .FirstAsync();
                if (item == null) break;
                if (item.CurrentCycleCount >= item.CycleCount && item.CycleCount > 0 || item.Status != 1)
                {
                    Logger.LogDebug(
                        $"市场管理 {taskitem.TokenAddress} 任务:{taskitem.TaskId} 当前刷量次数:{modelTask.CurrentCycleCount} 达到目标次数:{modelTask.CycleCount} 退出");
                    break;
                }

                // 定义操作类型和参数
                string operationType = modelTask.Model == 1 ? "拉盘" : "砸盘";
                bool isTargetPriceReached = modelTask.Model == 1
                    ? mintCurPrice > 0 && modelTask.TargetPrice > 0 && mintCurPrice >= modelTask.TargetPrice
                    : mintCurPrice > 0 && modelTask.TargetPrice > 0 && mintCurPrice <= modelTask.TargetPrice;
                // 检查是否达到目标价格
                if (isTargetPriceReached)
                {
                    Logger.LogDebug(
                        $"市场管理 {taskitem.TokenAddress} 任务:{taskitem.TaskId} 当前价格:{mintCurPrice} 达到目标价格:{modelTask.TargetPrice} 退出");
                    await _eventPublisher.PublishAsync(EventConsts.SetSubTaskStatus,
                        new SetSubTaskStatusEvent(taskitem.TaskId));
                    break;
                }

                if (modelTask.Model == 1)
                {
                    var buySol = GenerateRandomDecimal(modelTask.MinAmount, modelTask.MaxAmount, modelTask.Decimals);

                    Logger.LogDebug(
                        $"市场管理 {taskitem.TokenAddress} 任务:{taskitem.TaskId} 钱包:{modelTask.PublicKey} 开始执行 拉盘 {buySol}");

                    (successful, sign, errormsg) = await _solanaApi.Buy(taskitem.DexType, account,
                        taskitem.TokenAddress,
                        buySol, modelTask.Slippage, modelTask.Gas, modelTask.Mev, isMev: modelTask.IsMev,
                        mevList: modelTask.MevType);
                    Logger.LogDebug(
                        $"市场管理 {taskitem.TokenAddress} 任务:{taskitem.TaskId} 钱包:{modelTask.PublicKey} 拉盘交易返回 {successful}=>{sign}=>{errormsg}");
                }
                else if (modelTask.Model == 2)
                {
                    Logger.LogDebug(
                        $"市场管理 {taskitem.TokenAddress} 任务:{taskitem.TaskId} 钱包:{modelTask.PublicKey} 开始执行 砸盘 {modelTask.SellPercent}%");
                    (successful, sign, errormsg) = await _solanaApi.Sell(taskitem.DexType, account,
                        taskitem.TokenAddress, modelTask.SellPercent,
                        slippage: modelTask.Slippage, fee: modelTask.Gas, mev: modelTask.Mev, isMev: modelTask.IsMev,
                        mevList: modelTask.MevType);
                    Logger.LogDebug(
                        $"市场管理 {taskitem.TokenAddress} 任务:{taskitem.TaskId} 钱包:{modelTask.PublicKey} 砸盘交易返回 {successful}=>{sign}=>{errormsg}");
                }

                await _eventPublisher.PublishAsync(EventConsts.SetSubTaskCycleCount,
                    new SetSubTaskBrushCountEvent(taskitem.TaskId));
            }

            //上链交易结果
            if (!successful || string.IsNullOrWhiteSpace(sign))
            {
                Logger.LogDebug(
                    $"市场管理 {taskitem.TokenAddress} 任务:{taskitem.TaskId} 钱包:{modelTask.PublicKey} 交易失败 {errormsg}");
                goto Next;
            }

            //写交易日志
            var logModel = new UserAllTraderLog()
            {
                TraderType = TraderType.Trader_05,
                TaskId = modelTask.TaskId,
                UId = modelTask.UId,
                Chain = modelTask.Chain,
                IsBuy = modelTask.Model == 1,
                SolAmount = 0,
                TokenAmount = 0,
                Gas = modelTask.Gas,
                Mev = modelTask.Mev,
                MyWalletAddress = modelTask.PublicKey,
                TokenAddress = modelTask.TokenAddress,
                TokenName = modelTask.TokenName,
                TokenSymbol = modelTask.TokenSymbol,
                TokenIcon = modelTask.TokenIcon,
                Signature = sign,
                Status = "市值管理拉盘-成功",
                CreateTime = DateTime.Now,
            };
            logModel.Id = await _userAllTraderLogRepository.CopyNew()
                .AsInsertable(logModel)
                .ExecuteReturnIdentityAsync();
            //把当前记录再存入队列
            await _redisClient.LPushAsync($"{RedisKey.MarketCapQueue}:{modelTask.TaskId}", modelTask);
            //更新交易结果
            var atuae = new AllTradeUpdateAmountEvent(logModel.Id, sign, logModel.TokenAddress, taskitem.DexType,
                logModel.IsBuy, "市值管理拉盘", "", 0);

            var send = new UpdataMCTModel(modelTask, atuae);

            //更新统计 持仓 等
            await _eventPublisher.PublishAsync(EventConsts.DBUpdata_AllTrades_SolAmount_TokenAmount, send);
            Next:
            var waitTime = GenerateRandomInt(modelTask.StartTime, modelTask.EndTime);
            Logger.LogDebug(
                $"市场管理 {taskitem.TokenAddress} 任务:{taskitem.TaskId} 等待 {waitTime}s 执行下一条");
            await Task.Delay(1000 * waitTime);
        } while (!dictionaryLock[taskitem.TaskId].IsCancellationRequested);
    }


    private async void OnRedisSubscribeStreamMessage(Dictionary<string, string> obj)
    {
        var uid = int.Parse(obj["uid"]);
        var taskId = int.Parse(obj["taskId"]);
        var status = int.Parse(obj["Status"]);
        var dict = new Dictionary<string, string>();
        var mcTask = await _userMarketCapSubTaskRepository.AsQueryable().Where(it => it.Id == taskId && it.UId == uid)
            .FirstAsync();
        if (mcTask == null)
        {
            Logger.LogWarning($"收到市值管理任务:{taskId} {status} 没有找到记录");
            return;
        }

        Logger.LogDebug($"收到市值管理任务:{taskId} {status} {mcTask.TokenAddress} {mcTask.TokenName}");
        dict["mint"] = mcTask.TokenAddress;
        //状态 -1终止，0暂停，1运行
        if (status == 1)
        {
            await InitSubscribeStream(taskId);
            //发送 订阅 交易
            dict["status"] = "1";
        }
        else if (status == 0)
        {
            if (dictionaryLock.TryGetValue(taskId, out var cts))
            {
                await cts.CancelAsync();
            }

            //发送取消 订阅 交易
            //await dictionaryLock[taskId].CancelAsync();
            dict["status"] = "0";
        }
        else
        {
            if (dictionaryLock.TryGetValue(taskId, out var cts))
            {
                await cts.CancelAsync();
            }

            //删除队列
            var key = $"{RedisKey.MarketCapQueue}:{taskId}";
            var isexist = await _redisClient.ExistsAsync(key);
            if (isexist) await _redisClient.ExpireAsync(key, 0);
            //发送取消 订阅 交易
            dict["status"] = "0";
        }

        await _redisClient.XAddAsync(StreamKey.SolanaMonitorMintStream, dict);
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        subscribeStream =
            _redisClient.SubscribeStream(StreamKey.MarketCapActionStream, onMessage: OnRedisSubscribeStreamMessage);
        //订阅到代币交易
        _redisClient.Subscribe(RedisKey.SolanaMonitorMintSubscribe,
            async (s, o) =>
            {
                Logger.LogDebug($"收到 代币交易消息 开始处理交易{o}");
                if (o == null) return;

                //if (o.ToString().IndexOf("ping", StringComparison.Ordinal) > -1) return;
                var sw = JsonSerializer.Deserialize<SolanaMonitorMintSubscribe>(o.ToString(),
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });
                if (sw == null) return;
                if (!sw.IsBuy || sw.SolAmount == 0 || sw.TokenAmount == 0) return;

                var mcTask = await _userMarketCapTaskRepository.AsQueryable().Where(it => it.TokenAddress == sw.Mint)
                    .FirstAsync();
                if (mcTask == null) return;

                await _userMarketCapTaskRepository.CopyNew().AsUpdateable()
                    .SetColumns(it => it.CurrentAmountAll == it.CurrentAmountAll + sw.SolAmount)
                    .SetColumns(it =>
                        it.IsReCurrentAmountAll ==
                        SqlFunc.IIF(
                            it.CurrentAmountAll + sw.SolAmount >= it.AmountAll &&
                            SqlFunc.IsNull(it.AmountAll, 0) > 0, true, it.IsReCurrentAmountAll))
                    .Where(it => it.Id == mcTask.Id)
                    .ExecuteCommandAsync(cancellationToken);
                var amountAll = mcTask.AmountAll ?? 0;
                var isPause = sw.SolAmount + mcTask.CurrentAmountAll >= amountAll && amountAll > 0;
                //暂停所有子任务
                if (isPause)
                {
                    await _userMarketCapSubTaskRepository.CopyNew().AsUpdateable()
                        .SetColumns(it => it.Status == 0)
                        .Where(it => it.TaskId == mcTask.Id)
                        .ExecuteCommandAsync(cancellationToken);
                }

                if (mcTask.AmountType == 2)
                {
                    //清仓
                    Logger.LogDebug($"市值管理 {mcTask.TokenAddress}  达到{mcTask.AmountAll} 执行清仓 任务");
                    var dict = new Dictionary<string, string>();
                }

                //更新所有子任务 外部买 
                /*await _userMarketCapSubTaskRepository.CopyNew().AsUpdateable()
                    .SetColumns(it => it.CurrentAmountAll == it.CurrentAmountAll + sw.TokenAmount)
                    .SetColumns(it =>
                        it.Status ==
                        SqlFunc.IIF(
                            it.CurrentAmountAll + sw.TokenAmount >= it.AmountAll &&
                            SqlFunc.IsNull(it.CurrentAmountAll, 0) > 0, 0, it.Status))
                    .Where(it => it.TaskId == mcTask.Id /*&& it.IsOpen#1#)
                    .ExecuteCommandAsync(cancellationToken);*/
                /*var list = await _userMarketCapSubTaskRepository.AsQueryable()
                    .Where(it => it.TaskId == mcTask.Id && it.IsOpen).ToListAsync();
                if (list == null || list.Count <= 0) return;
                for (int i = 0; i < list.Count; i++)
                {
                    var item = list[i];
                    //暂停任务
                    if (item.CurrentAmountAll >= item.AmountAll)
                    {
                        if (dictionaryLock.ContainsKey(item.Id))
                        {
                            await dictionaryLock[item.Id].CancelAsync();
                            Logger.LogDebug($"市值管理 {item.TokenAddress} 外部买入 达到目标金额 {item.CurrentAmountAll}停止任务");
                        }
                    }
                }*/
            });

        await InitSubscribeStream();

        Logger.LogDebug($"市值管理 服务 启动");
        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        subscribeStream.Dispose();
        Logger.LogDebug($"市值管理 服务 停止");
        await base.StopAsync(cancellationToken);
    }
}