
using System.ComponentModel;

namespace Tron.Abp.Domain.Contracts.Enum;
[Description("钱包类型")]
public enum WalletEnum
{
    /// <summary>
    /// 手动打款
    /// </summary>
    [Description("手动打款")]
    HAND=-1,
    /// <summary>
    /// 波场
    /// </summary>
    [Description("波场")]
    TRC20,
    /// <summary>
    /// 以太坊
    /// </summary>
    [Description("以太坊")]
    ERC20,
    /// <summary>
    /// 万维巴西
    /// </summary>
    [Description("万维巴西")]
    WWWPAGO,
    /// <summary>
    /// 福盈国际
    /// </summary>
    [Description("福盈国际")]
    BUGAQ,
    /// <summary>
    /// BBNPAY
    /// </summary>
    [Description("BBNPAY")]
    BBNPAY
}