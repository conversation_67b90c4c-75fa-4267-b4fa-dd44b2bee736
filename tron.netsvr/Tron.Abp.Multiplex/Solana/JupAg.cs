using System.Diagnostics;
using System.Text.Json;
using Flurl.Http;
using Newtonsoft.Json;
using Tron.Abp.Multiplex.Solana.Dto;

namespace Tron.Abp.Multiplex.Solana;

partial class SolanaApi
{
    /// <summary>
    /// 获取代币价格 来自木星API sol
    /// </summary>
    /// <param name="mint"></param>
    /// <returns></returns>
    private async Task<decimal> GetMintPriceByJupiterAsync(string mint)
    {
        var result = await "https://lite-api.jup.ag"
            .WithHeader("accept", "application/json")
            .WithHeader("origin", "https://jup.ag/")
            .WithHeader("referer", "https://jup.ag/")
            .WithHeader("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
            .AppendPathSegment($"/price/v2")
            .SetQueryParam("ids", mint)
            .SetQueryParam("vsToken", "So11111111111111111111111111111111111111112")
            .GetStringAsync();
        if (string.IsNullOrWhiteSpace(result)) return 0m;
        using JsonDocument doc = JsonDocument.Parse(result);
        JsonElement root = doc.RootElement;
        if (doc.RootElement.TryGetProperty("data", out JsonElement dataElement) &&
            dataElement.TryGetProperty(mint, out JsonElement tokenElement) &&
            tokenElement.TryGetProperty("price", out JsonElement priceElement))
        {
            return priceElement.GetDecimal(); // 如果 price 为 null，此处返回 null
        }

        return 0m;
    }

    /// <summary>
    /// 获取代币价格 来自木星API usd
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public async Task<JupAgTokenPrice> GetTokenPriceByJup(string address)
    {
        try
        {
            var res = await "https://lite-api.jup.ag"
                .WithHeader("accept", "application/json")
                .WithHeader("origin", "https://jup.ag/")
                .WithHeader("referer", "https://jup.ag/")
                .WithHeader("User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .AppendPathSegment($"/price/v2")
                .WithTimeout(5 * 1000)
                .SetQueryParam("ids", address)
                .GetStringAsync();
            if (!string.IsNullOrWhiteSpace(res))
            {
                var data = JsonConvert.DeserializeObject<JupAgTokenPrice>(res);
                return data;
            }
        }
        catch (Exception e)
        {
            Debug.WriteLine($"请求jupapi失败: {e}");
        }

        return null;
    }

    public async Task<JupAgToken> GetTokenInfoByJup(string address)
    {
        try
        {
            var res = await "https://lite-api.jup.ag"
                .WithHeader("accept", "application/json")
                .WithHeader("origin", "https://jup.ag/")
                .WithHeader("referer", "https://jup.ag/")
                .WithHeader("User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .AppendPathSegment($"/tokens/v1/token/{address}")
                .WithTimeout(5 * 1000)
                .GetStringAsync();
            if (!string.IsNullOrWhiteSpace(res))
            {
                var data = JsonConvert.DeserializeObject<JupAgToken>(res);
                return data;
            }
        }
        catch (Exception e)
        {
            Debug.WriteLine($"请求jupapi失败: {e}");
        }

        return null;
    }

    public async Task GetPoolInfoByJup(string address)
    {
        try
        {
            var res = await "https://datapi.jup.ag/"
                .WithHeader("accept", "application/json")
                .WithHeader("origin", "https://jup.ag/")
                .WithHeader("referer", "https://jup.ag/")
                .WithHeader("User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .AppendPathSegment($"/v1/pools")
                .SetQueryParam("assetIds", address)
                .WithTimeout(5 * 1000)
                .GetStringAsync();
            if (!string.IsNullOrWhiteSpace(res))
            {
            }
        }
        catch (Exception e)
        {
            Debug.WriteLine($"请求jupapi失败: {e}");
        }

        // return null;
    }
}