using Solnet.AABot.RaydiumCpmm;
using Tron.Abp.Multiplex.Contracts;

namespace Tron.Abp.Multiplex.Solana;

partial class SolanaApi
{
    #region RaydiumCpmm

    public async Task<(bool Successful, string Signature, string ErrorMsg)> RaydiumCpmmSwapIn(
        Solnet.Wallet.Account account,
        string mint, decimal amount,
        decimal fee = 0.0001m, decimal mev = 0, decimal slippage = 0, string poolId = "", decimal tokenPrice = 0,
        bool isRise = false)
    {
        Solnet.AABot.RaydiumCpmm.IRaydiumCpmm raydiumCpmm =
            new RaydiumCpmmImpl(_rpcClient, _zeroSlotRpcClient, account, Logger);
        string[] accountAddresses = null;
        //池子ID为空 重新获取池子ID
        if (string.IsNullOrWhiteSpace(poolId)) poolId = await GetMintPoolId(mint);
        if (await _redisClient.HExistsAsync(RedisKey.SolanaRaydiumCpmmPool, mint))
        {
            var ammInfoDict =
                await _redisClient.HGetAsync<Dictionary<string, Tron.Abp.Multiplex.Contracts.RaydiumCpmmPool>>(
                    RedisKey.SolanaRaydiumCpmmPool, mint);
            if (ammInfoDict.TryGetValue(poolId, out var ammInfo))
            {
                accountAddresses = new string[]
                {
                    ammInfo.CpmmPool,
                    ammInfo.Token0Vault,
                    ammInfo.Token1Vault,
                    ammInfo.Token0Program,
                    ammInfo.Token1Program,
                    ammInfo.Token0Mint,
                    ammInfo.Token1Mint,
                    ammInfo.ObservationKey,
                };
            }
        }

        var blockHash = await _redisClient.GetAsync(RedisKey.SolanaBlockHash);
        var result =
            await raydiumCpmm.SwapInAsync(mint, amount, slippage, (decimal)fee, mev, accountAddresses, tokenPrice,
                isRise: isRise, blockHash);
        return result;
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> RaydiumCpmmSwapOut(
        Solnet.Wallet.Account account,
        string mint, decimal percentage,
        decimal fee = 0.0001m, decimal mev = 0, decimal slippage = 0, string poolId = "", decimal tokenPrice = 0)
    {
        Solnet.AABot.RaydiumCpmm.IRaydiumCpmm raydiumCpmm =
            new RaydiumCpmmImpl(_rpcClient, _zeroSlotRpcClient, account, Logger);
        string[] accountAddresses = null;
        //池子ID为空 重新获取池子ID
        if (string.IsNullOrWhiteSpace(poolId)) poolId = await GetMintPoolId(mint);
        if (await _redisClient.HExistsAsync(RedisKey.SolanaRaydiumCpmmPool, mint))
        {
            var ammInfoDict =
                await _redisClient.HGetAsync<Dictionary<string, Tron.Abp.Multiplex.Contracts.RaydiumCpmmPool>>(
                    RedisKey.SolanaRaydiumCpmmPool, mint);
            if (ammInfoDict.TryGetValue(poolId, out var ammInfo))
            {
                accountAddresses = new string[]
                {
                    ammInfo.CpmmPool,
                    ammInfo.Token0Vault,
                    ammInfo.Token1Vault,
                    ammInfo.Token0Program,
                    ammInfo.Token1Program,
                    ammInfo.Token0Mint,
                    ammInfo.Token1Mint,
                    ammInfo.ObservationKey,
                };
            }
        }

        var blockHash = await _redisClient.GetAsync(RedisKey.SolanaBlockHash);
        var result =
            await raydiumCpmm.SwapOutByPercentAsync(mint, percentage, slippage, (decimal)fee, mev, accountAddresses,
                tokenPrice, blockHash);
        return result;
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> RaydiumCpmmSwapOutAmount(
        Solnet.Wallet.Account account, string mint, decimal tokenAmount,
        decimal fee = 0.0001m, decimal mev = 0, decimal slippage = 0, string poolId = "", decimal tokenPrice = 0)
    {
        Solnet.AABot.RaydiumCpmm.IRaydiumCpmm raydiumCpmm =
            new RaydiumCpmmImpl(_rpcClient, _zeroSlotRpcClient, account, Logger);
        string[] accountAddresses = null;
        //池子ID为空 重新获取池子ID
        if (string.IsNullOrWhiteSpace(poolId)) poolId = await GetMintPoolId(mint);
        if (await _redisClient.HExistsAsync(RedisKey.SolanaRaydiumCpmmPool, mint))
        {
            var ammInfoDict =
                await _redisClient.HGetAsync<Dictionary<string, Tron.Abp.Multiplex.Contracts.RaydiumCpmmPool>>(
                    RedisKey.SolanaRaydiumCpmmPool, mint);
            if (ammInfoDict.TryGetValue(poolId, out var ammInfo))
            {
                accountAddresses = new string[]
                {
                    ammInfo.CpmmPool,
                    ammInfo.Token0Vault,
                    ammInfo.Token1Vault,
                    ammInfo.Token0Program,
                    ammInfo.Token1Program,
                    ammInfo.Token0Mint,
                    ammInfo.Token1Mint,
                    ammInfo.ObservationKey,
                };
            }
        }

        var blockHash = await _redisClient.GetAsync(RedisKey.SolanaBlockHash);
        var result =
            await raydiumCpmm.SwapOutAsync(mint, tokenAmount, slippage, (decimal)fee, mev, accountAddresses,
                tokenPrice, blockHash);
        return result;
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> RaydiumCpmmBuySell(
        Solnet.Wallet.Account account, string mint, decimal amount, decimal slippage = 0, decimal fee = 0.0001m,
        decimal mev = 0)
    {
        var blockHash = await _redisClient.GetAsync(RedisKey.SolanaBlockHash);
        Solnet.AABot.RaydiumCpmm.IRaydiumCpmm raydiumCpmm =
            new RaydiumCpmmImpl(_rpcClient, _zeroSlotRpcClient, account, Logger);
        var result = await raydiumCpmm.BuySell(mint, amount, slippage, fee, mev, blockHash);
        return result;
    }

    #endregion

    #region 使用jito

    public async Task<(bool Successful, string Signature, string ErrorMsg)> RaydiumCpmmSwapIn(
        Solnet.Wallet.Account account, string mint, decimal amount, decimal fee = 0.0001m, decimal mev = 0,
        decimal slippage = 0, string poolId = "", decimal tokenPrice = 0,
        bool isRise = false, bool isMev = false, List<int> mevList = null)
    {
        Solnet.AABot.IAABot raydiumCpmm = new RaydiumCpmmImpl(_rpcClient, account, Logger);
        string[] accountAddresses = null;
        //池子ID为空 重新获取池子ID
        if (string.IsNullOrWhiteSpace(poolId)) poolId = await GetMintPoolId(mint);
        if (await _redisClient.HExistsAsync(RedisKey.SolanaRaydiumCpmmPool, mint))
        {
            var ammInfoDict =
                await _redisClient.HGetAsync<Dictionary<string, Tron.Abp.Multiplex.Contracts.RaydiumCpmmPool>>(
                    RedisKey.SolanaRaydiumCpmmPool, mint);
            if (ammInfoDict.TryGetValue(poolId, out var ammInfo))
            {
                accountAddresses = new string[]
                {
                    ammInfo.CpmmPool,
                    ammInfo.Token0Vault,
                    ammInfo.Token1Vault,
                    ammInfo.Token0Program,
                    ammInfo.Token1Program,
                    ammInfo.Token0Mint,
                    ammInfo.Token1Mint,
                    ammInfo.ObservationKey,
                };
            }
        }

        var blockHash = await _redisClient.GetAsync(RedisKey.SolanaBlockHash);
        var instructions =
            await raydiumCpmm.GetBuy(mint, amount, slippage, (decimal)fee, mev, accountAddresses, tokenPrice,
                isRise: isRise, blockHash);
        var lookupTableAddress =
            isDev
                ? Solnet.AABot.RaydiumCpmm.RaydiumCpmmProgram.DevLookupTableAddress
                : Solnet.AABot.RaydiumCpmm.RaydiumCpmmProgram.LookupTableAddress;
        return await Trade_Send(instructions, account, lookupTableAddress, mev, blockHash, isMev, mevList);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> RaydiumCpmmSwapOut(
        Solnet.Wallet.Account account, string mint, decimal percentage, decimal fee = 0.0001m, decimal mev = 0,
        decimal slippage = 0, string poolId = "", decimal tokenPrice = 0, bool isMev = false, List<int> mevList = null)
    {
        Solnet.AABot.IAABot raydiumCpmm = new RaydiumCpmmImpl(_rpcClient, account, Logger);
        string[] accountAddresses = null;
        //池子ID为空 重新获取池子ID
        if (string.IsNullOrWhiteSpace(poolId)) poolId = await GetMintPoolId(mint);
        if (await _redisClient.HExistsAsync(RedisKey.SolanaRaydiumCpmmPool, mint))
        {
            var ammInfoDict =
                await _redisClient.HGetAsync<Dictionary<string, Tron.Abp.Multiplex.Contracts.RaydiumCpmmPool>>(
                    RedisKey.SolanaRaydiumCpmmPool, mint);
            if (ammInfoDict.TryGetValue(poolId, out var ammInfo))
            {
                accountAddresses = new string[]
                {
                    ammInfo.CpmmPool,
                    ammInfo.Token0Vault,
                    ammInfo.Token1Vault,
                    ammInfo.Token0Program,
                    ammInfo.Token1Program,
                    ammInfo.Token0Mint,
                    ammInfo.Token1Mint,
                    ammInfo.ObservationKey,
                };
            }
        }

        var blockHash = await _redisClient.GetAsync(RedisKey.SolanaBlockHash);
        var instructions =
            await raydiumCpmm.GetSellPercentage(mint, percentage, slippage, (decimal)fee, mev, accountAddresses,
                tokenPrice, blockHash);
        var lookupTableAddress =
            isDev
                ? Solnet.AABot.RaydiumCpmm.RaydiumCpmmProgram.DevLookupTableAddress
                : Solnet.AABot.RaydiumCpmm.RaydiumCpmmProgram.LookupTableAddress;
        return await Trade_Send(instructions, account, lookupTableAddress, mev, blockHash, isMev, mevList);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> RaydiumCpmmBuySell(
        Solnet.Wallet.Account account, string mint, decimal amount, decimal slippage = 0, decimal fee = 0.0001m,
        decimal mev = 0, bool isMev = false, List<int> mevList = null)
    {
        var blockHash = await _redisClient.GetAsync(RedisKey.SolanaBlockHash);
        Solnet.AABot.IAABot raydiumCpmm = new RaydiumCpmmImpl(_rpcClient, account, Logger);
        var instructions = await raydiumCpmm.GetBuySell(mint, amount, slippage, fee, mev, null, blockHash);
        var lookupTableAddress =
            isDev
                ? Solnet.AABot.RaydiumCpmm.RaydiumCpmmProgram.DevLookupTableAddress
                : Solnet.AABot.RaydiumCpmm.RaydiumCpmmProgram.LookupTableAddress;
        return await Trade_Send(instructions, account, lookupTableAddress, mev, blockHash, isMev, mevList);
    }

    #endregion
}