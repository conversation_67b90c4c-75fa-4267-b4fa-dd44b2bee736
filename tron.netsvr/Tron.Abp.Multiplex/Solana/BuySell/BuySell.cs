using Solnet.Wallet;
using Tron.Abp.Multiplex.Contracts;

namespace Tron.Abp.Multiplex.Solana;

partial class SolanaApi
{
    /// <summary>
    /// 买入
    /// </summary>
    /// <param name="dexType"></param>
    /// <param name="account"></param>
    /// <param name="mint"></param>
    /// <param name="amount"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> Buy(string dexType,
        Solnet.Wallet.Account account, string mint, decimal amount,
        decimal slippage = 10, decimal fee = 0.0001m, decimal mev = 0, string poolId = "", decimal tokenPrice = 0,
        bool isRise = false, bool isMev = false, List<int> mevList = null)
    {
        var (successful, signature, errormsg) = dexType switch
        {
            DexType.Pumpfun => await <PERSON><PERSON><PERSON><PERSON><PERSON>uy(account, mint, amount, slippage, fee, mev, tokenPrice, isRise, isMev,
                mevList),
            DexType.RaydiumAmm => await RaydiumBuy(account, mint, amount, fee, mev, slippage, poolId, tokenPrice,
                isRise, isMev, mevList),
            DexType.PumpSwapAmm => await PumpSwapBuy(account, mint, amount, fee, mev, slippage, poolId, tokenPrice,
                isRise, isMev, mevList),
            DexType.RaydiumCpmm => await RaydiumCpmmSwapIn(account, mint, amount, fee, mev, slippage, poolId,
                tokenPrice, isRise, isMev, mevList),
            DexType.Launchpad => await LaunchpadBuy(account, mint, amount, fee, mev, slippage, poolId, tokenPrice,
                isRise, isMev, mevList),
            DexType.RaydiumClmm => await RaydiumClmmBuy(account, mint, amount, fee, mev, slippage, poolId, tokenPrice,
                isRise, isMev, mevList),
            DexType.MeteoraDBC => await MeteoraBuy(account, mint, amount, fee, mev, slippage, poolId, tokenPrice,
                isRise, isMev, mevList),
            DexType.MeteoraDlmm => await MeteoraDlmmBuy(account, mint, amount, fee, mev, slippage, poolId, tokenPrice,
                isRise, isMev, mevList),
            DexType.MeteoraDYN => await MeteoraDynBuy(account, mint, amount, fee, mev, slippage, poolId, tokenPrice,
                isRise, isMev, mevList),
            _ => (false, string.Empty, $"无效的交易类型:{dexType}")
        };
        return (successful, signature, errormsg);
    }

    /// <summary>
    /// 多买
    /// </summary>
    /// <param name="dexType"></param>
    /// <param name="player"></param>
    /// <param name="mint"></param>
    /// <param name="sol"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="tokenPrice"></param>
    /// <param name="isRise"></param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> MultipleBuy(string dexType,
        List<(string, decimal)> player, string mint,
        decimal sol, decimal slippage, decimal fee = 0, decimal mev = 0, decimal tokenPrice = 0, bool isRise = false)
    {
        var (successful, signature, errormsg) = dexType switch
        {
            DexType.Pumpfun => await PumpMultipleBuy(dexType, player, mint, sol, slippage, fee, mev, tokenPrice,
                isRise),
            _ => (false, string.Empty, string.Empty)
        };
        return (successful, signature, errormsg);
    }

    /// <summary>
    /// 给其他用户买入
    /// </summary>
    /// <param name="dexType"></param>
    /// <param name="account"></param>
    /// <param name="mint"></param>
    /// <param name="amount"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="toPublicKey"></param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> BuyToPublick(string dexType,
        Solnet.Wallet.Account account, string mint, decimal amount,
        decimal slippage = 10, decimal fee = 0.0001m, decimal mev = 0, string toPublicKey = "")
    {
        var (successful, signature, errormsg) = dexType switch
        {
            DexType.Pumpfun => await PumpFunBuyToUser(account, mint, amount, slippage, fee, mev, toPublicKey),
            _ => (false, string.Empty, string.Empty)
        };
        return (successful, signature, errormsg);
    }


    /// <summary>
    /// 卖出 百分比
    /// </summary>
    /// <param name="dexType"></param>
    /// <param name="account"></param>
    /// <param name="mint"></param>
    /// <param name="percentage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="slippage"></param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> Sell(string dexType,
        Solnet.Wallet.Account account, string mint, decimal percentage,
        decimal fee = 0.0001m, decimal mev = 0, decimal slippage = 10, string poolId = "", decimal tokenPrice = 0,
        bool isMev = false, List<int> mevList = null)
    {
        var (successful, signature, errormsg) = dexType switch
        {
            DexType.Pumpfun => await PumpFunSell(account, mint, percentage, fee, mev, slippage, tokenPrice, isMev,
                mevList),
            DexType.RaydiumAmm => await RaydiumSell(account, mint, percentage, fee, mev, slippage, poolId, tokenPrice,
                isMev, mevList),
            DexType.PumpSwapAmm => await PumpSwapSell(account, mint, percentage, fee, mev, slippage, poolId, tokenPrice,
                isMev, mevList),
            DexType.RaydiumCpmm => await RaydiumCpmmSwapOut(account, mint, percentage, fee, mev, slippage, poolId,
                tokenPrice, isMev, mevList),
            DexType.Launchpad => await LaunchpadSell(account, mint, percentage, fee, mev, slippage, poolId, tokenPrice,
                isMev, mevList),
            DexType.RaydiumClmm => await RaydiumClmmSell(account, mint, percentage, fee, mev, slippage, poolId,
                tokenPrice, isMev, mevList),
            DexType.MeteoraDBC => await MeteoraSellPercentage(account, mint, percentage, fee, mev, slippage, poolId,
                tokenPrice, isMev, mevList),
            DexType.MeteoraDlmm => await MeteoraDlmmSellPercentage(account, mint, percentage, fee, mev, slippage,
                poolId, tokenPrice, isMev, mevList),
            DexType.MeteoraDYN => await MeteoraDynSellPercentage(account, mint, percentage, fee, mev, slippage, poolId,
                tokenPrice, isMev, mevList),
            _ => (false, string.Empty, $"无效的交易类型:{dexType}")
        };
        return (successful, signature, errormsg);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> SellAmount(string dexType,
        Solnet.Wallet.Account account, string mint,
        decimal tokenAmount,
        decimal fee = 0.0001m, decimal mev = 0, decimal slippage = 10, string poolId = "", decimal tokenPrice = 0)
    {
        var (successful, signature, errormsg) = dexType switch
        {
            DexType.Pumpfun => await PumpFunSellAmount(account, mint, tokenAmount, fee, mev, slippage, tokenPrice),
            DexType.RaydiumAmm => await RaydiumSellAmount(account, mint, tokenAmount, fee, mev, slippage, poolId,
                tokenPrice),
            DexType.PumpSwapAmm => await PumpSwapSellAmount(account, mint, tokenAmount, fee, mev, slippage, poolId,
                tokenPrice),
            DexType.RaydiumCpmm => await RaydiumCpmmSwapOutAmount(account, mint, tokenAmount, fee, mev, slippage,
                poolId, tokenPrice),
            DexType.Launchpad => await LaunchpadSellAmount(account, mint, tokenAmount, fee, mev, slippage, poolId,
                tokenPrice),
            DexType.RaydiumClmm => await RaydiumClmmSellAmount(account, mint, tokenAmount, fee, mev, slippage, poolId,
                tokenPrice),
            DexType.MeteoraDBC => await MeteoraSellAmount(account, mint, tokenAmount, fee, mev, slippage, poolId,
                tokenPrice),
            DexType.MeteoraDlmm => await MeteoraDlmmSellAmount(account, mint, tokenAmount, fee, mev, slippage, poolId,
                tokenPrice),
            DexType.MeteoraDYN => await MeteoraDynSellAmount(account, mint, tokenAmount, fee, mev, slippage, poolId,
                tokenPrice),
            _ => (false, string.Empty, $"无效的交易类型:{dexType}")
        };
        return (successful, signature, errormsg);
    }


    public async Task<(bool Successful, string Signature, string ErrorMsg)> BuySell(string dexType, Account account,
        string mint, decimal amount, decimal slippage = 10, decimal fee = 0.0001m,
        decimal mev = 0, bool isMev = false, List<int> mevList = null)
    {
        var (successful, signature, errormsg) = dexType switch
        {
            DexType.Pumpfun => await PumpFunBuySell(account, mint, amount, slippage, fee, mev, isMev, mevList),
            DexType.Launchpad => await LaunchpadBuySell(account, mint, amount, slippage, fee, mev, isMev, mevList),
            DexType.MeteoraDBC => await MeteoraDBCBuySell(account, mint, amount, slippage, fee, mev, isMev, mevList),
            DexType.PumpSwapAmm => await PumpSwapBuySell(account, mint, amount, slippage, fee, mev, isMev, mevList),
            DexType.RaydiumAmm => await RaydiumBuySell(account, mint, amount, slippage, fee, mev, isMev, mevList),
            DexType.RaydiumClmm => await RaydiumClmmBuySell(account, mint, amount, slippage, fee, mev, isMev, mevList),
            DexType.RaydiumCpmm => await RaydiumCpmmBuySell(account, mint, amount, slippage, fee, mev, isMev, mevList),
            DexType.MeteoraDlmm => await MeteoraDlmmBuySell(account, mint, amount, slippage, fee, mev, isMev, mevList),
            DexType.MeteoraDYN => await MeteoraDynBuySell(account, mint, amount, slippage, fee, mev, isMev, mevList),
            _ => (false, string.Empty, $"无效的交易类型:{dexType}")
        };
        return (successful, signature, errormsg);
    }
}