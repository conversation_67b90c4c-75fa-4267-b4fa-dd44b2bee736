using Solnet.AABot.Pumpfun;
using Solnet.Pumpfun;
using Solnet.Wallet;
using Tron.Abp.Multiplex.Contracts;

namespace Tron.Abp.Multiplex.Solana;

partial class SolanaApi
{
    #region PumpFun

    /// <summary>
    /// 
    /// </summary>
    /// <param name="account"></param>
    /// <param name="mint"></param>
    /// <param name="amount"></param>
    /// <param name="slippage"> 滑点</param>
    /// <param name="fee">Gas</param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> PumpFunBuy(Solnet.Wallet.Account account,
        string mint, decimal amount,
        decimal slippage = 10, decimal fee = 0.0001m, decimal mev = 0, decimal tokenPrice = 0, bool isRise = false)
    {
        var blockHash = await _redisClient.GetAsync(RedisKey.SolanaBlockHash);
        Solnet.AABot.Pumpfun.IPumpFun pumpFun = new PumpFunImpl(_rpcClient, _zeroSlotRpcClient, account, Logger);
        var sign = await pumpFun.Buy(mint, amount, slippage, (decimal)fee, mev, tokenPrice, isRise, blockHash);
        return sign;
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="account"></param>
    /// <param name="mint"></param>
    /// <param name="amount"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="toPublick"></param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> PumpFunBuyToPublick(
        Solnet.Wallet.Account account, string mint, decimal amount, decimal slippage = 10, decimal fee = 0.0001m,
        decimal mev = 0, string toPublick = "")
    {
        var pumpfun = new PumpfunClient(_rpcClient, account);
        return await pumpfun.BuyToPublicKey(mint, amount, slippage, toPublick, fee, mev);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="account"></param>
    /// <param name="mint"></param>
    /// <param name="percentage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="slippage"></param>
    /// <param name="tokenPrice"></param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> PumpFunSell(Solnet.Wallet.Account account,
        string mint, decimal percentage, decimal fee = 0.0001m, decimal mev = 0, decimal slippage = 10,
        decimal tokenPrice = 0)
    {
        var blockHash = await _redisClient.GetAsync(RedisKey.SolanaBlockHash);
        Solnet.AABot.Pumpfun.IPumpFun pumpFun = new PumpFunImpl(_rpcClient, _zeroSlotRpcClient, account, Logger);
        var sign = await pumpFun.SellPercentage(mint, percentage, slippage, (decimal)fee, mev, tokenPrice, blockHash);
        return sign;
    }


    public async Task<(bool Successful, string Signature, string ErrorMsg)> PumpFunSellAmount(
        Solnet.Wallet.Account account, string mint, decimal tokenAmount,
        decimal fee = 0.0001m, decimal mev = 0, decimal slippage = 10, decimal tokenPrice = 0)
    {
        /*if (percentage == 100)
        {
            PublicKey associatedUser =
                AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account, new PublicKey(mint));
            var tokenbalance = await _rpcClient.GetTokenAccountBalanceAsync(associatedUser);

            if (!(tokenbalance.WasHttpRequestSuccessful && tokenbalance?.Result?.Value?.AmountDecimal > 0))
            {
                Console.WriteLine($"{account.PublicKey} {mint} 没有持仓");
                return string.Empty;
            }
        }*/

        Solnet.AABot.Pumpfun.IPumpFun pumpFun = new PumpFunImpl(_rpcClient, _zeroSlotRpcClient, account, Logger);
        var sign = await pumpFun.Sell(mint, tokenAmount, slippage, (decimal)fee, mev, tokenPrice);
        return sign;
        /*
        Solnet.Pumpfun.PumpfunClient client = new PumpfunClient(_rpcClient, account);
        PublicKey associatedUser =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account, new PublicKey(mint));
        var tokenbalance = await client.RpcClient.GetTokenAccountBalanceAsync(associatedUser);
        if (!tokenbalance.WasSuccessful) return null;
        var tokenAmount = tokenbalance.Result.Value.AmountDecimal * percentage / 100;
        var cu = 1_000_000ul;
        fee = fee == 0 ? 0.000005 : fee;
        var microLamports = (ulong)((fee - 0.000005) * (Math.Pow(10, 15) / cu));
        var jito = (ulong)(mev * 1e9m);
        var result = await client.Sell(mint, tokenAmount, 0, cu, microLamports, jito, "");
        Debug.WriteLine(result);
        LogInfo("PumpFunSell", result);
        return result;
        */
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> PumpMultipleBuy(string dexType,
        List<(string, decimal)> player,
        string mint,
        decimal sol, decimal slippage, decimal fee = 0, decimal mev = 0, decimal tokenPrice = 0, bool isRise = false)
    {
        Account account = Account.FromSecretKey(player[0].Item1);
        var accountList = player[1..].ToList().Select(it => (Account.FromSecretKey(it.Item1), it.Item2)).ToList();

        Solnet.AABot.Pumpfun.IPumpFun pumpFun = new PumpFunImpl(_rpcClient, _zeroSlotRpcClient, account, Logger);
        return await pumpFun.MultipleBuy(accountList, mint, sol, slippage, fee, mev, tokenPrice, isRise);
    }

    /// <summary>
    /// 不使用
    /// </summary>
    /// <param name="account"></param>
    /// <param name="mint"></param>
    /// <param name="amount"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> PumpFunBuySell(
        Solnet.Wallet.Account account,
        string mint, decimal amount,
        decimal slippage = 10, decimal fee = 0.0001m, decimal mev = 0)
    {
        var blockHash = await _redisClient.GetAsync(RedisKey.SolanaBlockHash);
        Solnet.AABot.Pumpfun.IPumpFun pumpFun = new PumpFunImpl(_rpcClient, _zeroSlotRpcClient, account, Logger);
        var sign = await pumpFun.BuySell(mint, amount, slippage, (decimal)fee, mev, blockHash);
        return sign;
    }

    #endregion

    #region 使用jito

    /// <summary>
    /// 使用jito
    /// </summary>
    /// <param name="account"></param>
    /// <param name="mint"></param>
    /// <param name="amount"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="tokenPrice"></param>
    /// <param name="isRise"></param>
    /// <param name="isMev"></param>
    /// <param name="mevList"></param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> PumpFunBuy(Solnet.Wallet.Account account,
        string mint, decimal amount, decimal slippage = 10, decimal fee = 0.0001m, decimal mev = 0,
        decimal tokenPrice = 0, bool isRise = false, bool isMev = false, List<int> mevList = null)
    {
        var blockHash = await _redisClient.GetAsync(RedisKey.SolanaBlockHash);
        Solnet.AABot.IAABot pumpFun = new PumpFunImpl(_rpcClient, account, Logger);
        var lookupTableAddress =isDev? Solnet.AABot.Pumpfun.PumpfunProgram.DevLookupTableAddress: Solnet.AABot.Pumpfun.PumpfunProgram.LookupTableAddress;
        var instructions = await pumpFun.GetBuy(mint, amount, slippage, fee, mev, null, tokenPrice, isRise, blockHash);
        return await Trade_Send(instructions, account, lookupTableAddress, mev, blockHash, isMev, mevList);
    }

    /// <summary>
    /// 使用jito
    /// </summary>
    /// <param name="account"></param>
    /// <param name="mint"></param>
    /// <param name="percentage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="slippage"></param>
    /// <param name="tokenPrice"></param>
    /// <param name="isMev"></param>
    /// <param name="mevList"></param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> PumpFunSell(Solnet.Wallet.Account account,
        string mint, decimal percentage, decimal fee = 0.0001m, decimal mev = 0, decimal slippage = 10,
        decimal tokenPrice = 0, bool isMev = false, List<int> mevList = null)
    {
        var blockHash = await _redisClient.GetAsync(RedisKey.SolanaBlockHash);
        Solnet.AABot.IAABot pumpFun = new PumpFunImpl(_rpcClient, account, Logger);
        var instructions =
            await pumpFun.GetSellPercentage(mint, percentage, slippage, (decimal)fee, mev, null, tokenPrice, blockHash);
        var lookupTableAddress =isDev? Solnet.AABot.Pumpfun.PumpfunProgram.DevLookupTableAddress: Solnet.AABot.Pumpfun.PumpfunProgram.LookupTableAddress;
        return await Trade_Send(instructions, account, lookupTableAddress, mev, blockHash, isMev, mevList);
    }

    /// <summary>
    /// 使用 jito
    /// </summary>
    /// <param name="account"></param>
    /// <param name="mint"></param>
    /// <param name="amount"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="isMev"></param>
    /// <param name="mevList"></param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> PumpFunBuySell(
        Solnet.Wallet.Account account, string mint, decimal amount, decimal slippage = 10, decimal fee = 0.0001m,
        decimal mev = 0,
        bool isMev = false, List<int> mevList = null)
    {
        var blockHash = await _redisClient.GetAsync(RedisKey.SolanaBlockHash);
        Solnet.AABot.IAABot pumpFun = new PumpFunImpl(_rpcClient, account, Logger);
        var lookupTableAddress =isDev? Solnet.AABot.Pumpfun.PumpfunProgram.DevLookupTableAddress: Solnet.AABot.Pumpfun.PumpfunProgram.LookupTableAddress;
        var instructions = await pumpFun.GetBuySell(mint, amount, slippage, (decimal)fee, mev, null, blockHash);
        return await Trade_Send(instructions, account, lookupTableAddress, mev, blockHash, isMev, mevList);
    }

    #endregion
}