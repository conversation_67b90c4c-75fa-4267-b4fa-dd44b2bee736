using System.Text.Json;
using Flurl;
using Flurl.Http;
using Microsoft.Extensions.Options;
using Volo.Abp.DependencyInjection;

namespace Tron.Abp.Multiplex.SolanaIo;

public class SolanaIoApi:ISolanaIoApi,IScopedDependency
{
    private string BaseUrl = "https://pro-api.solscan.io";
    private SolanaIoOptions options;
    public SolanaIoApi(IOptions<SolanaIoOptions> options)
    {
        this.options=options.Value;
    }

    public async Task<Dto.TokenMeta> GetTokenMetaAsync(string mint)
    {
        var rep =await BaseUrl
            .WithHeader("content-Type", "application/json")
            .WithHeader("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
            .WithHeader("token", this.options.ApiKey)
            .AppendPathSegment("/v2.0/token/meta")
            .SetQueryParam("address",mint)
            .GetStringAsync();
        var result = JsonSerializer.Deserialize<Dto.TokenMeta>(rep);
        return result;
    }
    
}