<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
    <PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Tron.Abp.Application\Tron.Abp.Application.csproj" />
    <ProjectReference Include="..\Tron.Abp.Base\Tron.Abp.Base.csproj" />
    <ProjectReference Include="..\Tron.Abp.Core\Tron.Abp.Core.csproj" />
    <ProjectReference Include="..\Tron.Serilog.Base\Tron.Serilog.Base.csproj" />
  </ItemGroup>

</Project>
