// MIT 许可证
// 版权 © 2023-present https://github.com/dymproject/purest-admin作者 以及贡献者
// 作者或版权持有人都不对任何索赔、损害或其他责任负责，无论这些追责来自合同、侵权或其它行为中，
// 还是产生于、源于或有关于本软件以及本软件的使用或其它处置。

using FreeRedis;
using Microsoft.AspNetCore.Authorization;
using Tron.Abp.Core;
using Tron.Abp.Domain.Contracts.Enum;
using UGF.Abp.Core.Auth;
using Volo.Abp.Users;

namespace Tron.Abp.Api.Host.Options;
public class AuthorizationHandler : IAuthorizationHandler
{
    private readonly IHostEnvironment _hostEnvironment;
    private readonly ICurrentUser _currentUser;
    private readonly IRedisClient _redisClient;
    public AuthorizationHandler(IHostEnvironment hostEnvironment, ICurrentUser currentUser, IRedisClient redisClient)
    {
        _currentUser = currentUser;
        _redisClient = redisClient;
        _hostEnvironment = hostEnvironment;
    }
    public async Task HandleAsync(AuthorizationHandlerContext context)
    {
        //登录验证
        if (context.Resource is HttpContext httpContext)
        {
            var isAuthenticated = httpContext.User.Identity?.IsAuthenticated ?? false;
            if (!isAuthenticated)
            {
                context.GetCurrentHttpContext().Response.Headers["access-token"] = "invalid_token";
                context.Fail();
                return;
            }
            

            await AuthorizeHandleAsync(context);

        }
        await Task.CompletedTask;
    }
    private async Task AuthorizeHandleAsync(AuthorizationHandlerContext context)
    {
        // 获取所有未成功验证的需求
        var pendingRequirements = context.PendingRequirements;

        // 获取 HttpContext 上下文
        var httpContext = context.GetCurrentHttpContext();

        // 调用子类管道
        var pipeline = await PipelineAsync(context, httpContext);
        if (pipeline)
        {
            // 通过授权验证
            foreach (var requirement in pendingRequirements)
            {
                // 验证策略管道
                var policyPipeline = await PolicyPipelineAsync(context, httpContext, requirement);
                if (policyPipeline) context.Succeed(requirement);
                else context.Fail();
            }
        }
        else context.Fail();
    }

    private Task<bool> PolicyPipelineAsync(AuthorizationHandlerContext context, DefaultHttpContext httpContext, IAuthorizationRequirement requirement)
    {
        return Task.FromResult(true);
    }

    private async Task<bool> PipelineAsync(AuthorizationHandlerContext context, DefaultHttpContext httpContext)
    {
        var curAccountType = -1;
        if (int.TryParse(_currentUser.FindClaim(ClaimConst.AccountType).Value, out curAccountType))
            //单用户登录
            if (curAccountType == -1)
            {
                //long.TryParse(_currentUser.FindClaim(ClaimConst.UserId).Value, out long userId);
                var singleToken = _redisClient.HGet("SingleUserLogin", $"{_currentUser.UserName}");
                if (!string.IsNullOrEmpty(singleToken) && singleToken !=
                    $"{context.GetCurrentHttpContext().Request.Headers["Authorization"]}".Replace("Bearer ", ""))
                {
                    return false;
                }

                return true;
            }
        // 已自动验证 Jwt Token 有效性
        return await CheckAuthorzieAsync(httpContext);
    }
    private async Task<bool> CheckAuthorzieAsync(DefaultHttpContext httpContext)
    {

        var curAccountType = 0;
        if (int.TryParse(_currentUser.FindClaim(ClaimConst.AccountType).Value, out curAccountType))
        {
            // 排除超管
            if (curAccountType == ((int)AccountTypeEnum.SuperAdmin))
                return true;
        }


        // 路由/按钮名称
        var routeName = httpContext.Request.Path.Value[1..].Replace("/", ":");
        //todo:仅限未实现 2024.5.3
        return true;
        /*
        // 获取用户拥有按钮权限集合
        var ownBtnPermList = await App.GetService<SysMenuService>().GetOwnBtnPermList();
        // 获取系统所有按钮权限集合
        var allBtnPermList = await App.GetService<SysMenuService>().GetAllBtnPermList();

        // 已拥有该按钮权限或者所有按钮集合里面不存在
        var exist1 = ownBtnPermList.Exists(u => routeName.Contains(u, System.StringComparison.CurrentCultureIgnoreCase));
        var exist2 = allBtnPermList.TrueForAll(u => !routeName.Contains(u, System.StringComparison.CurrentCultureIgnoreCase));
        return exist1 || exist2;
        */
    }
}
