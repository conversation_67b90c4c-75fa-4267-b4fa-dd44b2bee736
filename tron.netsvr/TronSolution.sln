
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.9.34728.123
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Base", "Base", "{32A89FFA-A1E0-45A0-8D6D-AF0A7B5FB050}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "EventBusModule", "EventBusModule", "{C5459FFB-B2C1-4FA8-9109-D32AD14AF66C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solana", "Solana", "{BFC7F7B0-5AA7-46C7-84B5-1A3B0D321211}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tron.Abp.Base", "Tron.Abp.Base\Tron.Abp.Base.csproj", "{39F221EB-E694-4589-8D80-E058813EA3ED}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tron.Serilog.Base", "Tron.Serilog.Base\Tron.Serilog.Base.csproj", "{3CA76009-E57A-4C1F-855E-791A9236A928}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Jaina.EventBus", "Jaina.EventBus\Jaina.EventBus.csproj", "{4F15DECE-054F-44FB-A8BB-923EBF553903}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Module", "Module", "{B978361D-5F3C-4262-A358-2204984D0B9D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Solnet.Raydium", "Solnet.Raydium\Solnet.Raydium.csproj", "{AA534C87-5B38-4469-ABC9-8DBBC0EE9A84}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Solnet.Pumpfun", "Solnet.Pumpfun\Solnet.Pumpfun.csproj", "{42ADE700-745F-402D-A4AE-6AA89FD38C20}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "TestApp", "TestApp", "{65A23DA3-80CB-462F-939F-EDECC1F3591D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SolanaNetTest", "SolanaNetTest\SolanaNetTest.csproj", "{615FF5A5-170C-4757-97EE-1D8878DFF39B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Solnet.Metaplex", "Solnet.Metaplex\Solnet.Metaplex.csproj", "{7623B118-0E76-4A35-A418-4A46B4E17823}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tron.Abp.Caching.FreeRedis", "Tron.Abp.Caching.FreeRedis\Tron.Abp.Caching.FreeRedis.csproj", "{EEB9BD6E-25E6-43DF-947B-4F926640F38A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Api", "Api", "{B70703A4-2B97-431A-833E-75627ADAE329}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Db", "Db", "{5A9A3A55-1286-49CF-8F14-C90B9DA23573}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tron.Abp.SqlSugar", "Tron.Abp.SqlSugar\Tron.Abp.SqlSugar.csproj", "{3F4EC50B-F78E-4BFB-BE60-96DFE5A6B3CA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tron.Abp.Core", "Tron.Abp.Core\Tron.Abp.Core.csproj", "{477E122C-A1DB-4C2F-9548-6077742CA267}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tron.Abp.Multiplex.Contracts", "Tron.Abp.Multiplex.Contracts\Tron.Abp.Multiplex.Contracts.csproj", "{720AA338-686B-4398-B8BE-790286CF295A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tron.Abp.Multiplex", "Tron.Abp.Multiplex\Tron.Abp.Multiplex.csproj", "{C4B37EAE-C779-427F-BCC4-A09E56099AA8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tron.Abp.Domain.Contracts", "Tron.Abp.Domain.Contracts\Tron.Abp.Domain.Contracts.csproj", "{DF1F740A-AD9D-4ECD-AA81-8E46E1C613CA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tron.Abp.Domain", "Tron.Abp.Domain\Tron.Abp.Domain.csproj", "{EC71FDB6-A543-47F3-A8BD-59534D21202C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tron.Abp.Application.Contracts", "Tron.Abp.Application.Contracts\Tron.Abp.Application.Contracts.csproj", "{50C67ED0-262C-475C-AC29-06ACA2880599}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tron.Abp.Application", "Tron.Abp.Application\Tron.Abp.Application.csproj", "{B06740FB-729C-41BF-84A7-3AE0548B4324}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tron.Abp.Api.Host", "Tron.Abp.Api.Host\Tron.Abp.Api.Host.csproj", "{714BC9F9-A3AD-4DAF-92B6-78DECE0D33BB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.JobServer", "Tran.Abp.JobServer\Tran.Abp.JobServer.csproj", "{A3278B4D-83B8-4AEA-B094-D9768605F7B6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UpDataApp", "UpDataApp\UpDataApp.csproj", "{558C022C-A722-4AFA-879F-BD740BC829B7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SolanaConsoleApp", "SolanaConsoleApp\SolanaConsoleApp.csproj", "{2992D1AD-340C-450A-A813-8A10D0822FFE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Cryptography.Obfuscation", "Cryptography.Obfuscation\Cryptography.Obfuscation.csproj", "{FD20A917-BFDB-48B5-BE33-49F883C61F86}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solana_gRpcApp", "Solana_gRpcApp\Solana_gRpcApp.csproj", "{73A520EE-3355-4A91-9E2C-322DA368AAA4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.MonitorServer", "Tran.Abp.MonitorServer\Tran.Abp.MonitorServer.csproj", "{25FFA728-7BFD-421E-AD4A-6ECDFA2F8568}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.GRpcServer", "Tran.Abp.GRpcServer\Tran.Abp.GRpcServer.csproj", "{C1DDA791-C884-4E78-B8AF-3B41A0556339}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.EnhancedWsServer", "Tran.Abp.EnhancedWsServer\Tran.Abp.EnhancedWsServer.csproj", "{27970626-D873-4B20-9A85-A8F5E5CCFC4B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JainaRedisSrvA", "JainaRedisSrvA\JainaRedisSrvA.csproj", "{D3390DDB-A3E0-48C2-9703-4C6ED2A8A68B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JainaRedisSrvB", "JainaRedisSrvB\JainaRedisSrvB.csproj", "{8B34A05C-463C-4202-9C1A-EDED98234330}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JainaRedisSrvC", "JainaRedisSrvC\JainaRedisSrvC.csproj", "{D3BCFDBD-050C-45CF-BCF0-7E896A887767}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WsConsoleApp", "WsConsoleApp\WsConsoleApp.csproj", "{C455260F-B981-4C19-95D8-F046557AF2BB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.AABot", "Solnet.AABot\Solnet.AABot.csproj", "{444A267F-57D1-4644-B182-8FC8ADA58731}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Extensions", "Solnet\Solnet.Extensions\Solnet.Extensions.csproj", "{15B331B3-14B7-1540-9C31-E5A045CDF4C1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.KeyStore", "Solnet\Solnet.KeyStore\Solnet.KeyStore.csproj", "{7D4A6633-2430-1BFF-AA83-79CC931FB304}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Programs", "Solnet\Solnet.Programs\Solnet.Programs.csproj", "{54D965D6-16C2-FC88-4025-6A194626F544}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Rpc", "Solnet\Solnet.Rpc\Solnet.Rpc.csproj", "{BADC1AB0-8E6E-A216-AF68-FAAE9FB24B72}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Wallet", "Solnet\Solnet.Wallet\Solnet.Wallet.csproj", "{C0545405-6957-1FB4-9F95-697D6F9BDCA1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.Ws.PumpFun", "Tran.Abp.Ws.PumpFun\Tran.Abp.Ws.PumpFun.csproj", "{543F8749-A64E-4802-B292-DF364A12391C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Ws", "Ws", "{4ECAB070-03B0-4D25-92E2-62869313DD55}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.Ws.PumpSwap", "Tran.Abp.Ws.PumpSwap\Tran.Abp.Ws.PumpSwap.csproj", "{AB7EB35B-B522-4F01-8F11-5591C29654AD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.Ws.RaydiumAmm", "Tran.Abp.Ws.RaydiumAmm\Tran.Abp.Ws.RaydiumAmm.csproj", "{3A25EF63-B3D6-4305-9C52-6A51695726A7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.Ws.RaydiumClmm", "Tran.Abp.Ws.RaydiumClmm\Tran.Abp.Ws.RaydiumClmm.csproj", "{DE0DA5A5-AADF-4F58-923F-A06F056E339C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.Ws.Core", "Tran.Abp.Ws.Core\Tran.Abp.Ws.Core.csproj", "{EC82C69B-3573-4789-8C3F-FFBB1F8101B6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.Ws.NetCore", "Tran.Abp.Ws.NetCore\Tran.Abp.Ws.NetCore.csproj", "{3D2DDB0B-85C9-406E-BE32-217A7382A59C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.Ws.RaydiumCpmm", "Tran.Abp.Ws.RaydiumCpmm\Tran.Abp.Ws.RaydiumCpmm.csproj", "{D5D0D5AF-E330-4ED4-97F5-646A4522FF05}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.WrokTest", "Tran.Abp.WrokTest\Tran.Abp.WrokTest.csproj", "{D6D25A97-6C31-4B58-A94A-37F0ED88FF37}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solnet.Launchpad", "Solnet.Launchpad\Solnet.Launchpad.csproj", "{40584FE8-9ADB-4D07-A465-1A3F01BD7377}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.Ws.RaydiumLaunchpad", "Tran.Abp.Ws.RaydiumLaunchpad\Tran.Abp.Ws.RaydiumLaunchpad.csproj", "{62C72E76-9D36-44EE-851C-E619F700B2FA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AxiomConsoleApp", "AxiomConsoleApp\AxiomConsoleApp.csproj", "{A3FDD9F7-A5C7-43EE-9C15-E21559416BBE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Clmm.Test", "Clmm.Test\Clmm.Test.csproj", "{181F72B9-7AC0-4AC9-9659-74C341BF9419}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tron.Abp.BatchTradeServer", "Tron.Abp.BatchTradeServer\Tron.Abp.BatchTradeServer.csproj", "{AC7BED4B-0E91-422A-8CC6-45F6899F4EA2}"
EndProject

Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Solana.DexType.Test", "Solana.DexType.Test\Solana.DexType.Test.csproj", "{23B6EA0E-1B2F-452B-A3C3-97641892F3DA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.Ws.BoopFun", "Tran.Abp.Ws.BoopFun\Tran.Abp.Ws.BoopFun.csproj", "{1D8CB5A1-41CA-4F1B-9AC9-EC026888024C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.GRpc.Solana", "Tran.Abp.GRpc.Solana\Tran.Abp.GRpc.Solana.csproj", "{35DB9B25-B934-48DD-B00A-833C4C6039E2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.Ws.MeteoraDBC", "Tran.Abp.Ws.MeteoraDBC\Tran.Abp.Ws.MeteoraDBC.csproj", "{732EC27A-BD7B-4984-88BF-3042823FC9C0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.Ws.MeteoraDYN", "Tran.Abp.Ws.MeteoraDYN\Tran.Abp.Ws.MeteoraDYN.csproj", "{D4C851BB-2905-4E19-9D4D-0E06555F4450}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.Ws.MeteoraDLMM", "Tran.Abp.Ws.MeteoraDLMM\Tran.Abp.Ws.MeteoraDLMM.csproj", "{89F9F210-F9F4-42BB-ABB2-CFF16068986A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NatsConsoleApp", "NatsConsoleApp\NatsConsoleApp.csproj", "{34BACDD8-6533-42EF-A911-A38171DF0560}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tran.Abp.MarketCap", "Tran.Abp.MarketCap\Tran.Abp.MarketCap.csproj", "{6EB965FD-**************-D7BC6E7BE380}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "NatsModule", "NatsModule", "{1C16B1BC-975D-494B-B4B2-32E21E7576B9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tron.Abp.Nats", "Tron.Abp.Nats\Tron.Abp.Nats.csproj", "{881F52AB-7C73-4536-B983-CBD3C92A7B92}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{39F221EB-E694-4589-8D80-E058813EA3ED}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{39F221EB-E694-4589-8D80-E058813EA3ED}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{39F221EB-E694-4589-8D80-E058813EA3ED}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{39F221EB-E694-4589-8D80-E058813EA3ED}.Release|Any CPU.Build.0 = Release|Any CPU
		{3CA76009-E57A-4C1F-855E-791A9236A928}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3CA76009-E57A-4C1F-855E-791A9236A928}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3CA76009-E57A-4C1F-855E-791A9236A928}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3CA76009-E57A-4C1F-855E-791A9236A928}.Release|Any CPU.Build.0 = Release|Any CPU
		{4F15DECE-054F-44FB-A8BB-923EBF553903}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4F15DECE-054F-44FB-A8BB-923EBF553903}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4F15DECE-054F-44FB-A8BB-923EBF553903}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4F15DECE-054F-44FB-A8BB-923EBF553903}.Release|Any CPU.Build.0 = Release|Any CPU
		{AA534C87-5B38-4469-ABC9-8DBBC0EE9A84}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA534C87-5B38-4469-ABC9-8DBBC0EE9A84}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA534C87-5B38-4469-ABC9-8DBBC0EE9A84}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA534C87-5B38-4469-ABC9-8DBBC0EE9A84}.Release|Any CPU.Build.0 = Release|Any CPU
		{42ADE700-745F-402D-A4AE-6AA89FD38C20}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{42ADE700-745F-402D-A4AE-6AA89FD38C20}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{42ADE700-745F-402D-A4AE-6AA89FD38C20}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{42ADE700-745F-402D-A4AE-6AA89FD38C20}.Release|Any CPU.Build.0 = Release|Any CPU
		{615FF5A5-170C-4757-97EE-1D8878DFF39B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{615FF5A5-170C-4757-97EE-1D8878DFF39B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{615FF5A5-170C-4757-97EE-1D8878DFF39B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{615FF5A5-170C-4757-97EE-1D8878DFF39B}.Release|Any CPU.Build.0 = Release|Any CPU
		{7623B118-0E76-4A35-A418-4A46B4E17823}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7623B118-0E76-4A35-A418-4A46B4E17823}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7623B118-0E76-4A35-A418-4A46B4E17823}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7623B118-0E76-4A35-A418-4A46B4E17823}.Release|Any CPU.Build.0 = Release|Any CPU
		{EEB9BD6E-25E6-43DF-947B-4F926640F38A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EEB9BD6E-25E6-43DF-947B-4F926640F38A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EEB9BD6E-25E6-43DF-947B-4F926640F38A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EEB9BD6E-25E6-43DF-947B-4F926640F38A}.Release|Any CPU.Build.0 = Release|Any CPU
		{3F4EC50B-F78E-4BFB-BE60-96DFE5A6B3CA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3F4EC50B-F78E-4BFB-BE60-96DFE5A6B3CA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3F4EC50B-F78E-4BFB-BE60-96DFE5A6B3CA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3F4EC50B-F78E-4BFB-BE60-96DFE5A6B3CA}.Release|Any CPU.Build.0 = Release|Any CPU
		{477E122C-A1DB-4C2F-9548-6077742CA267}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{477E122C-A1DB-4C2F-9548-6077742CA267}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{477E122C-A1DB-4C2F-9548-6077742CA267}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{477E122C-A1DB-4C2F-9548-6077742CA267}.Release|Any CPU.Build.0 = Release|Any CPU
		{720AA338-686B-4398-B8BE-790286CF295A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{720AA338-686B-4398-B8BE-790286CF295A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{720AA338-686B-4398-B8BE-790286CF295A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{720AA338-686B-4398-B8BE-790286CF295A}.Release|Any CPU.Build.0 = Release|Any CPU
		{C4B37EAE-C779-427F-BCC4-A09E56099AA8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4B37EAE-C779-427F-BCC4-A09E56099AA8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4B37EAE-C779-427F-BCC4-A09E56099AA8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4B37EAE-C779-427F-BCC4-A09E56099AA8}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF1F740A-AD9D-4ECD-AA81-8E46E1C613CA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF1F740A-AD9D-4ECD-AA81-8E46E1C613CA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF1F740A-AD9D-4ECD-AA81-8E46E1C613CA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF1F740A-AD9D-4ECD-AA81-8E46E1C613CA}.Release|Any CPU.Build.0 = Release|Any CPU
		{EC71FDB6-A543-47F3-A8BD-59534D21202C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EC71FDB6-A543-47F3-A8BD-59534D21202C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EC71FDB6-A543-47F3-A8BD-59534D21202C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EC71FDB6-A543-47F3-A8BD-59534D21202C}.Release|Any CPU.Build.0 = Release|Any CPU
		{50C67ED0-262C-475C-AC29-06ACA2880599}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50C67ED0-262C-475C-AC29-06ACA2880599}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{50C67ED0-262C-475C-AC29-06ACA2880599}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{50C67ED0-262C-475C-AC29-06ACA2880599}.Release|Any CPU.Build.0 = Release|Any CPU
		{B06740FB-729C-41BF-84A7-3AE0548B4324}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B06740FB-729C-41BF-84A7-3AE0548B4324}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B06740FB-729C-41BF-84A7-3AE0548B4324}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B06740FB-729C-41BF-84A7-3AE0548B4324}.Release|Any CPU.Build.0 = Release|Any CPU
		{714BC9F9-A3AD-4DAF-92B6-78DECE0D33BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{714BC9F9-A3AD-4DAF-92B6-78DECE0D33BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{714BC9F9-A3AD-4DAF-92B6-78DECE0D33BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{714BC9F9-A3AD-4DAF-92B6-78DECE0D33BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3278B4D-83B8-4AEA-B094-D9768605F7B6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3278B4D-83B8-4AEA-B094-D9768605F7B6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3278B4D-83B8-4AEA-B094-D9768605F7B6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3278B4D-83B8-4AEA-B094-D9768605F7B6}.Release|Any CPU.Build.0 = Release|Any CPU
		{558C022C-A722-4AFA-879F-BD740BC829B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{558C022C-A722-4AFA-879F-BD740BC829B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{558C022C-A722-4AFA-879F-BD740BC829B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{558C022C-A722-4AFA-879F-BD740BC829B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{2992D1AD-340C-450A-A813-8A10D0822FFE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2992D1AD-340C-450A-A813-8A10D0822FFE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2992D1AD-340C-450A-A813-8A10D0822FFE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2992D1AD-340C-450A-A813-8A10D0822FFE}.Release|Any CPU.Build.0 = Release|Any CPU
		{FD20A917-BFDB-48B5-BE33-49F883C61F86}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FD20A917-BFDB-48B5-BE33-49F883C61F86}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FD20A917-BFDB-48B5-BE33-49F883C61F86}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FD20A917-BFDB-48B5-BE33-49F883C61F86}.Release|Any CPU.Build.0 = Release|Any CPU
		{73A520EE-3355-4A91-9E2C-322DA368AAA4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{73A520EE-3355-4A91-9E2C-322DA368AAA4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{73A520EE-3355-4A91-9E2C-322DA368AAA4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{73A520EE-3355-4A91-9E2C-322DA368AAA4}.Release|Any CPU.Build.0 = Release|Any CPU
		{25FFA728-7BFD-421E-AD4A-6ECDFA2F8568}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{25FFA728-7BFD-421E-AD4A-6ECDFA2F8568}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{25FFA728-7BFD-421E-AD4A-6ECDFA2F8568}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{25FFA728-7BFD-421E-AD4A-6ECDFA2F8568}.Release|Any CPU.Build.0 = Release|Any CPU
		{C1DDA791-C884-4E78-B8AF-3B41A0556339}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C1DDA791-C884-4E78-B8AF-3B41A0556339}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C1DDA791-C884-4E78-B8AF-3B41A0556339}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C1DDA791-C884-4E78-B8AF-3B41A0556339}.Release|Any CPU.Build.0 = Release|Any CPU
		{27970626-D873-4B20-9A85-A8F5E5CCFC4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{27970626-D873-4B20-9A85-A8F5E5CCFC4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{27970626-D873-4B20-9A85-A8F5E5CCFC4B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{27970626-D873-4B20-9A85-A8F5E5CCFC4B}.Release|Any CPU.Build.0 = Release|Any CPU
		{D3390DDB-A3E0-48C2-9703-4C6ED2A8A68B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D3390DDB-A3E0-48C2-9703-4C6ED2A8A68B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D3390DDB-A3E0-48C2-9703-4C6ED2A8A68B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D3390DDB-A3E0-48C2-9703-4C6ED2A8A68B}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B34A05C-463C-4202-9C1A-EDED98234330}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B34A05C-463C-4202-9C1A-EDED98234330}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B34A05C-463C-4202-9C1A-EDED98234330}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B34A05C-463C-4202-9C1A-EDED98234330}.Release|Any CPU.Build.0 = Release|Any CPU
		{D3BCFDBD-050C-45CF-BCF0-7E896A887767}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D3BCFDBD-050C-45CF-BCF0-7E896A887767}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D3BCFDBD-050C-45CF-BCF0-7E896A887767}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D3BCFDBD-050C-45CF-BCF0-7E896A887767}.Release|Any CPU.Build.0 = Release|Any CPU
		{C455260F-B981-4C19-95D8-F046557AF2BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C455260F-B981-4C19-95D8-F046557AF2BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C455260F-B981-4C19-95D8-F046557AF2BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C455260F-B981-4C19-95D8-F046557AF2BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{444A267F-57D1-4644-B182-8FC8ADA58731}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{444A267F-57D1-4644-B182-8FC8ADA58731}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{444A267F-57D1-4644-B182-8FC8ADA58731}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{444A267F-57D1-4644-B182-8FC8ADA58731}.Release|Any CPU.Build.0 = Release|Any CPU
		{15B331B3-14B7-1540-9C31-E5A045CDF4C1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{15B331B3-14B7-1540-9C31-E5A045CDF4C1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{15B331B3-14B7-1540-9C31-E5A045CDF4C1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{15B331B3-14B7-1540-9C31-E5A045CDF4C1}.Release|Any CPU.Build.0 = Release|Any CPU
		{7D4A6633-2430-1BFF-AA83-79CC931FB304}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7D4A6633-2430-1BFF-AA83-79CC931FB304}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7D4A6633-2430-1BFF-AA83-79CC931FB304}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7D4A6633-2430-1BFF-AA83-79CC931FB304}.Release|Any CPU.Build.0 = Release|Any CPU
		{54D965D6-16C2-FC88-4025-6A194626F544}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{54D965D6-16C2-FC88-4025-6A194626F544}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{54D965D6-16C2-FC88-4025-6A194626F544}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{54D965D6-16C2-FC88-4025-6A194626F544}.Release|Any CPU.Build.0 = Release|Any CPU
		{BADC1AB0-8E6E-A216-AF68-FAAE9FB24B72}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BADC1AB0-8E6E-A216-AF68-FAAE9FB24B72}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BADC1AB0-8E6E-A216-AF68-FAAE9FB24B72}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BADC1AB0-8E6E-A216-AF68-FAAE9FB24B72}.Release|Any CPU.Build.0 = Release|Any CPU
		{C0545405-6957-1FB4-9F95-697D6F9BDCA1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0545405-6957-1FB4-9F95-697D6F9BDCA1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0545405-6957-1FB4-9F95-697D6F9BDCA1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0545405-6957-1FB4-9F95-697D6F9BDCA1}.Release|Any CPU.Build.0 = Release|Any CPU
		{543F8749-A64E-4802-B292-DF364A12391C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{543F8749-A64E-4802-B292-DF364A12391C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{543F8749-A64E-4802-B292-DF364A12391C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{543F8749-A64E-4802-B292-DF364A12391C}.Release|Any CPU.Build.0 = Release|Any CPU
		{AB7EB35B-B522-4F01-8F11-5591C29654AD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB7EB35B-B522-4F01-8F11-5591C29654AD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB7EB35B-B522-4F01-8F11-5591C29654AD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB7EB35B-B522-4F01-8F11-5591C29654AD}.Release|Any CPU.Build.0 = Release|Any CPU
		{3A25EF63-B3D6-4305-9C52-6A51695726A7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3A25EF63-B3D6-4305-9C52-6A51695726A7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3A25EF63-B3D6-4305-9C52-6A51695726A7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3A25EF63-B3D6-4305-9C52-6A51695726A7}.Release|Any CPU.Build.0 = Release|Any CPU
		{DE0DA5A5-AADF-4F58-923F-A06F056E339C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DE0DA5A5-AADF-4F58-923F-A06F056E339C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DE0DA5A5-AADF-4F58-923F-A06F056E339C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DE0DA5A5-AADF-4F58-923F-A06F056E339C}.Release|Any CPU.Build.0 = Release|Any CPU
		{EC82C69B-3573-4789-8C3F-FFBB1F8101B6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EC82C69B-3573-4789-8C3F-FFBB1F8101B6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EC82C69B-3573-4789-8C3F-FFBB1F8101B6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EC82C69B-3573-4789-8C3F-FFBB1F8101B6}.Release|Any CPU.Build.0 = Release|Any CPU
		{3D2DDB0B-85C9-406E-BE32-217A7382A59C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3D2DDB0B-85C9-406E-BE32-217A7382A59C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3D2DDB0B-85C9-406E-BE32-217A7382A59C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3D2DDB0B-85C9-406E-BE32-217A7382A59C}.Release|Any CPU.Build.0 = Release|Any CPU
		{D5D0D5AF-E330-4ED4-97F5-646A4522FF05}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D5D0D5AF-E330-4ED4-97F5-646A4522FF05}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D5D0D5AF-E330-4ED4-97F5-646A4522FF05}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D5D0D5AF-E330-4ED4-97F5-646A4522FF05}.Release|Any CPU.Build.0 = Release|Any CPU
		{D6D25A97-6C31-4B58-A94A-37F0ED88FF37}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D6D25A97-6C31-4B58-A94A-37F0ED88FF37}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D6D25A97-6C31-4B58-A94A-37F0ED88FF37}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D6D25A97-6C31-4B58-A94A-37F0ED88FF37}.Release|Any CPU.Build.0 = Release|Any CPU
		{40584FE8-9ADB-4D07-A465-1A3F01BD7377}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{40584FE8-9ADB-4D07-A465-1A3F01BD7377}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{40584FE8-9ADB-4D07-A465-1A3F01BD7377}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{40584FE8-9ADB-4D07-A465-1A3F01BD7377}.Release|Any CPU.Build.0 = Release|Any CPU
		{62C72E76-9D36-44EE-851C-E619F700B2FA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{62C72E76-9D36-44EE-851C-E619F700B2FA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{62C72E76-9D36-44EE-851C-E619F700B2FA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{62C72E76-9D36-44EE-851C-E619F700B2FA}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3FDD9F7-A5C7-43EE-9C15-E21559416BBE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3FDD9F7-A5C7-43EE-9C15-E21559416BBE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3FDD9F7-A5C7-43EE-9C15-E21559416BBE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3FDD9F7-A5C7-43EE-9C15-E21559416BBE}.Release|Any CPU.Build.0 = Release|Any CPU
		{181F72B9-7AC0-4AC9-9659-74C341BF9419}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{181F72B9-7AC0-4AC9-9659-74C341BF9419}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{181F72B9-7AC0-4AC9-9659-74C341BF9419}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{181F72B9-7AC0-4AC9-9659-74C341BF9419}.Release|Any CPU.Build.0 = Release|Any CPU
		{AC7BED4B-0E91-422A-8CC6-45F6899F4EA2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AC7BED4B-0E91-422A-8CC6-45F6899F4EA2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AC7BED4B-0E91-422A-8CC6-45F6899F4EA2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AC7BED4B-0E91-422A-8CC6-45F6899F4EA2}.Release|Any CPU.Build.0 = Release|Any CPU
		{23B6EA0E-1B2F-452B-A3C3-97641892F3DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{23B6EA0E-1B2F-452B-A3C3-97641892F3DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{23B6EA0E-1B2F-452B-A3C3-97641892F3DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{23B6EA0E-1B2F-452B-A3C3-97641892F3DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{1D8CB5A1-41CA-4F1B-9AC9-EC026888024C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1D8CB5A1-41CA-4F1B-9AC9-EC026888024C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1D8CB5A1-41CA-4F1B-9AC9-EC026888024C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1D8CB5A1-41CA-4F1B-9AC9-EC026888024C}.Release|Any CPU.Build.0 = Release|Any CPU
		{35DB9B25-B934-48DD-B00A-833C4C6039E2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{35DB9B25-B934-48DD-B00A-833C4C6039E2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{35DB9B25-B934-48DD-B00A-833C4C6039E2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{35DB9B25-B934-48DD-B00A-833C4C6039E2}.Release|Any CPU.Build.0 = Release|Any CPU
		{732EC27A-BD7B-4984-88BF-3042823FC9C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{732EC27A-BD7B-4984-88BF-3042823FC9C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{732EC27A-BD7B-4984-88BF-3042823FC9C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{732EC27A-BD7B-4984-88BF-3042823FC9C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4C851BB-2905-4E19-9D4D-0E06555F4450}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4C851BB-2905-4E19-9D4D-0E06555F4450}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4C851BB-2905-4E19-9D4D-0E06555F4450}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4C851BB-2905-4E19-9D4D-0E06555F4450}.Release|Any CPU.Build.0 = Release|Any CPU
		{89F9F210-F9F4-42BB-ABB2-CFF16068986A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{89F9F210-F9F4-42BB-ABB2-CFF16068986A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{89F9F210-F9F4-42BB-ABB2-CFF16068986A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{89F9F210-F9F4-42BB-ABB2-CFF16068986A}.Release|Any CPU.Build.0 = Release|Any CPU
		{34BACDD8-6533-42EF-A911-A38171DF0560}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34BACDD8-6533-42EF-A911-A38171DF0560}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{34BACDD8-6533-42EF-A911-A38171DF0560}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{34BACDD8-6533-42EF-A911-A38171DF0560}.Release|Any CPU.Build.0 = Release|Any CPU
		{6EB965FD-**************-D7BC6E7BE380}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6EB965FD-**************-D7BC6E7BE380}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6EB965FD-**************-D7BC6E7BE380}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6EB965FD-**************-D7BC6E7BE380}.Release|Any CPU.Build.0 = Release|Any CPU
		{881F52AB-7C73-4536-B983-CBD3C92A7B92}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{881F52AB-7C73-4536-B983-CBD3C92A7B92}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{881F52AB-7C73-4536-B983-CBD3C92A7B92}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{881F52AB-7C73-4536-B983-CBD3C92A7B92}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{39F221EB-E694-4589-8D80-E058813EA3ED} = {32A89FFA-A1E0-45A0-8D6D-AF0A7B5FB050}
		{3CA76009-E57A-4C1F-855E-791A9236A928} = {32A89FFA-A1E0-45A0-8D6D-AF0A7B5FB050}
		{4F15DECE-054F-44FB-A8BB-923EBF553903} = {C5459FFB-B2C1-4FA8-9109-D32AD14AF66C}
		{B978361D-5F3C-4262-A358-2204984D0B9D} = {BFC7F7B0-5AA7-46C7-84B5-1A3B0D321211}
		{AA534C87-5B38-4469-ABC9-8DBBC0EE9A84} = {B978361D-5F3C-4262-A358-2204984D0B9D}
		{42ADE700-745F-402D-A4AE-6AA89FD38C20} = {B978361D-5F3C-4262-A358-2204984D0B9D}
		{615FF5A5-170C-4757-97EE-1D8878DFF39B} = {65A23DA3-80CB-462F-939F-EDECC1F3591D}
		{7623B118-0E76-4A35-A418-4A46B4E17823} = {BFC7F7B0-5AA7-46C7-84B5-1A3B0D321211}
		{EEB9BD6E-25E6-43DF-947B-4F926640F38A} = {32A89FFA-A1E0-45A0-8D6D-AF0A7B5FB050}
		{3F4EC50B-F78E-4BFB-BE60-96DFE5A6B3CA} = {5A9A3A55-1286-49CF-8F14-C90B9DA23573}
		{477E122C-A1DB-4C2F-9548-6077742CA267} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{720AA338-686B-4398-B8BE-790286CF295A} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{C4B37EAE-C779-427F-BCC4-A09E56099AA8} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{DF1F740A-AD9D-4ECD-AA81-8E46E1C613CA} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{EC71FDB6-A543-47F3-A8BD-59534D21202C} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{50C67ED0-262C-475C-AC29-06ACA2880599} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{B06740FB-729C-41BF-84A7-3AE0548B4324} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{714BC9F9-A3AD-4DAF-92B6-78DECE0D33BB} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{A3278B4D-83B8-4AEA-B094-D9768605F7B6} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{558C022C-A722-4AFA-879F-BD740BC829B7} = {65A23DA3-80CB-462F-939F-EDECC1F3591D}
		{2992D1AD-340C-450A-A813-8A10D0822FFE} = {65A23DA3-80CB-462F-939F-EDECC1F3591D}
		{FD20A917-BFDB-48B5-BE33-49F883C61F86} = {5A9A3A55-1286-49CF-8F14-C90B9DA23573}
		{73A520EE-3355-4A91-9E2C-322DA368AAA4} = {65A23DA3-80CB-462F-939F-EDECC1F3591D}
		{25FFA728-7BFD-421E-AD4A-6ECDFA2F8568} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{C1DDA791-C884-4E78-B8AF-3B41A0556339} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{27970626-D873-4B20-9A85-A8F5E5CCFC4B} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{D3390DDB-A3E0-48C2-9703-4C6ED2A8A68B} = {C5459FFB-B2C1-4FA8-9109-D32AD14AF66C}
		{8B34A05C-463C-4202-9C1A-EDED98234330} = {C5459FFB-B2C1-4FA8-9109-D32AD14AF66C}
		{D3BCFDBD-050C-45CF-BCF0-7E896A887767} = {C5459FFB-B2C1-4FA8-9109-D32AD14AF66C}
		{C455260F-B981-4C19-95D8-F046557AF2BB} = {65A23DA3-80CB-462F-939F-EDECC1F3591D}
		{444A267F-57D1-4644-B182-8FC8ADA58731} = {B978361D-5F3C-4262-A358-2204984D0B9D}
		{15B331B3-14B7-1540-9C31-E5A045CDF4C1} = {BFC7F7B0-5AA7-46C7-84B5-1A3B0D321211}
		{7D4A6633-2430-1BFF-AA83-79CC931FB304} = {BFC7F7B0-5AA7-46C7-84B5-1A3B0D321211}
		{54D965D6-16C2-FC88-4025-6A194626F544} = {BFC7F7B0-5AA7-46C7-84B5-1A3B0D321211}
		{BADC1AB0-8E6E-A216-AF68-FAAE9FB24B72} = {BFC7F7B0-5AA7-46C7-84B5-1A3B0D321211}
		{C0545405-6957-1FB4-9F95-697D6F9BDCA1} = {BFC7F7B0-5AA7-46C7-84B5-1A3B0D321211}
		{4ECAB070-03B0-4D25-92E2-62869313DD55} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{543F8749-A64E-4802-B292-DF364A12391C} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{AB7EB35B-B522-4F01-8F11-5591C29654AD} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{3A25EF63-B3D6-4305-9C52-6A51695726A7} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{DE0DA5A5-AADF-4F58-923F-A06F056E339C} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{EC82C69B-3573-4789-8C3F-FFBB1F8101B6} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{3D2DDB0B-85C9-406E-BE32-217A7382A59C} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{D5D0D5AF-E330-4ED4-97F5-646A4522FF05} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{D6D25A97-6C31-4B58-A94A-37F0ED88FF37} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{40584FE8-9ADB-4D07-A465-1A3F01BD7377} = {B978361D-5F3C-4262-A358-2204984D0B9D}
		{62C72E76-9D36-44EE-851C-E619F700B2FA} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{A3FDD9F7-A5C7-43EE-9C15-E21559416BBE} = {65A23DA3-80CB-462F-939F-EDECC1F3591D}
		{181F72B9-7AC0-4AC9-9659-74C341BF9419} = {65A23DA3-80CB-462F-939F-EDECC1F3591D}
		{AC7BED4B-0E91-422A-8CC6-45F6899F4EA2} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{23B6EA0E-1B2F-452B-A3C3-97641892F3DA} = {65A23DA3-80CB-462F-939F-EDECC1F3591D}
		{1D8CB5A1-41CA-4F1B-9AC9-EC026888024C} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{35DB9B25-B934-48DD-B00A-833C4C6039E2} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{732EC27A-BD7B-4984-88BF-3042823FC9C0} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{D4C851BB-2905-4E19-9D4D-0E06555F4450} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{89F9F210-F9F4-42BB-ABB2-CFF16068986A} = {4ECAB070-03B0-4D25-92E2-62869313DD55}
		{34BACDD8-6533-42EF-A911-A38171DF0560} = {65A23DA3-80CB-462F-939F-EDECC1F3591D}
		{6EB965FD-**************-D7BC6E7BE380} = {B70703A4-2B97-431A-833E-75627ADAE329}
		{881F52AB-7C73-4536-B983-CBD3C92A7B92} = {1C16B1BC-975D-494B-B4B2-32E21E7576B9}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C9D617B4-7C54-46A2-9520-BA098CCB4BFF}
	EndGlobalSection
EndGlobal
