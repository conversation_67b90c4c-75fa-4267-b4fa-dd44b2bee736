using System.Text.Json;
using Microsoft.Extensions.Logging;
using Solnet.Programs;
using Solnet.Programs.Utilities;
using Solnet.Rpc;
using Solnet.Rpc.Models;
using Solnet.Rpc.Types;
using Solnet.Wallet;

namespace Solnet.AABot.Launchpad;

public class RayLaunchpadImpl : AABotProgram, IRayLaunchpad, IAABot
{
    /*public IRpcClient RpcClient { get; set; }
    private Account Trader { get; set; }
    private bool IsDev = true;
    protected readonly ILogger _logger;*/

    public RayLaunchpadImpl(IRpcClient rpc, Account _trader, ILogger logger) : base(rpc, null, _trader, logger)
    {
        RpcClient = rpc;
        Trader = _trader;
        IsDev = rpc.NodeAddress.Host.Contains("devnet");
        _logger = logger ?? new LoggerFactory().CreateLogger("PumpFunImpl");
    }

    public RayLaunchpadImpl(IRpcClient rpc, IRpcClient zeroSlotRpcClient, Account _trader, ILogger logger) : base(rpc,
        zeroSlotRpcClient, _trader, logger)
    {
        RpcClient = rpc;
        Trader = _trader;
        IsDev = rpc.NodeAddress.Host.Contains("devnet");
        _logger = logger ?? new LoggerFactory().CreateLogger("PumpFunImpl");
    }

    /// <summary>
    ///  添加计算预算指令的公共方法
    /// </summary>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="cu"></param>
    /// <returns></returns>
    private (TransactionInstruction computeBudget, TransactionInstruction computePrice)
        GetComputeInstructions(decimal fee = 0, decimal mev = 0, decimal cu = 1_000_000m)
    {
        //var cu = 1_000_000m;
        fee = fee == 0 ? 0.000005m : fee;
        var microLamports = (ulong)((fee - 0.000005M) * cu);
        var jito = (ulong)(mev * 1e9m); // 未使用，可以保留以备将来扩展

        var computebudget = (ulong)cu;
        var computeprice = (ulong)microLamports * 1000;
        TransactionInstruction computeBudget = RayLaunchpadProgram.SetCUlimit(computebudget);
        TransactionInstruction computePrice = ComputeBudgetProgram.SetComputeUnitPrice(computeprice);

        return (computeBudget, computePrice);
    }

    private async Task<string> GetPoolInfo(string address)
    {
        var result = await RpcClient.GetAccountInfoAsync(address, Commitment.Confirmed);
        do
        {
            await Task.Delay(200);
            result = await RpcClient.GetAccountInfoAsync(address, Commitment.Confirmed);
        } while (!result.WasHttpRequestSuccessful);

        //var data = result.Result.Value.Data[0];
        var data = new ReadOnlySpan<byte>(Convert.FromBase64String(result.Result.Value.Data[0]));
        var offset = 8 + 8 + 5 * 1 + 15 * 8 + 32;
        string platform_config = data.GetPubKey(offset);
        return platform_config;
    }

    /// <summary>
    /// 模拟 提交
    /// </summary>
    /// <param name="transactions"></param>
    /// <returns></returns>
    private async Task<(decimal, string?)> GetSimulationUnits(List<TransactionInstruction> transactions)
    {
        var blockhash = await RpcClient.GetLatestBlockHashAsync();
        VersionedTransaction tx = new VersionedTransaction
        {
            FeePayer = Trader,
            RecentBlockHash = blockhash?.Result?.Value?.Blockhash,
            Instructions = new(),
            AddressTableLookups = new()
        };

        tx.Instructions.AddRange(transactions);
        tx.Sign(new List<Solnet.Wallet.Account> { Trader });

        var result = await RpcClient.SimulateTransactionAsync(tx.Serialize(), commitment: Commitment.Confirmed);
        Console.WriteLine("GetSimulationUnits=>" + result.RawRpcResponse);
        if (result.WasSuccessful)
        {
            JsonDocument doc = JsonDocument.Parse(result.RawRpcResponse);
            JsonElement root = doc.RootElement;

            JsonElement resultElement = default;
            JsonElement valueElement = default;
            JsonElement unitsConsumedElement = default;
            if (result.Result.Value.Error != null)
            {
                _logger?.LogDebug("GetSimulationUnits=>" + result.RawRpcResponse);
                return (-1, JsonSerializer.Serialize(result.Result.Value.Error));
            }

            // 使用 TryGetProperty 减少嵌套
            bool success = root.TryGetProperty("result", out resultElement) &&
                           resultElement.TryGetProperty("value", out valueElement) &&
                           valueElement.TryGetProperty("unitsConsumed", out unitsConsumedElement) &&
                           unitsConsumedElement.ValueKind == JsonValueKind.Number;
            if (success) return (unitsConsumedElement.GetDecimal(), null);
            else return (0m, null);

            ulong unitsConsumed = doc.RootElement
                .GetProperty("result")
                .GetProperty("value")
                .GetProperty("unitsConsumed").GetUInt64();
        }

        return (0m, null);
    }

    /// <summary>
    /// 解析 地址表 数据
    /// </summary>
    /// <param name="jsonData"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private List<PublicKey> ExtractAddressesFromJson(string jsonData)
    {
        try
        {
            using var document = JsonDocument.Parse(jsonData);
            var addressesElement = document.RootElement
                .GetProperty("parsed")
                .GetProperty("info")
                .GetProperty("addresses");

            var addresses = new List<PublicKey>();
            foreach (var address in addressesElement.EnumerateArray())
            {
                addresses.Add(new PublicKey(address.GetString()));
            }

            return addresses;
        }
        catch (Exception ex)
        {
            throw new Exception($"无法从 JSON 提取 addresses 数组：{ex.Message}");
        }
    }

    /// <summary>
    /// 封装交易构建和发送逻辑
    /// </summary>
    /// <param name="instructions"></param>
    /// <returns></returns>
    private async Task<(bool Successful, string Signature, string ErrorMsg)> BuildAndSendTableAddressTransaction(
        List<TransactionInstruction> instructions)
    {
        var lookupTableAddress =
            IsDev ? RayLaunchpadProgram.DevLookupTableAddress : RayLaunchpadProgram.LookupTableAddress;

        var programId = instructions.Select(s => new PublicKey(s.ProgramId)).ToList().Distinct().ToList();
        var writableSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && s.IsSigner && s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var readonlySigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !writableSigners.Contains(s.PublicKey) &&
                        s.IsSigner && !s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var writableNonSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !s.IsSigner && s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var readonlyNonSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !s.IsSigner && !s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();

        //获取地址表信息
        //var accountInfo =
        //    await RpcClient.GetAccountInfoAsync(lookupTableAddress, Commitment.Finalized, BinaryEncoding.JsonParsed);
        //if (accountInfo.Result.Value == null)
        //{
        //    await Task.Delay(100);
        //    accountInfo =
        //        await RpcClient.GetAccountInfoAsync(lookupTableAddress, Commitment.Finalized,
        //            BinaryEncoding.JsonParsed);
        //    Console.WriteLine("获取地址表=>" + accountInfo.RawRpcResponse);
        //    if (accountInfo.Result.Value == null)
        //    {
        //        Console.WriteLine($"获取地址表失败  退出交易");
        //        return (false, "", "获取地址表失败");
        //    }
        //}
        //var tableAddresses = ExtractAddressesFromJson(accountInfo.Result.Value.Data[0]);

        var tableAddresses = AABotProgram.TableAddressRaydium(IsDev);
        var write_data = new List<byte>();
        var read_data = new List<byte>();
        for (int i = 0; i < instructions.Count; i++)
        {
            for (int j = 0; j < instructions[i].Keys.Count; j++)
            {
                var key = instructions[i].Keys[j];
                if (programId.Contains(new PublicKey(key.PublicKey))) continue;
                var index = tableAddresses.IndexOf(new PublicKey(key.PublicKey));
                if (index > -1)
                {
                    if (key.IsWritable)
                    {
                        if (!write_data.Contains(Convert.ToByte(index)))
                            write_data.Add(Convert.ToByte(index));
                    }
                    else
                    {
                        if (!read_data.Contains(Convert.ToByte(index)))
                            read_data.Add(Convert.ToByte(index));
                    }
                }
            }
        }

        var lookupTableAccount = new List<Message.MessageAddressTableLookup>
        {
            new Message.MessageAddressTableLookup()
            {
                AccountKey = lookupTableAddress,
                WritableIndexes = write_data.ToArray(),
                ReadonlyIndexes = read_data.ToArray()
            }
        };


        var AccountKeys = new List<PublicKey>();
        AccountKeys.AddRange(writableSigners.Select(s => new PublicKey(s)).ToList());
        AccountKeys.AddRange(readonlySigners.Select(s => new PublicKey(s)).ToList());
        var AccountKeysWritableNonSigners = writableNonSigners.Where(s => !tableAddresses.Contains(new PublicKey(s)))
            .Select(s => new PublicKey(s)).ToList();
        AccountKeys.AddRange(AccountKeysWritableNonSigners);
        AccountKeys.AddRange(programId);
        var AccountKeysReadonlyNonSigners = readonlyNonSigners.Where(s => !tableAddresses.Contains(new PublicKey(s)))
            .Select(s => new PublicKey(s)).ToList();
        AccountKeys.AddRange(AccountKeysReadonlyNonSigners);
        AccountKeys = AccountKeys.Distinct().ToList();
        var StaticAccountKeys = new List<PublicKey>();
        StaticAccountKeys.AddRange(AccountKeys);
        StaticAccountKeys.AddRange(writableNonSigners.Select(s => new PublicKey(s)).ToList());
        StaticAccountKeys.AddRange(readonlyNonSigners.Select(s => new PublicKey(s)).ToList());
        StaticAccountKeys = StaticAccountKeys.Distinct().ToList();

        var compileInstructions = new List<CompiledInstruction>();

        for (int i = 0; i < instructions.Count; i++)
        {
            var item = instructions[i];
            var KeyIndices = item.Keys.Select(s =>
                {
                    return Convert.ToByte(StaticAccountKeys.IndexOf(new PublicKey(s.PublicKey)));
                }
            ).ToArray();
            var compileIns = new CompiledInstruction()
            {
                ProgramIdIndex = Convert.ToByte(AccountKeys.IndexOf(new PublicKey(item.ProgramId))),
                Data = item.Data,
                DataLength = new byte[] { Convert.ToByte(item.Data.Length) },
                KeyIndices = KeyIndices,
                KeyIndicesCount = new byte[] { Convert.ToByte(KeyIndices.Length) },
            };
            compileInstructions.Add(compileIns);
        }

        var res = await RpcClient.GetLatestBlockHashAsync();
        LatestBlockHash latestBlockHash = res?.Result?.Value;
        if (latestBlockHash == null)
        {
            await Task.Delay(100);
            res = await RpcClient.GetLatestBlockHashAsync();
            latestBlockHash = res?.Result?.Value;
        }

        var message = new Message.VersionedMessage()
        {
            Header = new MessageHeader()
            {
                RequiredSignatures = Convert.ToByte(writableSigners.Count + readonlySigners.Count),
                ReadOnlySignedAccounts = Convert.ToByte(readonlySigners.Count),
                ReadOnlyUnsignedAccounts = Convert.ToByte(AccountKeys.Count - AccountKeysWritableNonSigners.Count -
                                                          readonlySigners.Count - writableSigners.Count),
            },
            AccountKeys = AccountKeys,
            AddressTableLookups = lookupTableAccount,
            Instructions = compileInstructions,
            RecentBlockhash = latestBlockHash?.Blockhash,
        };

        var sign = new List<Solnet.Wallet.Account> { Trader };
        //sign.AddRange(player);

        var CompileTx = VersionedTransaction.Populate(message);
        CompileTx.Sign(sign);

        var response =
            await RpcClient.SendTransactionAsync(CompileTx.VersionedSerialize(), commitment: Commitment.Confirmed);
        _logger?.LogDebug(response.RawRpcResponse);
        Console.WriteLine(response.RawRpcResponse);
        return (response.WasSuccessful, response.Result, response.Reason);
    }

    /// <summary>
    /// 发送交易的公共方法 
    /// </summary>
    /// <param name="transactions"></param>
    /// <returns></returns>
    private async Task<(bool Successful, string Signature, string ErrorMsg)> SendTransactionAsync(
        List<TransactionInstruction> transactions)
    {
        var blockhash = await RpcClient.GetLatestBlockHashAsync();
        var builder = new VersionedTransaction
        {
            FeePayer = Trader.PublicKey,
            RecentBlockHash = blockhash.Result.Value.Blockhash,
            Instructions = transactions,
            AddressTableLookups = new()
        };

        builder.Sign(new List<Account> { Trader });
        var transaction = await RpcClient.SendTransactionAsync(builder.Serialize(), commitment: Commitment.Confirmed);
        _logger?.LogDebug(transaction.RawRpcResponse);
        Console.WriteLine($"SendTransaction=>{transaction.RawRpcResponse}");
        return (transaction.WasSuccessful, transaction.Result, transaction.Reason);
    }

    private async Task<SwapAccounts> BuildSwapAccountsAsync(string mintAddress, string[] accountAddresses = null)
    {
        var mint = new PublicKey(mintAddress);
        SwapAccounts accounts = new SwapAccounts { Payer = Trader.PublicKey };
        if (accountAddresses != null)
        {
            accounts.PoolState = new PublicKey(accountAddresses[0]);
            accounts.BaseVault = new PublicKey(accountAddresses[1]);
            accounts.QuoteVault = new PublicKey(accountAddresses[2]);
            accounts.BaseMint = new PublicKey(accountAddresses[3]);
            accounts.QuoteMint = new PublicKey(accountAddresses[4]);
            accounts.PlatformConfig = new PublicKey(accountAddresses[5]);
        }
        else
        {
            var programId = IsDev ? RayLaunchpadProgram.LAUNCHPAD_PROGRAM_Dev : RayLaunchpadProgram.LAUNCHPAD_PROGRAM;
            accounts.PoolState = RayLaunchpadPda.GetPdaLaunchpadPoolId(programId, mint, RayLaunchpadProgram.WSOL)
                .Address;


            accounts.BaseVault = RayLaunchpadPda.GetPdaLaunchpadVaultId(programId, accounts.PoolState, mint).Address;
            accounts.QuoteVault = RayLaunchpadPda
                .GetPdaLaunchpadVaultId(programId, accounts.PoolState, RayLaunchpadProgram.WSOL).Address;
            accounts.BaseMint = mint;
            accounts.QuoteMint = RayLaunchpadProgram.WSOL;
            var platformConfig = await GetPoolInfo(accounts.PoolState);
            accounts.PlatformConfig = new PublicKey(platformConfig);
        }

        return accounts;
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> SwapInAsync(string mintAddress, decimal sol,
        decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0, bool isRise = false, string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();
        transactions = await GetBuy(mintAddress, sol, slippage, fee, mev, accountAddresses, tokenPrice, isRise,
            blockHash);
        var lookupTableAddress =
            IsDev ? RayLaunchpadProgram.DevLookupTableAddress : RayLaunchpadProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev, blockHash);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> SwapOutAsync(string mintAddress,
        decimal token_Amount, decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0, string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);

        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);
        ulong tokenAmountLamports = (ulong)(token_Amount * 1e6m);
        var swapAccounts = await BuildSwapAccountsAsync(mintAddress, accountAddresses);
        if (swapAccounts == null) return (false, string.Empty, "Accounts is null");
        var quoteTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, RayLaunchpadProgram.WSOL);
        swapAccounts.UserBaseToken =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));
        swapAccounts.UserQuoteToken = quoteTokenAccount;
        transactions.Add(RayLaunchpadProgram.Sell(swapAccounts, tokenAmountLamports, 0ul, slippagePercentage,
            IsDev, tokenPriceUl));
        /*var (cu, err) = await GetSimulationUnits(transactions);
        if (err != null)
        {
            return (false, string.Empty, err);
        }

        if (cu > 0)
        {
            cu = Math.Ceiling(cu * 1.1m); // 增加 10% 缓冲
            (computeBudget, computePrice) = GetComputeInstructions(fee, mev, cu);
        }*/

        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);
        var lookupTableAddress =
            IsDev ? RayLaunchpadProgram.DevLookupTableAddress : RayLaunchpadProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev, blockHash);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> SwapOutByPercentAsync(string mintAddress,
        decimal token_Percent, decimal slippage, decimal fee = 0,
        decimal mev = 0, string[] accountAddresses = null, decimal tokenPrice = 0, string blockHash = "")
    {
        var transactions = await GetSellPercentage(mintAddress, token_Percent, slippage, fee, mev, accountAddresses,
            tokenPrice, blockHash);
        var lookupTableAddress =
            IsDev ? RayLaunchpadProgram.DevLookupTableAddress : RayLaunchpadProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev, blockHash);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> BuySell(string mintAddress, decimal sol,
        decimal slippage, decimal fee = 0, decimal mev = 0, string blockHash = "")
    {
        var transactions =
            await GetBuySell(mintAddress, sol, slippage, fee, mev, null,
                blockHash); //new List<TransactionInstruction>() { computeBudget, computePrice, createSeed, initSeed, buy, sell };
        var lookupTableAddress =
            IsDev ? RayLaunchpadProgram.DevLookupTableAddress : RayLaunchpadProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev, blockHash);
    }


    public async Task<List<TransactionInstruction>> GetBuy(string mintAddress, decimal sol, decimal slippage,
        decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0, bool isRise = false, string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);
        var swapAccounts = await BuildSwapAccountsAsync(mintAddress, accountAddresses: accountAddresses);
        if (swapAccounts == null) return null;
        // 创建并初始化源账户
        var seed = new Account().PublicKey.Key.Substring(0, 32);
        PublicKey.TryCreateWithSeed(Trader.PublicKey, seed, TokenProgram.ProgramIdKey, out var source);
        transactions.Add(SystemProgram.CreateAccountWithSeed(
            Trader.PublicKey, source, Trader.PublicKey, seed, (ulong)(2039280 + solAmount), 165,
            TokenProgram.ProgramIdKey));
        transactions.Add(TokenProgram.InitializeAccount(source, RayLaunchpadProgram.WSOL, Trader.PublicKey));
        swapAccounts.UserQuoteToken = source;

        swapAccounts.UserBaseToken =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));
        transactions.Add(RayLaunchpadProgram.Buy(swapAccounts, solAmount, slippagePercentage, IsDev, tokenPriceUl,
            isRise));


        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);
        /*//Console.WriteLine(JsonSerializer.Serialize(transactions));
        var lookupTableAddress =
            IsDev ? RayLaunchpadProgram.DevLookupTableAddress : RayLaunchpadProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev, blockHash);*/

        return transactions;
    }

    public async Task<List<TransactionInstruction>> GetSellPercentage(string mintAddress, decimal token_Percent,
        decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0, string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);

        var tokenPercentLamports = (ulong)token_Percent;
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);
        var swapAccounts = await BuildSwapAccountsAsync(mintAddress, accountAddresses);
        if (swapAccounts == null) return null;
        var quoteTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, RayLaunchpadProgram.WSOL);
        swapAccounts.UserBaseToken =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));
        swapAccounts.UserQuoteToken = quoteTokenAccount;
        transactions.Add(RayLaunchpadProgram.Sell(swapAccounts, 0ul, tokenPercentLamports, slippagePercentage,
            IsDev, tokenPriceUl));

        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);
        /*var lookupTableAddress =
            IsDev ? RayLaunchpadProgram.DevLookupTableAddress : RayLaunchpadProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev, blockHash);*/

        return transactions;
    }

    public async Task<List<TransactionInstruction>> GetBuySell(string mintAddress, decimal sol, decimal slippage,
        decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var swapAccounts = await BuildSwapAccountsAsync(mintAddress, accountAddresses: null);
        if (swapAccounts == null) return null;
        // 创建并初始化源账户
        var seed = new Account().PublicKey.Key.Substring(0, 32);
        PublicKey.TryCreateWithSeed(Trader.PublicKey, seed, TokenProgram.ProgramIdKey, out var source);
        var createSeed = (SystemProgram.CreateAccountWithSeed(
            Trader.PublicKey, source, Trader.PublicKey, seed, (ulong)(2039280 + solAmount), 165,
            TokenProgram.ProgramIdKey));
        var initSeed = (TokenProgram.InitializeAccount(source, RayLaunchpadProgram.WSOL, Trader.PublicKey));
        swapAccounts.UserQuoteToken = source;

        swapAccounts.UserBaseToken =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));
        var buy = (RayLaunchpadProgram.Buy(swapAccounts, solAmount, slippagePercentage, IsDev, 0, false));


        var quoteTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, RayLaunchpadProgram.WSOL);
        swapAccounts.UserBaseToken =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));
        swapAccounts.UserQuoteToken = quoteTokenAccount;
        var sell = (RayLaunchpadProgram.Sell(swapAccounts, 0ul, 100, slippagePercentage, IsDev, 0));

        transactions = new List<TransactionInstruction>()
            { computeBudget, computePrice, createSeed, initSeed, buy, sell };
        /*var lookupTableAddress =
            IsDev ? RayLaunchpadProgram.DevLookupTableAddress : RayLaunchpadProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev, blockHash);*/

        return transactions;
    }
}