using Solnet.Programs.Utilities;
using Solnet.Wallet;

namespace Solnet.AABot.RaydiumClmm.Model;

public class RaydiumClmmPoolAccounts
{
    public PublicKey AmmConfig { get; set; } // publicKey
    public PublicKey Owner { get; set; } // publicKey
    public PublicKey TokenMint0 { get; set; } // publicKey
    public PublicKey TokenMint1 { get; set; } // publicKey
    public PublicKey TokenVault0 { get; set; } // publicKey
    public PublicKey TokenVault1 { get; set; } // publicKey
    public PublicKey ObservationKey { get; set; } // publicKey
    public ulong MintDecimals0 { get; set; } // u8
    public ulong MintDecimals1 { get; set; } // u8
    public ulong TickSpacing { get; set; }//u16
    public int TickCurrent { get; set; }
    public ulong[] TickArrayBitmap { get; set; } // [u64; 16]

    public static RaydiumClmmPoolAccounts Deserialize(ReadOnlySpan<byte> _data, int offset = 0)
    {
        var clmmPool=new RaydiumClmmPoolAccounts();
        offset = 8 + 1;
        clmmPool.AmmConfig = _data.GetPubKey(offset);
        offset += 32;
        clmmPool.Owner = _data.GetPubKey(offset);
        offset += 32;
        clmmPool.TokenMint0 = _data.GetPubKey(offset);
        offset += 32;
        clmmPool.TokenMint1 = _data.GetPubKey(offset);
        offset += 32;
        clmmPool.TokenVault0 = _data.GetPubKey(offset);
        offset += 32;
        clmmPool.TokenVault1 = _data.GetPubKey(offset);
        offset += 32;
        clmmPool.ObservationKey = _data.GetPubKey(offset);
        offset += 32;
        clmmPool.MintDecimals0 = _data.GetU8(offset);
        offset += 1;
        clmmPool.MintDecimals1 = _data.GetU8(offset);
        offset += 1;
        clmmPool.TickSpacing = _data.GetU16(offset);
        offset += 2;
        offset += 16;
        offset += 16;
        clmmPool.TickCurrent = _data.GetS32(offset);
        offset += 4;
        //u16 u128 u128 i32 u16 u16 u128 u128 u64 u64 u128 u128 u128 u128  u8 u8*7 
        offset +=  2 + 2 + 16 + 16 + 8 + 8 + 16 + 16 + 16 + 16 + 1 + 7;
        //跳过 rewardInfo
        var rewardInfoLen = (1 + 8 + 8 + 8 + 16 + 8 + 8 + 32 + 32 + 32 + 16)*3;
        offset+=rewardInfoLen;
        clmmPool.TickArrayBitmap=new ulong[16];
        for (var i = 0; i < 16; i++)
        {
            clmmPool.TickArrayBitmap[i] = _data.GetU64(offset);
            offset += 8;
        }
        return clmmPool;
    }
}