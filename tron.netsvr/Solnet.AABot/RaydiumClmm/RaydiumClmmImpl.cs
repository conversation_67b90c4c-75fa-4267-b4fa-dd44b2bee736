using System.Text.Json;
using Microsoft.Extensions.Logging;
using Solnet.AABot.RaydiumClmm.Model;
using Solnet.Programs;
using Solnet.Programs.Utilities;
using Solnet.Rpc;
using Solnet.Rpc.Models;
using Solnet.Rpc.Types;
using Solnet.Wallet;

namespace Solnet.AABot.RaydiumClmm;

public class RaydiumClmmImpl :AABotProgram, IRaydiumClmm, IAABot
{
    /*public IRpcClient RpcClient { get; set; }
    private Account Trader { get; set; }
    private bool IsDev = false;
    protected readonly ILogger _logger;*/

    public RaydiumClmmImpl(IRpcClient rpc, Account _trader, ILogger logger = null):base(rpc, null,_trader, logger)
    {
        RpcClient = rpc;
        Trader = _trader;
        IsDev = rpc.NodeAddress.Host.Contains("devnet");
        _logger = logger ?? new LoggerFactory().CreateLogger("RaydiumClmmImpl");
    }
    public RaydiumClmmImpl(IRpcClient rpc,IRpcClient zeroSlotRpcClient, Account _trader, ILogger logger):base(rpc, zeroSlotRpcClient,_trader, logger)
    {
        RpcClient = rpc;
        Trader = _trader;
        IsDev = rpc.NodeAddress.Host.Contains("devnet");
        _logger = logger ?? new LoggerFactory().CreateLogger("RaydiumClmmImpl");
    }
    #region 私有方法

    public async Task GetAmmInfo(string mint)
    {
        var result = await RpcClient.GetProgramAccountsAsync("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK",
            dataSize: 1544, commitment: Commitment.Confirmed, memCmpList: new List<MemCmp>
            {
                new MemCmp()
                {
                    Bytes = mint, Offset = 8 + 1 + 32 + 32 + 32
                }
            });
        if (result.WasSuccessful && result.Result != null && result.Result.Count > 0)
        {
        }
    }

    public async Task<RaydiumClmmPoolAccounts> GetPoolState(string poolId)
    {
        var result = await RpcClient.GetAccountInfoAsync(poolId, Commitment.Confirmed);
        while (true)
        {
            if (result.WasSuccessful && result.Result != null)
            {
                var data = result.Result.Value.Data[0];
                var poolState = RaydiumClmmPoolAccounts.Deserialize(Convert.FromBase64String(data));
                return poolState;
            }

            await Task.Delay(1000);
            result = await RpcClient.GetAccountInfoAsync(poolId, Commitment.Confirmed);
        }

        return null;
    }

    public async Task<TickArrayBitmapExtension> GetTickArrayBitmap(string tickarray_bitmap_extension)
    {
        var result = await RpcClient.GetAccountInfoAsync(tickarray_bitmap_extension, Commitment.Confirmed);
        while (true)
        {
            if (result.WasSuccessful && result.Result != null)
            {
                var data = result.Result.Value.Data[0];
                var tickArrayBitmapExtension = TickArrayBitmapExtension.Deserialize(Convert.FromBase64String(data));
                return tickArrayBitmapExtension;
            }

            await Task.Delay(1000);
            result = await RpcClient.GetAccountInfoAsync(tickarray_bitmap_extension, Commitment.Confirmed);
        }

        return null;
    }

    /// <summary>
    ///  添加计算预算指令的公共方法
    /// </summary>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="cu"></param>
    /// <returns></returns>
    private (TransactionInstruction computeBudget, TransactionInstruction computePrice) GetComputeInstructions(
        decimal fee = 0, decimal mev = 0, decimal cu = 1_000_000m)
    {
        //var cu = 1_000_000m;
        fee = fee == 0 ? 0.000005m : fee;
        var microLamports = (ulong)((fee - 0.000005m) * ((decimal)Math.Pow(10, 15) / cu));
        var jito = (ulong)(mev * 1e9m); // 未使用，可以保留以备将来扩展

        var computebudget = (ulong)cu;
        var computeprice = (ulong)microLamports;
        TransactionInstruction computeBudget = RaydiumClmmProgram.SetCUlimit(computebudget);
        TransactionInstruction computePrice = ComputeBudgetProgram.SetComputeUnitPrice(computeprice);

        return (computeBudget, computePrice);
    }

    /// <summary>
    /// 模拟 提交
    /// </summary>
    /// <param name="transactions"></param>
    /// <returns></returns>
    private async Task<(decimal, string?)> GetSimulationUnits(List<TransactionInstruction> transactions)
    {
        var blockhash = await RpcClient.GetLatestBlockHashAsync();
        VersionedTransaction tx = new VersionedTransaction
        {
            FeePayer = Trader,
            RecentBlockHash = blockhash?.Result?.Value?.Blockhash,
            Instructions = new(),
            AddressTableLookups = new()
        };

        tx.Instructions.AddRange(transactions);
        tx.Sign(new List<Solnet.Wallet.Account> { Trader });

        var result = await RpcClient.SimulateTransactionAsync(tx.Serialize(), commitment: Commitment.Confirmed);
        Console.WriteLine("GetSimulationUnits=>" + result.RawRpcResponse);
        if (result.WasSuccessful)
        {
            JsonDocument doc = JsonDocument.Parse(result.RawRpcResponse);
            JsonElement root = doc.RootElement;

            JsonElement resultElement = default;
            JsonElement valueElement = default;
            JsonElement unitsConsumedElement = default;
            if (result.Result.Value.Error != null)
            {
                _logger?.LogDebug("GetSimulationUnits=>" + result.RawRpcResponse);
                return (-1, result.Result.Value.Error.Type.ToString());
            }

            // 使用 TryGetProperty 减少嵌套
            bool success = root.TryGetProperty("result", out resultElement) &&
                           resultElement.TryGetProperty("value", out valueElement) &&
                           valueElement.TryGetProperty("unitsConsumed", out unitsConsumedElement) &&
                           unitsConsumedElement.ValueKind == JsonValueKind.Number;
            if (success) return (unitsConsumedElement.GetDecimal(), null);
            else return (0m, null);

            ulong unitsConsumed = doc.RootElement
                .GetProperty("result")
                .GetProperty("value")
                .GetProperty("unitsConsumed").GetUInt64();
        }

        return (0m, null);
    }

    /// <summary>
    /// 解析 地址表 数据
    /// </summary>
    /// <param name="jsonData"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private List<PublicKey> ExtractAddressesFromJson(string jsonData)
    {
        try
        {
            using var document = JsonDocument.Parse(jsonData);
            var addressesElement = document.RootElement
                .GetProperty("parsed")
                .GetProperty("info")
                .GetProperty("addresses");

            var addresses = new List<PublicKey>();
            foreach (var address in addressesElement.EnumerateArray())
            {
                addresses.Add(new PublicKey(address.GetString()));
            }

            return addresses;
        }
        catch (Exception ex)
        {
            throw new Exception($"无法从 JSON 提取 addresses 数组：{ex.Message}");
        }
    }

    /// <summary>
    /// 封装交易构建和发送逻辑
    /// </summary>
    /// <param name="instructions"></param>
    /// <returns></returns>
    private async Task<(bool Successful, string Signature, string ErrorMsg)> BuildAndSendTableAddressTransaction(
        List<TransactionInstruction> instructions)
    {
        var lookupTableAddress =
            IsDev ? RaydiumClmmProgram.DevLookupTableAddress : RaydiumClmmProgram.LookupTableAddress;

        var programId = instructions.Select(s => new PublicKey(s.ProgramId)).ToList().Distinct().ToList();
        var writableSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && s.IsSigner && s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var readonlySigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !writableSigners.Contains(s.PublicKey) &&
                        s.IsSigner && !s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var writableNonSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !s.IsSigner && s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var readonlyNonSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !s.IsSigner && !s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();

        ////获取地址表信息
        //var accountInfo =
        //    await RpcClient.GetAccountInfoAsync(lookupTableAddress, Commitment.Finalized, BinaryEncoding.JsonParsed);
        //if (accountInfo.Result.Value == null)
        //{
        //    await Task.Delay(100);
        //    accountInfo =
        //        await RpcClient.GetAccountInfoAsync(lookupTableAddress, Commitment.Finalized,
        //            BinaryEncoding.JsonParsed);
        //    Console.WriteLine("获取地址表=>" + accountInfo.RawRpcResponse);
        //    if (accountInfo.Result.Value == null)
        //    {
        //        Console.WriteLine($"获取地址表失败  退出交易");
        //        return (false, "", "获取地址表失败");
        //    }
        //}

        //var tableAddresses = ExtractAddressesFromJson(accountInfo.Result.Value.Data[0]);

        var tableAddresses = AABotProgram.TableAddressRaydium(IsDev);
        var write_data = new List<byte>();
        var read_data = new List<byte>();
        for (int i = 0; i < instructions.Count; i++)
        {
            for (int j = 0; j < instructions[i].Keys.Count; j++)
            {
                var key = instructions[i].Keys[j];
                if (programId.Contains(new PublicKey(key.PublicKey))) continue;
                var index = tableAddresses.IndexOf(new PublicKey(key.PublicKey));
                if (index > -1)
                {
                    if (key.IsWritable)
                    {
                        if (!write_data.Contains(Convert.ToByte(index)))
                            write_data.Add(Convert.ToByte(index));
                    }
                    else
                    {
                        if (!read_data.Contains(Convert.ToByte(index)))
                            read_data.Add(Convert.ToByte(index));
                    }
                }
            }
        }

        var lookupTableAccount = new List<Message.MessageAddressTableLookup>
        {
            new Message.MessageAddressTableLookup()
            {
                AccountKey = lookupTableAddress,
                WritableIndexes = write_data.ToArray(),
                ReadonlyIndexes = read_data.ToArray()
            }
        };


        var AccountKeys = new List<PublicKey>();
        AccountKeys.AddRange(writableSigners.Select(s => new PublicKey(s)).ToList());
        AccountKeys.AddRange(readonlySigners.Select(s => new PublicKey(s)).ToList());
        var AccountKeysWritableNonSigners = writableNonSigners.Where(s => !tableAddresses.Contains(new PublicKey(s)))
            .Select(s => new PublicKey(s)).ToList();
        if (AccountKeysWritableNonSigners != null && AccountKeysWritableNonSigners.Any())
            AccountKeys.AddRange(AccountKeysWritableNonSigners);
        AccountKeys.AddRange(programId);
        var AccountKeysReadonlyNonSigners = readonlyNonSigners.Where(s => !tableAddresses.Contains(new PublicKey(s)))
            .Select(s => new PublicKey(s)).ToList();
        AccountKeys.AddRange(AccountKeysReadonlyNonSigners);
        AccountKeys = AccountKeys.Distinct().ToList();
        var StaticAccountKeys = new List<PublicKey>();
        StaticAccountKeys.AddRange(AccountKeys);
        StaticAccountKeys.AddRange(writableNonSigners.Select(s => new PublicKey(s)).ToList());
        StaticAccountKeys.AddRange(readonlyNonSigners.Select(s => new PublicKey(s)).ToList());
        StaticAccountKeys = StaticAccountKeys.Distinct().ToList();

        var compileInstructions = new List<CompiledInstruction>();

        for (int i = 0; i < instructions.Count; i++)
        {
            var item = instructions[i];
            var KeyIndices = item.Keys.Select(s =>
                {
                    return Convert.ToByte(StaticAccountKeys.IndexOf(new PublicKey(s.PublicKey)));
                }
            ).ToArray();
            var compileIns = new CompiledInstruction()
            {
                ProgramIdIndex = Convert.ToByte(AccountKeys.IndexOf(new PublicKey(item.ProgramId))),
                Data = item.Data,
                DataLength = new byte[] { Convert.ToByte(item.Data.Length) },
                KeyIndices = KeyIndices,
                KeyIndicesCount = new byte[] { Convert.ToByte(KeyIndices.Length) },
            };
            compileInstructions.Add(compileIns);
        }

        var res = await RpcClient.GetLatestBlockHashAsync();
        LatestBlockHash latestBlockHash = res?.Result?.Value;
        if (latestBlockHash == null)
        {
            await Task.Delay(100);
            res = await RpcClient.GetLatestBlockHashAsync();
            latestBlockHash = res?.Result?.Value;
        }

        var message = new Message.VersionedMessage()
        {
            Header = new MessageHeader()
            {
                RequiredSignatures = Convert.ToByte(writableSigners.Count + readonlySigners.Count),
                ReadOnlySignedAccounts = Convert.ToByte(readonlySigners.Count),
                ReadOnlyUnsignedAccounts = Convert.ToByte(AccountKeys.Count - AccountKeysWritableNonSigners.Count -
                                                          readonlySigners.Count - writableSigners.Count),
            },
            AccountKeys = AccountKeys,
            AddressTableLookups = lookupTableAccount,
            Instructions = compileInstructions,
            RecentBlockhash = latestBlockHash?.Blockhash,
        };

        var sign = new List<Solnet.Wallet.Account> { Trader };
        //sign.AddRange(player);

        var CompileTx = VersionedTransaction.Populate(message);
        CompileTx.Sign(sign);

        var response =
            await RpcClient.SendTransactionAsync(CompileTx.VersionedSerialize(), commitment: Commitment.Confirmed);
        _logger?.LogDebug(response.RawRpcResponse);
        Console.WriteLine(response.RawRpcResponse);
        return (response.WasSuccessful, response.Result, response.Reason);
    }

    /// <summary>
    /// 发送交易的公共方法 
    /// </summary>
    /// <param name="transactions"></param>
    /// <returns></returns>
    private async Task<(bool Successful, string Signature, string ErrorMsg)> SendTransactionAsync(
        List<TransactionInstruction> transactions)
    {
        var blockhash = await RpcClient.GetLatestBlockHashAsync();
        var builder = new VersionedTransaction
        {
            FeePayer = Trader.PublicKey,
            RecentBlockHash = blockhash.Result.Value.Blockhash,
            Instructions = transactions,
            AddressTableLookups = new()
        };

        builder.Sign(new List<Account> { Trader });
        var transaction = await RpcClient.SendTransactionAsync(builder.Serialize(), commitment: Commitment.Confirmed);
        _logger?.LogDebug(transaction.RawRpcResponse);
        Console.WriteLine($"SendTransaction=>{transaction.RawRpcResponse}");
        return (transaction.WasSuccessful, transaction.Result, transaction.Reason);
    }

    private async Task<SwapV2BaseInAccounts> BuildSwapAccountsAsync(string mintAddress,
        string[] accountAddresses = null, bool zero_for_one = false)
    {
        var mint = new PublicKey(mintAddress);
        var accounts = new SwapV2BaseInAccounts() { Payer = Trader.PublicKey };
        var programId = IsDev
            ? RaydiumClmmProgram.RAYDIUM_Clmm_PROGRAM_ID_Dev
            : RaydiumClmmProgram.RAYDIUM_Clmm_PROGRAM_ID;
        if (accountAddresses != null)
        {
            accounts.PoolState = new PublicKey(accountAddresses[0]);
            var mint0 = new PublicKey(accountAddresses[1]);
            var mint1 = new PublicKey(accountAddresses[2]);
            var tokenVault0 = new PublicKey(accountAddresses[3]);
            var tokenVault1 = new PublicKey(accountAddresses[4]);
            accounts.InputVault = zero_for_one == (mint0 == RaydiumClmmProgram.WSOL)
                ? tokenVault0
                : tokenVault1;
            accounts.OutputVault = zero_for_one == (mint0 == RaydiumClmmProgram.WSOL)
                ? tokenVault1
                : tokenVault0;
            accounts.InputVaultMint = zero_for_one == (mint0 == RaydiumClmmProgram.WSOL)
                ? mint0
                : mint1;
            accounts.OutputVaultMint = zero_for_one == (mint0 == RaydiumClmmProgram.WSOL)
                ? mint1
                : mint0;
            accounts.ObservationState = new PublicKey(accountAddresses[5]);

            accounts.TickArrayAccount1 = ClmmPda.GetClmmExBitmap(programId, accounts.PoolState).Address;
            /*var index = new ClmmTickArray().GetFirstInitializedTickArray(
                new ClmmTickArray.TickArrayBitmapPoolInfo()
                {
                    tickSpacing = (int)poolState.TickSpacing,
                    tickCurrent = poolState.TickCurrent,
                    tickArrayBitmap = poolState.TickArrayBitmap,
                    exBitmapInfo = new ClmmTickArray.TickArrayBitmapExtensionType()
                    {
                        PoolId = tickArrayAccount.PoolId,
                        PositiveTickArrayBitmap = tickArrayAccount.PositiveTickArrayBitmap,
                        NegativeTickArrayBitmap = tickArrayAccount.NegativeTickArrayBitmap,
                    }
                }, zero_for_one);
            accounts.TickArrayAccount2 = ClmmPda.GetClmmTickArray(programId, poolId, index ?? 0).Address;*/
        }
        else
        {
            var ammConfig = IsDev ? RaydiumClmmProgram.AmmConfig_Dev : RaydiumClmmProgram.AmmConfig;
            var poolId = ClmmPda.GetClmmPoolId(programId, mint, ammConfig).Address;
            var poolState = await GetPoolState(poolId);
            if (poolState == null) return null;
            /*var zero_for_one = mintAddress == poolState.TokenMint0.Key &&
                               RaydiumClmmProgram.WSOL == poolState.TokenMint1.Key;*/
            accounts.PoolState = poolId;

            accounts.InputVault = zero_for_one == (poolState.TokenMint0 == RaydiumClmmProgram.WSOL)
                ? poolState.TokenVault0
                : poolState.TokenVault1;
            accounts.OutputVault = zero_for_one == (poolState.TokenMint0 == RaydiumClmmProgram.WSOL)
                ? poolState.TokenVault1
                : poolState.TokenVault0;
            accounts.InputVaultMint = zero_for_one == (poolState.TokenMint0 == RaydiumClmmProgram.WSOL)
                ? poolState.TokenMint0
                : poolState.TokenMint1;
            accounts.OutputVaultMint = zero_for_one == (poolState.TokenMint0 == RaydiumClmmProgram.WSOL)
                ? poolState.TokenMint1
                : poolState.TokenMint0;

            accounts.ObservationState = poolState.ObservationKey;
            accounts.TickArrayAccount1 = ClmmPda.GetClmmExBitmap(programId, poolId).Address;
            var tickArrayAccount = await GetTickArrayBitmap(accounts.TickArrayAccount1.Key);
            var index = new ClmmTickArray().GetFirstInitializedTickArray(
                new ClmmTickArray.TickArrayBitmapPoolInfo()
                {
                    tickSpacing = (int)poolState.TickSpacing,
                    tickCurrent = poolState.TickCurrent,
                    tickArrayBitmap = poolState.TickArrayBitmap,
                    exBitmapInfo = new ClmmTickArray.TickArrayBitmapExtensionType()
                    {
                        PoolId = tickArrayAccount.PoolId,
                        PositiveTickArrayBitmap = tickArrayAccount.PositiveTickArrayBitmap,
                        NegativeTickArrayBitmap = tickArrayAccount.NegativeTickArrayBitmap,
                    }
                }, zero_for_one);
            accounts.TickArrayAccount2 = ClmmPda.GetClmmTickArray(programId, poolId, index ?? 0).Address;
        }

        return accounts;
    }

    #endregion

    public async Task<(bool Successful, string Signature, string ErrorMsg)> SwapInAsync(string mintAddress, decimal sol,
        decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0, bool isRise = false,string blockHash="")
    {
        var transactions = await GetBuy(mintAddress, sol, slippage, fee, mev, accountAddresses, tokenPrice, isRise,
            blockHash);
        //Console.WriteLine(JsonSerializer.Serialize(transactions));
        var lookupTableAddress =
            IsDev ? RaydiumClmmProgram.DevLookupTableAddress : RaydiumClmmProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions,null,lookupTableAddress,mev,blockHash);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> SwapOutAsync(string mintAddress,
        decimal token_Amount, decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0,string blockHash="")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);

        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);
        ulong tokenAmountLamports = (ulong)(token_Amount * 1e6m);
        var swapAccounts = await BuildSwapAccountsAsync(mintAddress, accountAddresses, false);
        if (swapAccounts == null) return (false, string.Empty, "Accounts is null");

        swapAccounts.InputTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));
        swapAccounts.OutputTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, RaydiumClmmProgram.WSOL);

        transactions.Add(RaydiumClmmProgram.SwapOut(swapAccounts, tokenAmountLamports, 0ul, slippagePercentage,
            IsDev));

        /*
        var (cu, err) = await GetSimulationUnits(transactions);
        if (err != null)
        {
            return (false, string.Empty, err);
        }

        if (cu > 0)
        {
            cu = Math.Ceiling(cu * 1.1m); // 增加 10% 缓冲
            (computeBudget, computePrice) = GetComputeInstructions(fee, mev, cu);
        }*/

        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);
        return await BuildAndSendTableAddressTransaction(transactions);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> SwapOutByPercentAsync(string mintAddress,
        decimal token_Percent, decimal slippage, decimal fee = 0,
        decimal mev = 0, string[] accountAddresses = null, decimal tokenPrice = 0,string blockHash="")
    {
        var transactions=await GetSellPercentage(mintAddress, token_Percent, slippage, fee, mev, accountAddresses, tokenPrice, blockHash);
        var lookupTableAddress =
            IsDev ? RaydiumClmmProgram.DevLookupTableAddress : RaydiumClmmProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions,null,lookupTableAddress,mev,blockHash);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> BuySell(string mintAddress, decimal sol,
        decimal slippage, decimal fee = 0, decimal mev = 0,string blockHash="")
    {
        var transactions=await GetBuySell(mintAddress, sol, slippage, fee, mev, null, blockHash);
        //return await SendTransactionAsync(transactions);
        var lookupTableAddress =
            IsDev ? RaydiumClmmProgram.DevLookupTableAddress : RaydiumClmmProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions,null,lookupTableAddress,mev,blockHash);
    }

    public async Task<List<TransactionInstruction>> GetBuy(string mintAddress, decimal sol, decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0, bool isRise = false, string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);
        var swapAccounts = await BuildSwapAccountsAsync(mintAddress, accountAddresses, true);
        if (swapAccounts == null) return null;

        // 创建并初始化源账户
        var seed = new Account().PublicKey.Key.Substring(0, 32);
        PublicKey.TryCreateWithSeed(Trader.PublicKey, seed, TokenProgram.ProgramIdKey, out var source);
        transactions.Add(SystemProgram.CreateAccountWithSeed(
            Trader.PublicKey, source, Trader.PublicKey, seed, (ulong)(2039280 + solAmount), 165,
            TokenProgram.ProgramIdKey));
        transactions.Add(TokenProgram.InitializeAccount(source, RaydiumClmmProgram.WSOL, Trader.PublicKey));
        swapAccounts.InputTokenAccount = source;

        swapAccounts.OutputTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));

        transactions.Add(RaydiumClmmProgram.SwapIn(swapAccounts, solAmount, slippagePercentage, IsDev, tokenPriceUl,
            isRise));
        
        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);
        return transactions;
    }

    public async Task<List<TransactionInstruction>> GetSellPercentage(string mintAddress, decimal percentage, decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0, string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);

        var tokenPercentLamports = (ulong)percentage;
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);
        var swapAccounts = await BuildSwapAccountsAsync(mintAddress, accountAddresses, false);
        if (swapAccounts == null) return null;

        swapAccounts.InputTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));
        swapAccounts.OutputTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, RaydiumClmmProgram.WSOL);

        transactions.Add(RaydiumClmmProgram.SwapOut(swapAccounts, 0ul, tokenPercentLamports, slippagePercentage,
            IsDev));
        
        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);

        return transactions;
    }

    public async Task<List<TransactionInstruction>> GetBuySell(string mintAddress, decimal sol, decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var swapAccounts = await BuildSwapAccountsAsync(mintAddress, accountAddresses: null, true);
        if (swapAccounts == null) return null;
        // 创建并初始化源账户
        var seed = new Account().PublicKey.Key.Substring(0, 32);
        PublicKey.TryCreateWithSeed(Trader.PublicKey, seed, TokenProgram.ProgramIdKey, out var source);
        transactions.Add(SystemProgram.CreateAccountWithSeed(
            Trader.PublicKey, source, Trader.PublicKey, seed, (ulong)(2039280 + solAmount), 165,
            TokenProgram.ProgramIdKey));
        transactions.Add(TokenProgram.InitializeAccount(source, RaydiumClmmProgram.WSOL, Trader.PublicKey));
        swapAccounts.InputTokenAccount = source;
        swapAccounts.OutputTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));
        transactions.Add(RaydiumClmmProgram.SwapIn(swapAccounts, solAmount, slippagePercentage, IsDev, 0, false));
        //transactions.Add(TokenProgram.CloseAccount(source, Trader.PublicKey, Trader.PublicKey, TokenProgram.ProgramIdKey));
        swapAccounts = await BuildSwapAccountsAsync(mintAddress, accountAddresses: null, false);
        swapAccounts.InputTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));
        swapAccounts.OutputTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, RaydiumClmmProgram.WSOL);

        transactions.Add(RaydiumClmmProgram.SwapOut(swapAccounts, 0ul, 100, slippagePercentage, IsDev));

        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);


        return transactions;

    }

    public Task<List<TransactionInstruction>> GetBuyToUser(string mintAddress, decimal sol, decimal slippage, string user, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, string blockHash = "")
    {
        throw new NotImplementedException();
    }
}