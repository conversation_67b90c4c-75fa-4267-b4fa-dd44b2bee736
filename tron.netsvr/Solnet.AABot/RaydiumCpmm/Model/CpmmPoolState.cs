using Solnet.Programs.Utilities;
using Solnet.Wallet;

namespace Solnet.AABot.RaydiumCpmm.Model;

public class CpmmPoolState
{
    public PublicKey AmmConfig { get; set; }
    public PublicKey PoolCreator { get; set; }
    public PublicKey Token0Vault { get; set; }
    public PublicKey Token1Vault { get; set; }
    public PublicKey LpMint { get; set; }
    public PublicKey Token0Mint { get; set; }
    public PublicKey Token1Mint { get; set; }
    public PublicKey Token0Program { get; set; }
    public PublicKey Token1Program { get; set; }
    public PublicKey ObservationKey { get; set; }
    public int AuthBump { get; set; }
    public int Status { get; set; }
    public int LpMintDecimals { get; set; }
    public int Mint0Decimals { get; set; }
    public int Mint1Decimals { get; set; }
    public ulong LpSupply { get; set; }
    public ulong ProtocolFeesToken0 { get; set; }
    public ulong ProtocolFeesToken1 { get; set; }
    public ulong FundFeesToken0 { get; set; }
    public ulong FundFeesToken1 { get; set; }
    public ulong OpenTime { get; set; }
    
    public static CpmmPoolState Deserialize(ReadOnlySpan<byte> _data, int initialOffset=8)
    {
        var result = new CpmmPoolState();
        int offset = initialOffset;
        result.AmmConfig = _data.GetPubKey(offset);
        offset += 32;
        result.PoolCreator = _data.GetPubKey(offset);
        offset += 32;
        result.Token0Vault = _data.GetPubKey(offset);
        offset += 32;
        result.Token1Vault = _data.GetPubKey(offset);
        offset += 32;
        result.LpMint = _data.GetPubKey(offset);
        offset += 32;
        result.Token0Mint = _data.GetPubKey(offset);
        offset += 32;
        result.Token1Mint = _data.GetPubKey(offset);
        offset += 32;
        result.Token0Program = _data.GetPubKey(offset);
        offset += 32;
        result.Token1Program = _data.GetPubKey(offset);
        offset += 32;
        result.ObservationKey = _data.GetPubKey(offset);
        offset += 32;
        result.AuthBump = _data.GetU8(offset);
        offset += 1;
        result.Status = _data.GetU8(offset);
        offset += 1;
        result.LpMintDecimals = _data.GetU8(offset);
        offset += 1;
        result.Mint0Decimals = _data.GetU8(offset);
        offset += 1;
        result.Mint1Decimals = _data.GetU8(offset);
        offset += 1;
        result.LpSupply = _data.GetU64(offset);
        offset += 8;
        result.ProtocolFeesToken0 = _data.GetU64(offset);
        offset += 8;
        result.ProtocolFeesToken1 = _data.GetU64(offset);
        offset += 8;
        result.FundFeesToken0 = _data.GetU64(offset);
        offset += 8;
        result.FundFeesToken1 = _data.GetU64(offset);
        offset += 8;
        result.OpenTime = _data.GetU64(offset);
        offset += 8;
        return result;
    }
}