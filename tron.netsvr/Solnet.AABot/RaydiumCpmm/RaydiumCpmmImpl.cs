using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Solnet.AABot.RaydiumCpmm.Model;
using Solnet.Programs;
using Solnet.Programs.Models;
using Solnet.Programs.Utilities;
using Solnet.Rpc;
using Solnet.Rpc.Models;
using Solnet.Rpc.Types;
using Solnet.Wallet;

namespace Solnet.AABot.RaydiumCpmm;

public class RaydiumCpmmImpl : AABotProgram,IRaydiumCpmm, IAABot
{
    /*public IRpcClient RpcClient { get; set; }
    private Account Trader { get; set; }
    private bool IsDev = false;
    protected readonly ILogger _logger;*/

    public RaydiumCpmmImpl(IRpcClient rpc, Account _trader,ILogger logger):base(rpc, null,_trader, logger)
    {
        RpcClient = rpc;
        Trader = _trader;
        IsDev = rpc.NodeAddress.Host.Contains("devnet");
        _logger =logger?? new LoggerFactory().CreateLogger("RaydiumCpmmImpl");
    }
    public RaydiumCpmmImpl(IRpcClient rpc,IRpcClient zeroSlotRpcClient, Account _trader, ILogger logger):base(rpc, zeroSlotRpcClient,_trader, logger)
    {
        RpcClient = rpc;
        Trader = _trader;
        IsDev = rpc.NodeAddress.Host.Contains("devnet");
        _logger = logger ?? new LoggerFactory().CreateLogger("RaydiumCpmmImpl");
    }
    private async Task<AccountResultWrapper<CpmmPoolState>> GetPollInfoAsync(string poolAddress,
        Commitment commitment = Commitment.Finalized)
    {
        if (string.IsNullOrEmpty(poolAddress)) return null;
        var res = await RpcClient.GetAccountInfoAsync(poolAddress, commitment);
        if (!res.WasSuccessful || res.Result == null || res.Result.Value == null)
        {
            _logger.LogError($"GetAmmInfoAsync failed: {res}");
            return null;
        }

        var resultingAccount = CpmmPoolState.Deserialize(Convert.FromBase64String(res.Result.Value.Data[0]));
        return new AccountResultWrapper<CpmmPoolState>(res, resultingAccount);
    }

    private (TransactionInstruction computeBudget, TransactionInstruction computePrice)
        GetComputeInstructions(decimal fee = 0, decimal mev = 0, decimal cu = 1_000_000m)
    {
        //var cu = 1_000_000m;
        fee = fee == 0 ? 0.000005m : fee;
        var microLamports = (ulong)((fee - 0.000005M) * cu);
        var jito = (ulong)(mev * 1e9m); // 未使用，可以保留以备将来扩展

        var computebudget = (ulong)cu;
        var computeprice = (ulong)microLamports * 1000;
        TransactionInstruction computeBudget = RaydiumCpmmProgram.SetCUlimit(computebudget);
        TransactionInstruction computePrice = ComputeBudgetProgram.SetComputeUnitPrice(computeprice);

        return (computeBudget, computePrice);
    }

    private PublicKey DeriveAssociatedTokenAccount(PublicKey owner,PublicKey mint,PublicKey programId)
    {
        bool success = PublicKey.TryFindProgramAddress(
            new List<byte[]> { owner.KeyBytes, programId.KeyBytes, mint.KeyBytes },
            AssociatedTokenAccountProgram.ProgramIdKey, out PublicKey derivedAssociatedTokenAddress, out _);
        return derivedAssociatedTokenAddress;
    }
    
    /// <summary>
    /// 模拟 提交
    /// </summary>
    /// <param name="transactions"></param>
    /// <returns></returns>
    private async Task<(decimal, string?)> GetSimulationUnits(List<TransactionInstruction> transactions)
    {
        var blockhash = await RpcClient.GetLatestBlockHashAsync();
        VersionedTransaction tx = new VersionedTransaction
        {
            FeePayer = Trader,
            RecentBlockHash = blockhash?.Result?.Value?.Blockhash,
            Instructions = new(),
            AddressTableLookups = new()
        };

        tx.Instructions.AddRange(transactions);
        tx.Sign(new List<Solnet.Wallet.Account> { Trader });

        var result = await RpcClient.SimulateTransactionAsync(tx.Serialize(), commitment: Commitment.Confirmed);
        Console.WriteLine("GetSimulationUnits=>" + result.RawRpcResponse);
        if (result.WasSuccessful)
        {
            JsonDocument doc = JsonDocument.Parse(result.RawRpcResponse);
            JsonElement root = doc.RootElement;

            JsonElement resultElement = default;
            JsonElement valueElement = default;
            JsonElement unitsConsumedElement = default;
            if (result.Result.Value.Error != null)
            {
                _logger?.LogDebug("GetSimulationUnits=>" + result.RawRpcResponse);
                return (-1, result.Result.Value.Error.Type.ToString());
            }

            // 使用 TryGetProperty 减少嵌套
            bool success = root.TryGetProperty("result", out resultElement) &&
                           resultElement.TryGetProperty("value", out valueElement) &&
                           valueElement.TryGetProperty("unitsConsumed", out unitsConsumedElement) &&
                           unitsConsumedElement.ValueKind == JsonValueKind.Number;
            if (success) return (unitsConsumedElement.GetDecimal(), null);
            else return (0m, null);

            ulong unitsConsumed = doc.RootElement
                .GetProperty("result")
                .GetProperty("value")
                .GetProperty("unitsConsumed").GetUInt64();
        }

        return (0m, null);
    }
    
    
    /// <summary>
    /// 解析 地址表 数据
    /// </summary>
    /// <param name="jsonData"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private List<PublicKey> ExtractAddressesFromJson(string jsonData)
    {
        try
        {
            using var document = JsonDocument.Parse(jsonData);
            var addressesElement = document.RootElement
                .GetProperty("parsed")
                .GetProperty("info")
                .GetProperty("addresses");

            var addresses = new List<PublicKey>();
            foreach (var address in addressesElement.EnumerateArray())
            {
                addresses.Add(new PublicKey(address.GetString()));
            }

            return addresses;
        }
        catch (Exception ex)
        {
            throw new Exception($"无法从 JSON 提取 addresses 数组：{ex.Message}");
        }
    }

    /// <summary>
    /// 封装交易构建和发送逻辑
    /// </summary>
    /// <param name="instructions"></param>
    /// <returns></returns>
    private async Task<(bool Successful, string Signature, string ErrorMsg)> BuildAndSendTableAddressTransaction(
        List<TransactionInstruction> instructions)
    {
        var lookupTableAddress = IsDev ?  RaydiumCpmmProgram.DevLookupTableAddress : RaydiumCpmmProgram.LookupTableAddress;

        var programId = instructions.Select(s => new PublicKey(s.ProgramId)).ToList().Distinct().ToList();
        var writableSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && s.IsSigner && s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var readonlySigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !writableSigners.Contains(s.PublicKey) && s.IsSigner && !s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var writableNonSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !s.IsSigner && s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var readonlyNonSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !s.IsSigner && !s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();

        //获取地址表信息
        //var accountInfo =
        //    await RpcClient.GetAccountInfoAsync(lookupTableAddress, Commitment.Finalized, BinaryEncoding.JsonParsed);
        //if (accountInfo.Result.Value == null)
        //{
        //    await Task.Delay(100);
        //    accountInfo =
        //        await RpcClient.GetAccountInfoAsync(lookupTableAddress, Commitment.Finalized,
        //            BinaryEncoding.JsonParsed);
        //    Console.WriteLine("获取地址表=>" + accountInfo.RawRpcResponse);
        //    if (accountInfo.Result.Value == null)
        //    {
        //        Console.WriteLine($"获取地址表失败  退出交易");
        //        return (false, "", "获取地址表失败");
        //    }
        //}

        //var tableAddresses = ExtractAddressesFromJson(accountInfo.Result.Value.Data[0]);

        var tableAddresses = AABotProgram.TableAddressRaydium(IsDev);
        var write_data = new List<byte>();
        var read_data = new List<byte>();
        for (int i = 0; i < instructions.Count; i++)
        {
            for (int j = 0; j < instructions[i].Keys.Count; j++)
            {
                var key = instructions[i].Keys[j];
                if (programId.Contains(new PublicKey(key.PublicKey))) continue;
                var index = tableAddresses.IndexOf(new PublicKey(key.PublicKey));
                if (index > -1)
                {
                    if (key.IsWritable)
                    {
                        if (!write_data.Contains(Convert.ToByte(index)))
                            write_data.Add(Convert.ToByte(index));
                    }
                    else
                    {
                        if (!read_data.Contains(Convert.ToByte(index)))
                            read_data.Add(Convert.ToByte(index));
                    }
                }
            }
        }

        var lookupTableAccount = new List<Message.MessageAddressTableLookup>
        {
            new Message.MessageAddressTableLookup()
            {
                AccountKey = lookupTableAddress,
                WritableIndexes = write_data.ToArray(),
                ReadonlyIndexes = read_data.ToArray()
            }
        };


        var AccountKeys = new List<PublicKey>();
        AccountKeys.AddRange(writableSigners.Select(s => new PublicKey(s)).ToList());
        AccountKeys.AddRange(readonlySigners.Select(s => new PublicKey(s)).ToList());
        var AccountKeysWritableNonSigners = writableNonSigners.Where(s => !tableAddresses.Contains(new PublicKey(s)))
            .Select(s => new PublicKey(s)).ToList();
        if(AccountKeysWritableNonSigners!=null && AccountKeysWritableNonSigners.Any())
            AccountKeys.AddRange(AccountKeysWritableNonSigners);
        AccountKeys.AddRange(programId);
        var AccountKeysReadonlyNonSigners = readonlyNonSigners.Where(s => !tableAddresses.Contains(new PublicKey(s)))
            .Select(s => new PublicKey(s)).ToList();
        AccountKeys.AddRange(AccountKeysReadonlyNonSigners);
        AccountKeys = AccountKeys.Distinct().ToList();
        var StaticAccountKeys = new List<PublicKey>();
        StaticAccountKeys.AddRange(AccountKeys);
        StaticAccountKeys.AddRange(writableNonSigners.Select(s => new PublicKey(s)).ToList());
        StaticAccountKeys.AddRange(readonlyNonSigners.Select(s => new PublicKey(s)).ToList());
        StaticAccountKeys = StaticAccountKeys.Distinct().ToList();

        var compileInstructions = new List<CompiledInstruction>();

        for (int i = 0; i < instructions.Count; i++)
        {
            var item = instructions[i];
            var KeyIndices = item.Keys.Select(s =>
                {
                    return Convert.ToByte(StaticAccountKeys.IndexOf(new PublicKey(s.PublicKey)));
                }
            ).ToArray();
            var compileIns = new CompiledInstruction()
            {
                ProgramIdIndex = Convert.ToByte(AccountKeys.IndexOf(new PublicKey(item.ProgramId))),
                Data = item.Data,
                DataLength = new byte[] { Convert.ToByte(item.Data.Length) },
                KeyIndices = KeyIndices,
                KeyIndicesCount = new byte[] { Convert.ToByte(KeyIndices.Length) },
            };
            compileInstructions.Add(compileIns);
        }

        var res = await RpcClient.GetLatestBlockHashAsync();
        LatestBlockHash latestBlockHash = res?.Result?.Value;
        if (latestBlockHash == null)
        {
            await Task.Delay(100);
            res = await RpcClient.GetLatestBlockHashAsync();
            latestBlockHash = res?.Result?.Value;
        }

        var message = new Message.VersionedMessage()
        {
            Header = new MessageHeader()
            {
                RequiredSignatures = Convert.ToByte(writableSigners.Count + readonlySigners.Count),
                ReadOnlySignedAccounts = Convert.ToByte(readonlySigners.Count),
                ReadOnlyUnsignedAccounts = Convert.ToByte(AccountKeys.Count - AccountKeysWritableNonSigners.Count -
                                                          readonlySigners.Count - writableSigners.Count),
            },
            AccountKeys = AccountKeys,
            AddressTableLookups = lookupTableAccount,
            Instructions = compileInstructions,
            RecentBlockhash = latestBlockHash?.Blockhash,
        };

        var sign = new List<Solnet.Wallet.Account> { Trader };
        //sign.AddRange(player);

        var CompileTx = VersionedTransaction.Populate(message);
        CompileTx.Sign(sign);

        var response =
            await RpcClient.SendTransactionAsync(CompileTx.VersionedSerialize(), commitment: Commitment.Confirmed);
        _logger?.LogDebug(response.RawRpcResponse);
        Console.WriteLine(response.RawRpcResponse);
        return (response.WasSuccessful, response.Result, response.Reason);
    }

    // 发送交易的公共方法
    private async Task<(bool Successful, string Signature, string ErrorMsg)> SendTransactionAsync(
        List<TransactionInstruction> transactions)
    {
        var blockhash = await RpcClient.GetLatestBlockHashAsync();
        var builder = new VersionedTransaction
        {
            FeePayer = Trader.PublicKey,
            RecentBlockHash = blockhash.Result.Value.Blockhash,
            Instructions = transactions,
            AddressTableLookups = new()
        };

        builder.Sign(new List<Account> { Trader });
        var transaction = await RpcClient.SendTransactionAsync(builder.Serialize(), commitment: Commitment.Confirmed);
        _logger?.LogDebug(transaction.RawRpcResponse);
        return (transaction.WasSuccessful, transaction.Result, transaction.Reason);
    }

    private async Task<CpmmSwapInAccounts> BuildSwapIn(string mintAddress, string[] accountAddresses = null)
    {
        var mint = new PublicKey(mintAddress);
        PublicKey.TryFindProgramAddress(
            [
                Encoding.UTF8.GetBytes("pool"),
                IsDev
                    ? RaydiumCpmmProgram.Raydium_Cpmm_AmmConfig_Dev
                    : RaydiumCpmmProgram.Raydium_Cpmm_AmmConfig.KeyBytes,
                RaydiumCpmmProgram.WSOL.KeyBytes,
                mint.KeyBytes
            ],
            IsDev ? RaydiumCpmmProgram.RAYDIUM_Cpmm_PROGRAM_ID_Dev : RaydiumCpmmProgram.RAYDIUM_Cpmm_PROGRAM_ID,
            out PublicKey pool_account_key, out _);
        if (accountAddresses != null && accountAddresses.Length > 0)
        {
            var swapAccount = new CpmmSwapInAccounts();
            var isWsol = accountAddresses[5] == RaydiumCpmmProgram.WSOL.Key;
            swapAccount.Payer = Trader.PublicKey;
            swapAccount.PoolState = new PublicKey(accountAddresses[0]);
            swapAccount.InputVault = new PublicKey(accountAddresses[isWsol ? 1 : 2]);
            swapAccount.OutputVault = new PublicKey(accountAddresses[isWsol ? 2 : 1]);
            swapAccount.InputTokenProgram = new PublicKey(accountAddresses[isWsol ? 3 : 4]);
            swapAccount.OutputTokenProgram = new PublicKey(accountAddresses[isWsol ? 4 : 3]);
            swapAccount.InputTokenMint = new PublicKey(accountAddresses[isWsol ? 5 : 6]);
            swapAccount.OutputTokenMint = new PublicKey(accountAddresses[isWsol ? 6 : 5]);
            swapAccount.ObservationState = new PublicKey(accountAddresses[7]);
            return swapAccount;
        }
        else
        {
            var swapAccount = new CpmmSwapInAccounts();
            var poolState = await GetPollInfoAsync(pool_account_key.Key);
            if (!poolState.WasSuccessful || poolState.ParsedResult == null) return null;
            var isWsol = poolState.ParsedResult.Token0Mint.Key == RaydiumCpmmProgram.WSOL.Key;
            swapAccount.Payer = Trader.PublicKey;
            swapAccount.PoolState = pool_account_key;
            swapAccount.InputVault = isWsol ? poolState.ParsedResult.Token0Vault : poolState.ParsedResult.Token1Vault;
            swapAccount.OutputVault = isWsol ? poolState.ParsedResult.Token1Vault : poolState.ParsedResult.Token0Vault;
            swapAccount.InputTokenProgram =
                isWsol
                    ? poolState.ParsedResult.Token0Program
                    : poolState.ParsedResult.Token1Program;
            swapAccount.OutputTokenProgram =
                isWsol
                    ? poolState.ParsedResult.Token1Program
                    : poolState.ParsedResult.Token0Program;
            swapAccount.InputTokenMint = isWsol
                ? poolState.ParsedResult.Token0Mint
                : poolState.ParsedResult.Token1Mint;
            swapAccount.OutputTokenMint =
                isWsol
                    ? poolState.ParsedResult.Token1Mint
                    : poolState.ParsedResult.Token0Mint;
            swapAccount.ObservationState = poolState.ParsedResult.ObservationKey;


            return swapAccount;
        }

        return null;
    }

    private async Task<CpmmSwapInAccounts> BuildSwapOut(string mintAddress, string[] accountAddresses = null)
    {
        var mint = new PublicKey(mintAddress);
        PublicKey.TryFindProgramAddress(
            [
                Encoding.UTF8.GetBytes("pool"),
                IsDev
                    ? RaydiumCpmmProgram.Raydium_Cpmm_AmmConfig_Dev
                    : RaydiumCpmmProgram.Raydium_Cpmm_AmmConfig.KeyBytes,
                RaydiumCpmmProgram.WSOL.KeyBytes,
                mint.KeyBytes
            ],
            IsDev ? RaydiumCpmmProgram.RAYDIUM_Cpmm_PROGRAM_ID_Dev : RaydiumCpmmProgram.RAYDIUM_Cpmm_PROGRAM_ID,
            out PublicKey pool_account_key, out _);
        if (accountAddresses != null && accountAddresses.Length > 0)
        {
            var swapAccount = new CpmmSwapInAccounts();
            var isWsol = accountAddresses[5] != RaydiumCpmmProgram.WSOL.Key;
            swapAccount.Payer = Trader.PublicKey;
            swapAccount.PoolState = new PublicKey(accountAddresses[0]);
            swapAccount.InputVault = new PublicKey(accountAddresses[isWsol ? 1 : 2]);
            swapAccount.OutputVault = new PublicKey(accountAddresses[isWsol ? 2 : 1]);
            swapAccount.InputTokenProgram = new PublicKey(accountAddresses[isWsol ? 3 : 4]);
            swapAccount.OutputTokenProgram = new PublicKey(accountAddresses[isWsol ? 4 : 3]);
            swapAccount.InputTokenMint = new PublicKey(accountAddresses[isWsol ? 5 : 6]);
            swapAccount.OutputTokenMint = new PublicKey(accountAddresses[isWsol ? 6 : 5]);
            swapAccount.ObservationState = new PublicKey(accountAddresses[7]);
            return swapAccount;
        }
        else
        {
            var swapAccount = new CpmmSwapInAccounts();
            var poolState = await GetPollInfoAsync(pool_account_key.Key);
            if (!poolState.WasSuccessful || poolState.ParsedResult == null) return null;
            var isWsol = poolState.ParsedResult.Token0Mint.Key != RaydiumCpmmProgram.WSOL.Key;
            swapAccount.Payer = Trader.PublicKey;
            swapAccount.PoolState = pool_account_key;
            swapAccount.InputVault = isWsol ? poolState.ParsedResult.Token0Vault : poolState.ParsedResult.Token1Vault;
            swapAccount.OutputVault = isWsol ? poolState.ParsedResult.Token1Vault : poolState.ParsedResult.Token0Vault;
            swapAccount.InputTokenProgram =
                isWsol
                    ? poolState.ParsedResult.Token0Program
                    : poolState.ParsedResult.Token1Program;
            swapAccount.OutputTokenProgram =
                isWsol
                    ? poolState.ParsedResult.Token1Program
                    : poolState.ParsedResult.Token0Program;
            swapAccount.InputTokenMint = isWsol
                ? poolState.ParsedResult.Token0Mint
                : poolState.ParsedResult.Token1Mint;
            swapAccount.OutputTokenMint =
                isWsol
                    ? poolState.ParsedResult.Token1Mint
                    : poolState.ParsedResult.Token0Mint;
            swapAccount.ObservationState = poolState.ParsedResult.ObservationKey;


            return swapAccount;
        }

        return null;
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> SwapInAsync(string mintAddress, decimal sol,
        decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0, bool isRise = false,string blockHash="")
    {
        var transactions = await GetBuy(mintAddress, sol, slippage, fee, mev, accountAddresses, tokenPrice, isRise,
            blockHash);
        var lookupTableAddress = IsDev ?  RaydiumCpmmProgram.DevLookupTableAddress : RaydiumCpmmProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev,blockHash);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> SwapOutAsync(string mintAddress,
        decimal token_Amount, decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0,string blockHash="")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);

        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);
        return await BuildAndSendTableAddressTransaction(transactions);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> SwapOutByPercentAsync(string mintAddress,
        decimal tokenPercent, decimal slippage, decimal fee = 0,
        decimal mev = 0, string[] accountAddresses = null, decimal tokenPrice = 0,string blockHash="")
    {
        var transactions=await GetSellPercentage(mintAddress,tokenPercent,slippage,fee,mev,accountAddresses,tokenPrice,blockHash);
        var lookupTableAddress = IsDev ?  RaydiumCpmmProgram.DevLookupTableAddress : RaydiumCpmmProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev,blockHash);
    }
    
    public async Task<(bool Successful, string Signature, string ErrorMsg)> BuySell(string mintAddress, decimal sol, decimal slippage, decimal fee = 0, decimal mev = 0,string blockHash="")
    {
        var transactions=await GetBuySell(mintAddress, sol, slippage, fee, mev, null, blockHash);
        var lookupTableAddress = IsDev ?  RaydiumCpmmProgram.DevLookupTableAddress : RaydiumCpmmProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev,blockHash);
    }

    public async Task<List<TransactionInstruction>> GetBuy(string mintAddress, decimal sol, decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0, bool isRise = false, string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);


        var swapBaseInAccounts = await BuildSwapIn(mintAddress, accountAddresses: accountAddresses);
        if (swapBaseInAccounts == null) return null;

        // 创建并初始化源账户
        var seed = new Account().PublicKey.Key.Substring(0, 32);
        PublicKey.TryCreateWithSeed(Trader.PublicKey, seed, TokenProgram.ProgramIdKey, out var source);
        transactions.Add(SystemProgram.CreateAccountWithSeed(
            Trader.PublicKey, source, Trader.PublicKey, seed, (ulong)(2039280 + solAmount), 165,
            TokenProgram.ProgramIdKey));
        transactions.Add(TokenProgram.InitializeAccount(source, RaydiumCpmmProgram.WSOL, Trader.PublicKey));
        
        swapBaseInAccounts.InputTokenAccount = source;
        
        swapBaseInAccounts.OutputTokenAccount =
            DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress),swapBaseInAccounts.OutputTokenProgram);

        var swapInTransactions = RaydiumCpmmProgram.SwapIn(swapBaseInAccounts, solAmount, slippagePercentage,
            IsDev, tokenPriceUl, isRise);
        transactions.Add(swapInTransactions);
 

        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);
        return transactions;
    }

    public async Task<List<TransactionInstruction>> GetSellPercentage(string mintAddress, decimal percentage, decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0, string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var tokenPercentLamports = (ulong)percentage;
        var slippagePercentage =  (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice*1e19m);
        
        var swapBaseInAccounts = await BuildSwapOut(mintAddress, accountAddresses: accountAddresses);
        if (swapBaseInAccounts == null) return null;

        swapBaseInAccounts.InputTokenAccount =  DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress),swapBaseInAccounts.InputTokenProgram);
        swapBaseInAccounts.OutputTokenAccount =AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, RaydiumCpmmProgram.WSOL);
        var swapInTransactions = RaydiumCpmmProgram.SwapOut(swapBaseInAccounts, 0,tokenPercentLamports, slippagePercentage,
            IsDev, tokenPriceUl);
        transactions.Add(swapInTransactions);

        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);
        return transactions;
    }

    public async Task<List<TransactionInstruction>> GetBuySell(string mintAddress, decimal sol, decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, string blockHash = "")
    {
       var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);


        var swapBaseInAccounts = await BuildSwapIn(mintAddress, accountAddresses: null);
        if (swapBaseInAccounts == null) return null;

        // 创建并初始化源账户
        var seed = new Account().PublicKey.Key.Substring(0, 32);
        PublicKey.TryCreateWithSeed(Trader.PublicKey, seed, TokenProgram.ProgramIdKey, out var source);
        transactions.Add(SystemProgram.CreateAccountWithSeed(
            Trader.PublicKey, source, Trader.PublicKey, seed, (ulong)(2039280 + solAmount), 165,
            TokenProgram.ProgramIdKey));
        transactions.Add(TokenProgram.InitializeAccount(source, RaydiumCpmmProgram.WSOL, Trader.PublicKey));
        
        swapBaseInAccounts.InputTokenAccount = source;
        
        swapBaseInAccounts.OutputTokenAccount =
            DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress),swapBaseInAccounts.OutputTokenProgram);

        var swapInTransactions = RaydiumCpmmProgram.SwapIn(swapBaseInAccounts, solAmount, slippagePercentage, IsDev);
        transactions.Add(swapInTransactions);
        
         swapBaseInAccounts = await BuildSwapOut(mintAddress, accountAddresses: null);
        if (swapBaseInAccounts == null) return null;

        swapBaseInAccounts.InputTokenAccount =  DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress),swapBaseInAccounts.InputTokenProgram);
        swapBaseInAccounts.OutputTokenAccount =AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, RaydiumCpmmProgram.WSOL);
        var swapOutTransactions = RaydiumCpmmProgram.SwapOut(swapBaseInAccounts, 0,100, slippagePercentage, IsDev);
        transactions.Add(swapOutTransactions);
        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);

        return transactions;
    }

    public Task<List<TransactionInstruction>> GetBuyToUser(string mintAddress, decimal sol, decimal slippage, string user, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, string blockHash = "")
    {
        throw new NotImplementedException();
    }
}