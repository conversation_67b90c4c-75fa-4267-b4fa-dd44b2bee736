using Solnet.Programs.Utilities;
using Solnet.Wallet;

namespace Solnet.AABot.MeteoraDBC;

public class VolatilityTracker
{
    public ulong LastUpdateTimestamp { get; set; }
    public byte[] Padding { get; set; } = new byte[8];
    public UInt128 SqrtPriceReference { get; set; }
    public UInt128 VolatilityAccumulator { get; set; }
    public UInt128 VolatilityReference { get; set; }

    public static int Deserialize(ReadOnlySpan<byte> _data, int initialOffset, out VolatilityTracker result)
    {
        var offset = initialOffset;
        result = new VolatilityTracker();

        // Deserialize LastUpdateTimestamp (u64, 8 bytes)
        result.LastUpdateTimestamp = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize Padding ([u8; 8], 8 bytes)
        result.Padding = new byte[8];
        _data.Slice(offset, 8).CopyTo(result.Padding);
        offset += 8;

        // Deserialize SqrtPriceReference (u128, 16 bytes)
        offset += Utils.ReadUInt128(_data, offset, out var sqrtPriceReference);
        result.SqrtPriceReference = sqrtPriceReference;

        // Deserialize VolatilityAccumulator (u128, 16 bytes)
        offset += Utils.ReadUInt128(_data, offset, out var volatilityAccumulator);
        result.VolatilityAccumulator = volatilityAccumulator;

        // Deserialize VolatilityReference (u128, 16 bytes)
        offset += Utils.ReadUInt128(_data, offset, out var volatilityReference);
        result.VolatilityReference = volatilityReference;

        return offset - initialOffset; // Total: 8 + 8 + 16 + 16 + 16 = 64 bytes
    }
}

public class PoolMetrics
{
    public ulong TotalProtocolBaseFee { get; set; }
    public ulong TotalProtocolQuoteFee { get; set; }
    public ulong TotalTradingBaseFee { get; set; }
    public ulong TotalTradingQuoteFee { get; set; }

    public static int Deserialize(ReadOnlySpan<byte> _data, int initialOffset, out PoolMetrics result)
    {
        var offset = initialOffset;
        result = new PoolMetrics();

        // Deserialize TotalProtocolBaseFee (u64, 8 bytes)
        result.TotalProtocolBaseFee = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize TotalProtocolQuoteFee (u64, 8 bytes)
        result.TotalProtocolQuoteFee = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize TotalTradingBaseFee (u64, 8 bytes)
        result.TotalTradingBaseFee = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize TotalTradingQuoteFee (u64, 8 bytes)
        result.TotalTradingQuoteFee = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        return offset - initialOffset; // Total: 8 + 8 + 8 + 8 = 32 bytes
    }
}

public class DBCPool
{
    public VolatilityTracker VolatilityTracker { get; set; }
    public PublicKey Config { get; set; }
    public PublicKey Creator { get; set; }
    public PublicKey BaseMint { get; set; }
    public PublicKey BaseVault { get; set; }
    public PublicKey QuoteVault { get; set; }
    public ulong BaseReserve { get; set; }
    public ulong QuoteReserve { get; set; }
    public ulong ProtocolBaseFee { get; set; }
    public ulong ProtocolQuoteFee { get; set; }
    public ulong PartnerBaseFee { get; set; }
    public ulong PartnerQuoteFee { get; set; }
    public UInt128 SqrtPrice { get; set; }
    public ulong ActivationPoint { get; set; }
    public byte PoolType { get; set; }
    public byte IsMigrated { get; set; }
    public byte IsPartnerWithdrawSurplus { get; set; }
    public byte IsProtocolWithdrawSurplus { get; set; }
    public byte MigrationProgress { get; set; }
    public byte IsWithdrawLeftover { get; set; }
    public byte IsCreatorWithdrawSurplus { get; set; }
    public byte[] _Padding0 { get; set; } = new byte[1];
    public PoolMetrics Metrics { get; set; }
    public ulong FinishCurveTimestamp { get; set; }
    public ulong CreatorBaseFee { get; set; }
    public ulong CreatorQuoteFee { get; set; }
    public ulong[] _Padding1 { get; set; } = new ulong[7];

    public static DBCPool Deserialize(ReadOnlySpan<byte> _data, int initialOffset=8)
    {
        var offset = initialOffset;
        var result = new DBCPool();

        // Deserialize VolatilityTracker
        offset += VolatilityTracker.Deserialize(_data, offset, out var volatilityTracker);
        result.VolatilityTracker = volatilityTracker;

        // Deserialize Config (pubkey, 32 bytes)
        result.Config = _data.GetPubKey(offset);
        offset += 32;

        // Deserialize Creator (pubkey, 32 bytes)
        result.Creator = _data.GetPubKey(offset);
        offset += 32;

        // Deserialize BaseMint (pubkey, 32 bytes)
        result.BaseMint = _data.GetPubKey(offset);
        offset += 32;

        // Deserialize BaseVault (pubkey, 32 bytes)
        result.BaseVault = _data.GetPubKey(offset);
        offset += 32;

        // Deserialize QuoteVault (pubkey, 32 bytes)
        result.QuoteVault = _data.GetPubKey(offset);
        offset += 32;

        // Deserialize BaseReserve (u64, 8 bytes)
        result.BaseReserve = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize QuoteReserve (u64, 8 bytes)
        result.QuoteReserve = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize ProtocolBaseFee (u64, 8 bytes)
        result.ProtocolBaseFee = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize ProtocolQuoteFee (u64, 8 bytes)
        result.ProtocolQuoteFee = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize PartnerBaseFee (u64, 8 bytes)
        result.PartnerBaseFee = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize PartnerQuoteFee (u64, 8 bytes)
        result.PartnerQuoteFee = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize SqrtPrice (u128, 16 bytes)
        offset += Utils.ReadUInt128(_data, offset, out var sqrtPrice);
        result.SqrtPrice = sqrtPrice;

        // Deserialize ActivationPoint (u64, 8 bytes)
        result.ActivationPoint = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize PoolType (u8, 1 byte)
        result.PoolType = _data[offset];
        offset += 1;

        // Deserialize IsMigrated (u8, 1 byte)
        result.IsMigrated = _data[offset];
        offset += 1;

        // Deserialize IsPartnerWithdrawSurplus (u8, 1 byte)
        result.IsPartnerWithdrawSurplus = _data[offset];
        offset += 1;

        // Deserialize IsProtocolWithdrawSurplus (u8, 1 byte)
        result.IsProtocolWithdrawSurplus = _data[offset];
        offset += 1;

        // Deserialize MigrationProgress (u8, 1 byte)
        result.MigrationProgress = _data[offset];
        offset += 1;

        // Deserialize IsWithdrawLeftover (u8, 1 byte)
        result.IsWithdrawLeftover = _data[offset];
        offset += 1;

        // Deserialize IsCreatorWithdrawSurplus (u8, 1 byte)
        result.IsCreatorWithdrawSurplus = _data[offset];
        offset += 1;

        // Deserialize _Padding0 ([u8; 1], 1 byte)
        result._Padding0 = new byte[1];
        _data.Slice(offset, 1).CopyTo(result._Padding0);
        offset += 1;

        // Deserialize Metrics
        offset += PoolMetrics.Deserialize(_data, offset, out var metrics);
        result.Metrics = metrics;

        // Deserialize FinishCurveTimestamp (u64, 8 bytes)
        result.FinishCurveTimestamp = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize CreatorBaseFee (u64, 8 bytes)
        result.CreatorBaseFee = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize CreatorQuoteFee (u64, 8 bytes)
        result.CreatorQuoteFee = BitConverter.ToUInt64(_data.Slice(offset, 8));
        offset += 8;

        // Deserialize _Padding1 ([u64; 7], 56 bytes)
        result._Padding1 = new ulong[7];
        for (int i = 0; i < 7; i++)
        {
            result._Padding1[i] = BitConverter.ToUInt64(_data.Slice(offset, 8));
            offset += 8;
        }

        return result; // Total: 376 bytes
    }
}