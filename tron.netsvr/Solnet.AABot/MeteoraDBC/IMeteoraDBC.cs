namespace Solnet.AABot.MeteoraDBC;

public interface IMeteoraDBC
{
    Task<(bool Successful, string Signature, string ErrorMsg)> SwapInAsync(string mintAddress, decimal sol,
        decimal slippage, decimal fee = 0, decimal mev = 0, string[] accountAddresses = null, decimal tokenPrice = 0,bool isRise = false,string blockHash="");

    Task<(bool Successful, string Signature, string ErrorMsg)> SwapOutAsync(string mintAddress, decimal token_Amount,
        decimal slippage, decimal fee = 0,
        decimal mev = 0, string[] accountAddresses = null, decimal tokenPrice = 0,string blockHash="");

    Task<(bool Successful, string Signature, string ErrorMsg)> SwapOutByPercentAsync(string mintAddress,
        decimal token_Percent, decimal slippage, decimal fee = 0,
        decimal mev = 0, string[] accountAddresses = null, decimal tokenPrice = 0,string blockHash="");
    Task<(bool Successful, string Signature,string ErrorMsg)> BuySell(string mintAddress, decimal sol, decimal slippage, decimal fee = 0, decimal mev = 0,string blockHash="");
}