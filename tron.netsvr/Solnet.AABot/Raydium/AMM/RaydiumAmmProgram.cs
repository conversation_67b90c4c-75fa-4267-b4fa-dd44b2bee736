using Solnet.AABot.Raydium.AMM.Models;
using Solnet.Extensions;
using Solnet.Programs;
using Solnet.Programs.Utilities;
using Solnet.Rpc.Models;
using Solnet.Wallet;

namespace Solnet.AABot.Raydium.AMM;

public class RaydiumAmmProgram
{
    public static PublicKey RAYDIUM_V4_PROGRAM_ID = new PublicKey("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8");
    public static PublicKey RAYDIUM_V4_PROGRAM_ID_Dev = new PublicKey("HWy1jotHpo6UqeQxx49dpYYdQB8wj9Qk9MdxwjLvDHB8");
    public static PublicKey WSOL = new PublicKey("So11111111111111111111111111111111111111112");
    public static PublicKey AmmAuthority = new PublicKey("5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1");
    public static PublicKey AmmAuthority_Dev = new PublicKey("DbQqP6ehDYmeYjcBaMRuA8tAJY1EjDUz9DpwSLjaQqfC");
    public static PublicKey LookupTableAddress = new PublicKey("2G1Thj3LFPPrZPr3LLFBnaWqDYLeP4EiZiQGmcoWEBjN");
    public static PublicKey DevLookupTableAddress = new PublicKey("7LwMoG8ARcTjDA6ncfpgJ5tuEx7weS6cqMxdnY4m47U8");
    public static TransactionInstruction SetCUlimit(ulong units)
    {
        List<AccountMeta> keys = new List<AccountMeta>();
        byte[] data = new byte[9];
        data.WriteU8(2, 0);
        data.WriteU64(units, 1);
        return new TransactionInstruction
        {
            ProgramId = ComputeBudgetProgram.ProgramIdKey,
            Keys = keys,
            Data = data
        };
    }


    public static TransactionInstruction SwapIn(SwapBaseInAccounts accounts, ulong amountIn, ulong slippagePercentage,
        bool isDev = false,ulong tokenPrice=0,bool isRise=false)
    {
        List<AccountMeta> keys = new()
        {
            //system
            AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false),
            //amm
            AccountMeta.Writable(accounts.Amm, false),
            AccountMeta.ReadOnly(accounts.AmmAuthority, false),
            AccountMeta.Writable(accounts.AmmOpenOrders, false),
            AccountMeta.Writable(accounts.AmmTargetOrders, false),
            AccountMeta.Writable(accounts.AmmCoinVaullt, false),
            AccountMeta.Writable(accounts.AmmPcVaullt, false),
            //serum
            AccountMeta.ReadOnly(accounts.SerumProgram, false),
            AccountMeta.Writable(accounts.SerumMarket, false),
            AccountMeta.Writable(accounts.SerumBids, false),
            AccountMeta.Writable(accounts.SerumAsks, false),
            AccountMeta.Writable(accounts.SerumEventQueue, false),
            AccountMeta.Writable(accounts.SerumCoinVaultAccount, false),
            AccountMeta.Writable(accounts.SerumPcVaultAccount, false),
            AccountMeta.ReadOnly(accounts.SerumVaultSigner, false),
            //user
            AccountMeta.Writable(accounts.UerSourceTokenAccount, false),
            AccountMeta.Writable(accounts.UerDestinationTokenAccount, false),
            AccountMeta.Writable(accounts.UserSourceOwner, true),

            AccountMeta.ReadOnly(isDev ? RAYDIUM_V4_PROGRAM_ID_Dev : RAYDIUM_V4_PROGRAM_ID, false),
            AccountMeta.ReadOnly(SystemProgram.ProgramIdKey, false),
            AccountMeta.ReadOnly(accounts.Mint, false),
            AccountMeta.ReadOnly(AssociatedTokenAccountProgram.ProgramIdKey, false),
        };

        byte[] instructionBuffer = new byte[] { 243, 19, 123, 6, 9, 222, 202, 31 };

        byte[] _data = new byte[1200];
        int offset = 0;
        _data.WriteSpan(instructionBuffer, 0);
        offset += 8;
        _data.WriteU64(amountIn, offset);
        offset += 8;
        _data.WriteU64(slippagePercentage, offset);
        offset += 8;
        _data.WriteU64(tokenPrice, offset);
        offset += 8;
        _data.WriteU8((byte)(isRise?1:0), offset);
        offset += 1;
        byte[] resultData = new byte[offset];
        Array.Copy(_data, resultData, offset);
        return new TransactionInstruction
        {
            Keys = keys, 
            ProgramId = isDev ? AABotProgram.ID_DevNet : AABotProgram.ID_MainNet,
            Data = resultData
        };
    }

    public static TransactionInstruction SwapOut(SwapBaseInAccounts accounts, ulong amountOut, ulong amountPercentage,
        ulong slippagePercentage, bool isDev = false,ulong tokenPrice=0)
    {
        List<AccountMeta> keys = new()
        {
            //system
            AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false),
            //amm
            AccountMeta.Writable(accounts.Amm, false),
            AccountMeta.ReadOnly(accounts.AmmAuthority, false),
            AccountMeta.Writable(accounts.AmmOpenOrders, false),
            AccountMeta.Writable(accounts.AmmTargetOrders, false),
            AccountMeta.Writable(accounts.AmmCoinVaullt, false),
            AccountMeta.Writable(accounts.AmmPcVaullt, false),
            //serum
            AccountMeta.ReadOnly(accounts.SerumProgram, false),
            AccountMeta.Writable(accounts.SerumMarket, false),
            AccountMeta.Writable(accounts.SerumBids, false),
            AccountMeta.Writable(accounts.SerumAsks, false),
            AccountMeta.Writable(accounts.SerumEventQueue, false),
            AccountMeta.Writable(accounts.SerumCoinVaultAccount, false),
            AccountMeta.Writable(accounts.SerumPcVaultAccount, false),
            AccountMeta.ReadOnly(accounts.SerumVaultSigner, false),
            //user
            AccountMeta.Writable(accounts.UerSourceTokenAccount, false),
            AccountMeta.Writable(accounts.UerDestinationTokenAccount, false),
            AccountMeta.Writable(accounts.UserSourceOwner, true),

            AccountMeta.ReadOnly(isDev ? RAYDIUM_V4_PROGRAM_ID_Dev : RAYDIUM_V4_PROGRAM_ID, false),
            AccountMeta.ReadOnly(SystemProgram.ProgramIdKey, false),
            AccountMeta.ReadOnly(accounts.Mint, false),
            AccountMeta.ReadOnly(WSOL, false),
            AccountMeta.ReadOnly(AssociatedTokenAccountProgram.ProgramIdKey, false),
        };

        byte[] instructionBuffer = new byte[] { 197, 64, 8, 156, 174, 195, 176, 253 };

        byte[] _data = new byte[1200];
        int offset = 0;
        _data.WriteSpan(instructionBuffer, 0);
        offset += 8;
        _data.WriteU64(amountOut, offset);
        offset += 8;
        _data.WriteU64(amountPercentage, offset);
        offset += 8;
        _data.WriteU64(slippagePercentage, offset);
        offset += 8;
        _data.WriteU64(tokenPrice, offset);
        offset += 8;
        byte[] resultData = new byte[offset];
        Array.Copy(_data, resultData, offset);
        return new TransactionInstruction
        {
            Keys = keys, 
            ProgramId = isDev ? AABotProgram.ID_DevNet : AABotProgram.ID_MainNet,
            Data = resultData
        };
    }
}