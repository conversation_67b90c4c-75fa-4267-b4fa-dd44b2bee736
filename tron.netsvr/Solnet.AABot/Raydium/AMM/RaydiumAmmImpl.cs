using System.Text.Json;
using Microsoft.Extensions.Logging;
using Solnet.AABot.Raydium.AMM.Models;
using Solnet.AABot.Raydium.Pool;
using Solnet.Programs;
using Solnet.Programs.Models;
using Solnet.Programs.Utilities;
using Solnet.Rpc;
using Solnet.Rpc.Models;
using Solnet.Rpc.Types;
using Solnet.Wallet;

namespace Solnet.AABot.Raydium.AMM;

public class RaydiumAmmImpl :AABotProgram, IRaydiumAMM, IAABot
{
    /*public IRpcClient RpcClient { get; set; }
    private Account Trader { get; set; }
    private bool IsDev = false;
    protected readonly ILogger _logger;*/

    public RaydiumAmmImpl(IRpcClient rpc, Account _trader, ILogger logger):base(rpc, null,_trader, logger)
    {
        RpcClient = rpc;
        Trader = _trader;
        IsDev = rpc.NodeAddress.Host.Contains("devnet");
        _logger = logger ?? new LoggerFactory().CreateLogger("RaydiumAmmImpl");
    }
    public RaydiumAmmImpl(IRpcClient rpc,IRpcClient zeroSlotRpcClient, Account _trader, ILogger logger):base(rpc, zeroSlotRpcClient,_trader, logger)
    {
        RpcClient = rpc;
        Trader = _trader;
        IsDev = rpc.NodeAddress.Host.Contains("devnet");
        _logger = logger ?? new LoggerFactory().CreateLogger("RaydiumAmmImpl");
    }
    #region 私有方法

    private async Task<AccountResultWrapper<AmmInfo>> GetAmmInfoAsync(string poolAddress,
        Commitment commitment = Commitment.Finalized)
    {
        if (string.IsNullOrEmpty(poolAddress)) return null;
        var res = await RpcClient.GetAccountInfoAsync(poolAddress, commitment);
        if (!res.WasSuccessful || res.Result == null || res.Result.Value == null)
        {
            _logger.LogError($"GetAmmInfoAsync failed: {res}");
            return null;
        }

        var resultingAccount = AmmInfo.Deserialize(Convert.FromBase64String(res.Result.Value.Data[0]));
        return new AccountResultWrapper<AmmInfo>(res, resultingAccount);
    }

    private async Task<AccountResultWrapper<MakeInfo>> GetMakeInfoAsync(string marketId,
        Commitment commitment = Commitment.Finalized)
    {
        if (string.IsNullOrEmpty(marketId)) return null;
        var res = await RpcClient.GetAccountInfoAsync(marketId, commitment);
        if (!res.WasSuccessful || res.Result == null || res.Result.Value == null)
        {
            _logger.LogError($"GetMakeInfoAsync failed: {res}");
            return null;
        }

        var resultingAccount = MakeInfo.Deserialize(Convert.FromBase64String(res.Result.Value.Data[0]));
        return new AccountResultWrapper<MakeInfo>(res, resultingAccount);
    }

    private async Task<Dictionary<string, AmmInfo>> GetListAmminfo(string mintAddress)
    {
        var res=await RpcClient.GetProgramAccountsAsync(IsDev?RaydiumAmmProgram.RAYDIUM_V4_PROGRAM_ID_Dev:RaydiumAmmProgram.RAYDIUM_V4_PROGRAM_ID,
            Commitment.Confirmed,dataSize:752,new List<MemCmp>()
            {
                new MemCmp()
                {
                    Bytes = mintAddress,
                    Offset =400
                }
            });
        while (!res.WasSuccessful || res.Result == null || res.Result.Count <= 0)
        {
            Console.WriteLine($"GetListAmminfo failed: {res.RawRpcResponse}");
            _logger.LogError($"GetListAmminfo failed: {res.RawRpcResponse}");
            res=await RpcClient.GetProgramAccountsAsync(IsDev?RaydiumAmmProgram.RAYDIUM_V4_PROGRAM_ID_Dev:RaydiumAmmProgram.RAYDIUM_V4_PROGRAM_ID,
                Commitment.Confirmed,dataSize:752,new List<MemCmp>()
                {
                    new MemCmp()
                    {
                        Bytes = mintAddress,
                        Offset =400
                    }
                });
            await Task.Delay(200);
        }

        var dict = new Dictionary<string, AmmInfo>();
        foreach (var accountKeyPair in res.Result)
        {
            dict[accountKeyPair.PublicKey] = AmmInfo.Deserialize(Convert.FromBase64String(accountKeyPair.Account.Data[0]));
        }
        /*List<AmmInfo> resultingAccounts = new List<AmmInfo>(res.Result.Count);
        resultingAccounts.AddRange(res.Result.Select(result =>
            AmmInfo.Deserialize(Convert.FromBase64String(result.Account.Data[0]))));*/
        return dict;
    }
    
    #endregion

    /// <summary>
    ///  添加计算预算指令的公共方法
    /// </summary>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="cu"></param>
    /// <returns></returns>
    private (TransactionInstruction computeBudget, TransactionInstruction computePrice)
        GetComputeInstructions(decimal fee = 0, decimal mev = 0, decimal cu = 1_000_000m)
    {
        //var cu = 1_000_000m;
        fee = fee == 0 ? 0.000005m : fee;
        var microLamports = (ulong)((fee - 0.000005M) * cu);
        var jito = (ulong)(mev * 1e9m); // 未使用，可以保留以备将来扩展

        var computebudget = (ulong)cu;
        var computeprice = (ulong)microLamports * 1000;
        TransactionInstruction computeBudget = RaydiumAmmProgram.SetCUlimit(computebudget);
        TransactionInstruction computePrice = ComputeBudgetProgram.SetComputeUnitPrice(computeprice);

        return (computeBudget, computePrice);
    }

    /// <summary>
    /// 模拟 提交
    /// </summary>
    /// <param name="transactions"></param>
    /// <returns></returns>
    private async Task<(decimal, string?)> GetSimulationUnits(List<TransactionInstruction> transactions)
    {
        var blockhash = await RpcClient.GetLatestBlockHashAsync();
        VersionedTransaction tx = new VersionedTransaction
        {
            FeePayer = Trader,
            RecentBlockHash = blockhash?.Result?.Value?.Blockhash,
            Instructions = new(),
            AddressTableLookups = new()
        };

        tx.Instructions.AddRange(transactions);
        tx.Sign(new List<Solnet.Wallet.Account> { Trader });

        var result = await RpcClient.SimulateTransactionAsync(tx.Serialize(), commitment: Commitment.Confirmed);
        Console.WriteLine("GetSimulationUnits=>" + result.RawRpcResponse);
        if (result.WasSuccessful)
        {
            JsonDocument doc = JsonDocument.Parse(result.RawRpcResponse);
            JsonElement root = doc.RootElement;

            JsonElement resultElement = default;
            JsonElement valueElement = default;
            JsonElement unitsConsumedElement = default;
            if (result.Result.Value.Error != null)
            {
                _logger?.LogDebug("GetSimulationUnits=>" + result.RawRpcResponse);
                return (-1, result.Result.Value.Error.Type.ToString());
            }

            // 使用 TryGetProperty 减少嵌套
            bool success = root.TryGetProperty("result", out resultElement) &&
                           resultElement.TryGetProperty("value", out valueElement) &&
                           valueElement.TryGetProperty("unitsConsumed", out unitsConsumedElement) &&
                           unitsConsumedElement.ValueKind == JsonValueKind.Number;
            if (success) return (unitsConsumedElement.GetDecimal(), null);
            else return (0m, null);

            ulong unitsConsumed = doc.RootElement
                .GetProperty("result")
                .GetProperty("value")
                .GetProperty("unitsConsumed").GetUInt64();
        }

        return (0m, null);
    }

    // 构建 SwapBaseInAccounts 的公共方法
    // 修改后的 BuildSwapBaseInAccountsAsync，接收 poolAddress 和 ammInfo 参数
    private async Task<SwapBaseInAccounts> BuildSwapBaseInAccountsAsync(string mintAddress,
        string poolAddress = null,
        AccountResultWrapper<AmmInfo> ammInfo = null,
        string[] accountAddresses = null)
    {
        var mint = new PublicKey(mintAddress);
        SwapBaseInAccounts accounts = new SwapBaseInAccounts { UserSourceOwner = Trader, Mint = mint };

        if (accountAddresses != null)
        {
            accounts.Amm = new PublicKey(accountAddresses[0]);
            accounts.AmmAuthority =
                IsDev
                    ? RaydiumAmmProgram.AmmAuthority_Dev
                    : RaydiumAmmProgram.AmmAuthority; //new PublicKey(accountAddresses[1]);
            accounts.AmmOpenOrders = new PublicKey(accountAddresses[2]);
            accounts.AmmTargetOrders = new PublicKey(accountAddresses[3]);
            accounts.AmmCoinVaullt = new PublicKey(accountAddresses[4]);
            accounts.AmmPcVaullt = new PublicKey(accountAddresses[5]);
            accounts.SerumProgram = new PublicKey(accountAddresses[6]);
            accounts.SerumMarket = new PublicKey(accountAddresses[7]);
            accounts.SerumBids = new PublicKey(accountAddresses[8]);
            accounts.SerumAsks = new PublicKey(accountAddresses[9]);
            accounts.SerumEventQueue = new PublicKey(accountAddresses[10]);
            accounts.SerumCoinVaultAccount = new PublicKey(accountAddresses[11]);
            accounts.SerumPcVaultAccount = new PublicKey(accountAddresses[12]);
            var vaultSignerNonce = ulong.Parse(accountAddresses[13]);
            PublicKey.TryCreateProgramAddress([accounts.SerumMarket.KeyBytes, BitConverter.GetBytes(vaultSignerNonce)],
                accounts.SerumProgram, out PublicKey marketAuthority);
            accounts.SerumVaultSigner = marketAuthority;
        }
        else
        {
            if (IsDev)
            {
                var poolDict=await GetListAmminfo(mintAddress);
                if (poolDict.Count > 0)
                {
                    poolAddress = poolDict.First().Key;
                   var  dammInfo = poolDict.First().Value;
                   
                   var makeInfo = await GetMakeInfoAsync(dammInfo.Market);
                   if (makeInfo == null) return null;
                   var makeOwner = makeInfo.ParsedResult.ownAddress;
                   var vaultSignerNonce = makeInfo.ParsedResult.vaultSignerNonce;

                   PublicKey.TryCreateProgramAddress([makeOwner.KeyBytes, BitConverter.GetBytes(vaultSignerNonce)],
                       dammInfo.SerumDex, out PublicKey marketAuthority);
                   
                   accounts.Amm = new PublicKey(poolAddress);
                   accounts.AmmAuthority = RaydiumAmmProgram.AmmAuthority_Dev;
                   accounts.AmmOpenOrders = dammInfo.OpenOrders;
                   accounts.AmmTargetOrders = dammInfo.TargetOrders;
                   accounts.AmmCoinVaullt = dammInfo.BaseVault;
                   accounts.AmmPcVaullt = dammInfo.QuoteVault;
                   accounts.SerumProgram = dammInfo.SerumDex;
                   accounts.SerumMarket = dammInfo.Market;
                   accounts.SerumBids = makeInfo.ParsedResult.bids;
                   accounts.SerumAsks = makeInfo.ParsedResult.asks;
                   accounts.SerumEventQueue = makeInfo.ParsedResult.eventQueue;
                   accounts.SerumCoinVaultAccount = makeInfo.ParsedResult.baseVault;
                   accounts.SerumPcVaultAccount = makeInfo.ParsedResult.quoteVault;
                   accounts.SerumVaultSigner = marketAuthority;
                }
            }
            else
            {

                IPoolAddress poolAddressClient = PoolAddressClientFactory.GetHttpClient();
                poolAddress ??= await poolAddressClient.GetPoolAddressAsync(mint); // 如果未提供 poolAddress，则请求
                if (poolAddress == null) return null;
                ammInfo ??= await GetAmmInfoAsync(poolAddress); // 如果未提供 ammInfo，则请求
                if (ammInfo == null) return null;
                var makeInfo = await GetMakeInfoAsync(ammInfo.ParsedResult.Market);
                if (makeInfo == null) return null;
                var makeOwner = makeInfo.ParsedResult.ownAddress;
                var vaultSignerNonce = makeInfo.ParsedResult.vaultSignerNonce;

                PublicKey.TryCreateProgramAddress([makeOwner.KeyBytes, BitConverter.GetBytes(vaultSignerNonce)],
                    ammInfo.ParsedResult.SerumDex, out PublicKey marketAuthority);

                accounts.Amm = new PublicKey(poolAddress);
                accounts.AmmAuthority = RaydiumAmmProgram.AmmAuthority; 
                accounts.AmmOpenOrders = ammInfo.ParsedResult.OpenOrders;
                accounts.AmmTargetOrders = ammInfo.ParsedResult.TargetOrders;
                accounts.AmmCoinVaullt = ammInfo.ParsedResult.BaseVault;
                accounts.AmmPcVaullt = ammInfo.ParsedResult.QuoteVault;
                accounts.SerumProgram = ammInfo.ParsedResult.SerumDex;
                accounts.SerumMarket = ammInfo.ParsedResult.Market;
                accounts.SerumBids = makeInfo.ParsedResult.bids;
                accounts.SerumAsks = makeInfo.ParsedResult.asks;
                accounts.SerumEventQueue = makeInfo.ParsedResult.eventQueue;
                accounts.SerumCoinVaultAccount = makeInfo.ParsedResult.baseVault;
                accounts.SerumPcVaultAccount = makeInfo.ParsedResult.quoteVault;
                accounts.SerumVaultSigner = marketAuthority;
            }
        }

        return accounts;
    }

    /// <summary>
    /// 解析 地址表 数据
    /// </summary>
    /// <param name="jsonData"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private List<PublicKey> ExtractAddressesFromJson(string jsonData)
    {
        try
        {
            using var document = JsonDocument.Parse(jsonData);
            var addressesElement = document.RootElement
                .GetProperty("parsed")
                .GetProperty("info")
                .GetProperty("addresses");

            var addresses = new List<PublicKey>();
            foreach (var address in addressesElement.EnumerateArray())
            {
                addresses.Add(new PublicKey(address.GetString()));
            }

            return addresses;
        }
        catch (Exception ex)
        {
            throw new Exception($"无法从 JSON 提取 addresses 数组：{ex.Message}");
        }
    }

    /// <summary>
    /// 封装交易构建和发送逻辑
    /// </summary>
    /// <param name="instructions"></param>
    /// <returns></returns>
    private async Task<(bool Successful, string Signature, string ErrorMsg)> BuildAndSendTableAddressTransaction(
        List<TransactionInstruction> instructions)
    {
        var lookupTableAddress = IsDev ? RaydiumAmmProgram.DevLookupTableAddress : RaydiumAmmProgram.LookupTableAddress;

        var programId = instructions.Select(s => new PublicKey(s.ProgramId)).ToList().Distinct().ToList();
        var writableSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && s.IsSigner && s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var readonlySigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !writableSigners.Contains(s.PublicKey) && s.IsSigner && !s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var writableNonSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !s.IsSigner && s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var readonlyNonSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !s.IsSigner && !s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();

        //获取地址表信息
        //var accountInfo =
        //    await RpcClient.GetAccountInfoAsync(lookupTableAddress, Commitment.Finalized, BinaryEncoding.JsonParsed);
        //if (accountInfo.Result.Value == null)
        //{
        //    await Task.Delay(100);
        //    accountInfo =
        //        await RpcClient.GetAccountInfoAsync(lookupTableAddress, Commitment.Finalized,
        //            BinaryEncoding.JsonParsed);
        //    Console.WriteLine("获取地址表=>" + accountInfo.RawRpcResponse);
        //    if (accountInfo.Result.Value == null)
        //    {
        //        Console.WriteLine($"获取地址表失败  退出交易");
        //        return (false, "", "获取地址表失败");
        //    }
        //}

        //var tableAddresses = ExtractAddressesFromJson(accountInfo.Result.Value.Data[0]);

        var tableAddresses = AABotProgram.TableAddressRaydium(IsDev);
        var write_data = new List<byte>();
        var read_data = new List<byte>();
        for (int i = 0; i < instructions.Count; i++)
        {
            for (int j = 0; j < instructions[i].Keys.Count; j++)
            {
                var key = instructions[i].Keys[j];
                if (programId.Contains(new PublicKey(key.PublicKey))) continue;
                var index = tableAddresses.IndexOf(new PublicKey(key.PublicKey));
                if (index > -1)
                {
                    if (key.IsWritable)
                    {
                        if (!write_data.Contains(Convert.ToByte(index)))
                            write_data.Add(Convert.ToByte(index));
                    }
                    else
                    {
                        if (!read_data.Contains(Convert.ToByte(index)))
                            read_data.Add(Convert.ToByte(index));
                    }
                }
            }
        }

        var lookupTableAccount = new List<Message.MessageAddressTableLookup>
        {
            new Message.MessageAddressTableLookup()
            {
                AccountKey = lookupTableAddress,
                WritableIndexes = write_data.ToArray(),
                ReadonlyIndexes = read_data.ToArray()
            }
        };


        var AccountKeys = new List<PublicKey>();
        AccountKeys.AddRange(writableSigners.Select(s => new PublicKey(s)).ToList());
        AccountKeys.AddRange(readonlySigners.Select(s => new PublicKey(s)).ToList());
        var AccountKeysWritableNonSigners = writableNonSigners.Where(s => !tableAddresses.Contains(new PublicKey(s)))
            .Select(s => new PublicKey(s)).ToList();
        if (AccountKeysWritableNonSigners != null && AccountKeysWritableNonSigners.Any())
            AccountKeys.AddRange(AccountKeysWritableNonSigners);
        AccountKeys.AddRange(programId);
        var AccountKeysReadonlyNonSigners = readonlyNonSigners.Where(s => !tableAddresses.Contains(new PublicKey(s)))
            .Select(s => new PublicKey(s)).ToList();
        AccountKeys.AddRange(AccountKeysReadonlyNonSigners);
        AccountKeys = AccountKeys.Distinct().ToList();
        var StaticAccountKeys = new List<PublicKey>();
        StaticAccountKeys.AddRange(AccountKeys);
        StaticAccountKeys.AddRange(writableNonSigners.Select(s => new PublicKey(s)).ToList());
        StaticAccountKeys.AddRange(readonlyNonSigners.Select(s => new PublicKey(s)).ToList());
        StaticAccountKeys = StaticAccountKeys.Distinct().ToList();

        var compileInstructions = new List<CompiledInstruction>();

        for (int i = 0; i < instructions.Count; i++)
        {
            var item = instructions[i];
            var KeyIndices = item.Keys.Select(s =>
                {
                    return Convert.ToByte(StaticAccountKeys.IndexOf(new PublicKey(s.PublicKey)));
                }
            ).ToArray();
            var compileIns = new CompiledInstruction()
            {
                ProgramIdIndex = Convert.ToByte(AccountKeys.IndexOf(new PublicKey(item.ProgramId))),
                Data = item.Data,
                DataLength = new byte[] { Convert.ToByte(item.Data.Length) },
                KeyIndices = KeyIndices,
                KeyIndicesCount = new byte[] { Convert.ToByte(KeyIndices.Length) },
            };
            compileInstructions.Add(compileIns);
        }

        var res = await RpcClient.GetLatestBlockHashAsync();
        LatestBlockHash latestBlockHash = res?.Result?.Value;
        if (latestBlockHash == null)
        {
            await Task.Delay(100);
            res = await RpcClient.GetLatestBlockHashAsync();
            latestBlockHash = res?.Result?.Value;
        }

        var message = new Message.VersionedMessage()
        {
            Header = new MessageHeader()
            {
                RequiredSignatures = Convert.ToByte(writableSigners.Count + readonlySigners.Count),
                ReadOnlySignedAccounts = Convert.ToByte(readonlySigners.Count),
                ReadOnlyUnsignedAccounts = Convert.ToByte(AccountKeys.Count - AccountKeysWritableNonSigners.Count -
                                                          readonlySigners.Count - writableSigners.Count),
            },
            AccountKeys = AccountKeys,
            AddressTableLookups = lookupTableAccount,
            Instructions = compileInstructions,
            RecentBlockhash = latestBlockHash?.Blockhash,
        };

        var sign = new List<Solnet.Wallet.Account> { Trader };
        //sign.AddRange(player);

        var CompileTx = VersionedTransaction.Populate(message);
        CompileTx.Sign(sign);

        var response =
            await RpcClient.SendTransactionAsync(CompileTx.VersionedSerialize(), commitment: Commitment.Confirmed);
        _logger?.LogDebug(response.RawRpcResponse);
        Console.WriteLine(response.RawRpcResponse);
        return (response.WasSuccessful, response.Result, response.Reason);
    }

    // 发送交易的公共方法
    private async Task<(bool Successful, string Signature, string ErrorMsg)> SendTransactionAsync(
        List<TransactionInstruction> transactions)
    {
        var blockhash = await RpcClient.GetLatestBlockHashAsync();
        var builder = new VersionedTransaction
        {
            FeePayer = Trader.PublicKey,
            RecentBlockHash = blockhash.Result.Value.Blockhash,
            Instructions = transactions,
            AddressTableLookups = new()
        };

        builder.Sign(new List<Account> { Trader });
        var transaction = await RpcClient.SendTransactionAsync(builder.Serialize(), commitment: Commitment.Confirmed);
        _logger?.LogDebug(transaction.RawRpcResponse);
        return (transaction.WasSuccessful, transaction.Result, transaction.Reason);
    }

    // SwapIn 方法：将 SOL 兑换为目标代币
    public async Task<(bool Successful, string Signature, string ErrorMsg)> SwapInAsync(string mintAddress, decimal sol,
        decimal slippage, decimal fee = 0,
        decimal mev = 0, string[] accountAddresses = null, decimal tokenPrice = 0, bool isRise = false,string blockHash="")
    {
        var transactions = await GetBuy(mintAddress, sol, slippage, fee, mev, accountAddresses, tokenPrice, isRise,
            blockHash);
        var lookupTableAddress = IsDev ? RaydiumAmmProgram.DevLookupTableAddress : RaydiumAmmProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions,null,lookupTableAddress,mev,blockHash);
    }

    // SwapOut 方法：将代币兑换为 SOL
    public async Task<(bool Successful, string Signature, string ErrorMsg)> SwapOutAsync(string mintAddress,
        decimal tokenAmount, decimal slippage, decimal fee = 0,
        decimal mev = 0, string[] accountAddresses = null, decimal tokenPrice = 0,string blockHash="")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);

        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);
        ulong tokenAmountLamports;
        string poolAddress = null;
        AccountResultWrapper<AmmInfo> ammInfo = null;

        if (accountAddresses != null)
        {
            tokenAmountLamports =
                (ulong)(tokenAmount * (decimal)Math.Pow(10, int.Parse(accountAddresses[14].ToString())));
        }
        else
        {
            var poolAddressClient = PoolAddressClientFactory.GetHttpClient();
            poolAddress = await poolAddressClient.GetPoolAddressAsync(new PublicKey(mintAddress)); // 只请求一次
            ammInfo = await GetAmmInfoAsync(poolAddress); // 只请求一次
            var decimals = ammInfo.ParsedResult.BaseMint == new PublicKey(mintAddress)
                ? ammInfo.ParsedResult.CoinDecimals
                : ammInfo.ParsedResult.PcDecimals;
            tokenAmountLamports = (ulong)(tokenAmount * (decimal)Math.Pow(10, decimals));
        }

        // 将 poolAddress 和 ammInfo 传递给 BuildSwapBaseInAccountsAsync，避免重复请求
        var swapBaseInAccounts =
            await BuildSwapBaseInAccountsAsync(mintAddress, poolAddress, ammInfo, accountAddresses);
        if (swapBaseInAccounts == null) return (false, string.Empty, "Accounts is null");
        var quoteTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, RaydiumAmmProgram.WSOL);
        swapBaseInAccounts.UerSourceTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));
        swapBaseInAccounts.UerDestinationTokenAccount = quoteTokenAccount;

        transactions.Add(RaydiumAmmProgram.SwapOut(swapBaseInAccounts, tokenAmountLamports, 0ul, slippagePercentage,
            IsDev, tokenPriceUl));
        transactions.Add(TokenProgram.CloseAccount(quoteTokenAccount, Trader.PublicKey, Trader.PublicKey,
            TokenProgram.ProgramIdKey));
        /*var (cu,err) = await GetSimulationUnits(transactions);
        if (err != null)
        {
            return (false, string.Empty, err);
        }
        if (cu > 0)
        {
            cu = Math.Ceiling(cu * 1.1m); // 增加 10% 缓冲
            (computeBudget, computePrice) = GetComputeInstructions(fee, mev, cu);
        }*/

        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);
        var lookupTableAddress = IsDev ? RaydiumAmmProgram.DevLookupTableAddress : RaydiumAmmProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions,null,lookupTableAddress,mev,blockHash);
    }

    // SwapOutByPercent 方法：按百分比将代币兑换为 SOL
    public async Task<(bool Successful, string Signature, string ErrorMsg)> SwapOutByPercentAsync(string mintAddress,
        decimal tokenPercent, decimal slippage,
        decimal fee = 0, decimal mev = 0, string[] accountAddresses = null, decimal tokenPrice = 0,string blockHash="")
    {
        var transactions =
            await GetSellPercentage(mintAddress, tokenPercent, slippage, fee, mev, null, tokenPrice, blockHash);
        var lookupTableAddress = IsDev ? RaydiumAmmProgram.DevLookupTableAddress : RaydiumAmmProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions,null,lookupTableAddress,mev,blockHash);
    }
    public async Task<(bool Successful, string Signature, string ErrorMsg)> BuySell(string mintAddress, decimal sol, decimal slippage, decimal fee = 0, decimal mev = 0,string blockHash="")
    {
        
        var transactions=await GetBuySell(mintAddress, sol, slippage, fee, mev, null, blockHash);
        //Console.WriteLine(JsonSerializer.Serialize(transactions));
        var lookupTableAddress = IsDev ? RaydiumAmmProgram.DevLookupTableAddress : RaydiumAmmProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions,null,lookupTableAddress,mev,blockHash);
    }

    public async Task<List<TransactionInstruction>> GetBuy(string mintAddress, decimal sol, decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0, bool isRise = false, string blockHash = "")
    {
        
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        transactions.Add(computeBudget);
        transactions.Add(computePrice);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);
        var swapBaseInAccounts = await BuildSwapBaseInAccountsAsync(mintAddress, accountAddresses: accountAddresses);
        if (swapBaseInAccounts == null) return null;
        // 创建并初始化源账户
        var seed = new Account().PublicKey.Key.Substring(0, 32);
        PublicKey.TryCreateWithSeed(Trader.PublicKey, seed, TokenProgram.ProgramIdKey, out var source);
        transactions.Add(SystemProgram.CreateAccountWithSeed(
            Trader.PublicKey, source, Trader.PublicKey, seed, (ulong)(2039280 + solAmount), 165,
            TokenProgram.ProgramIdKey));
        transactions.Add(TokenProgram.InitializeAccount(source, RaydiumAmmProgram.WSOL, Trader.PublicKey));

        swapBaseInAccounts.UerSourceTokenAccount = source;
        swapBaseInAccounts.UerDestinationTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));

        // 添加兑换和关闭账户指令
        transactions.Add(RaydiumAmmProgram.SwapIn(swapBaseInAccounts, solAmount, slippagePercentage, IsDev, tokenPriceUl, isRise));
        transactions.Add(TokenProgram.CloseAccount(source, Trader.PublicKey, Trader.PublicKey,
            TokenProgram.ProgramIdKey));
        return transactions;
    }

    public async Task<List<TransactionInstruction>> GetSellPercentage(string mintAddress, decimal percentage, decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, decimal tokenPrice = 0, string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);

        var tokenPercentLamports = (ulong)percentage;
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);
        var swapBaseInAccounts = await BuildSwapBaseInAccountsAsync(mintAddress, accountAddresses: accountAddresses);
        if (swapBaseInAccounts == null) return null;
        var quoteTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, RaydiumAmmProgram.WSOL);
        swapBaseInAccounts.UerSourceTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));
        swapBaseInAccounts.UerDestinationTokenAccount = quoteTokenAccount;

        // 添加兑换和关闭账户指令
        transactions.Add(RaydiumAmmProgram.SwapOut(swapBaseInAccounts, 0ul, tokenPercentLamports, slippagePercentage,
            IsDev, tokenPriceUl));
        transactions.Add(TokenProgram.CloseAccount(quoteTokenAccount, Trader.PublicKey, Trader.PublicKey,
            TokenProgram.ProgramIdKey));

        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);
        return transactions;
    }

    public async Task<List<TransactionInstruction>> GetBuySell(string mintAddress, decimal sol, decimal slippage, decimal fee = 0, decimal mev = 0,
        string[] accountAddresses = null, string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        transactions.Add(computeBudget);
        transactions.Add(computePrice);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var swapBaseInAccounts = await BuildSwapBaseInAccountsAsync(mintAddress, accountAddresses: null);
        if (swapBaseInAccounts == null) return null;
        // 创建并初始化源账户
        var seed = new Account().PublicKey.Key.Substring(0, 32);
        PublicKey.TryCreateWithSeed(Trader.PublicKey, seed, TokenProgram.ProgramIdKey, out var source);
        transactions.Add(SystemProgram.CreateAccountWithSeed(
            Trader.PublicKey, source, Trader.PublicKey, seed, (ulong)(2039280 + solAmount), 165,
            TokenProgram.ProgramIdKey));
        transactions.Add(TokenProgram.InitializeAccount(source, RaydiumAmmProgram.WSOL, Trader.PublicKey));

        swapBaseInAccounts.UerSourceTokenAccount = source;
        swapBaseInAccounts.UerDestinationTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));
        // 添加兑换和关闭账户指令
        transactions.Add(RaydiumAmmProgram.SwapIn(swapBaseInAccounts, solAmount, slippagePercentage, IsDev));
        transactions.Add(TokenProgram.CloseAccount(source, Trader.PublicKey, Trader.PublicKey, TokenProgram.ProgramIdKey));
        //sell
        var quoteTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, RaydiumAmmProgram.WSOL);
        swapBaseInAccounts.UerSourceTokenAccount =
            AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, new PublicKey(mintAddress));
        swapBaseInAccounts.UerDestinationTokenAccount = quoteTokenAccount;

        // 添加兑换和关闭账户指令
        transactions.Add(RaydiumAmmProgram.SwapOut(swapBaseInAccounts, 0ul, 100, slippagePercentage, IsDev));
        transactions.Add(TokenProgram.CloseAccount(quoteTokenAccount, Trader.PublicKey, Trader.PublicKey,
            TokenProgram.ProgramIdKey));
        return transactions;
    }
}