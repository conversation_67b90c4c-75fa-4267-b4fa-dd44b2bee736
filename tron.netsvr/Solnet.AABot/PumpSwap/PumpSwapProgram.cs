using Solnet.AABot.PumpSwap.Model;
using Solnet.Programs;
using Solnet.Programs.Utilities;
using Solnet.Rpc.Models;
using Solnet.Wallet;

namespace Solnet.AABot.PumpSwap;

public class PumpSwapProgram
{
    public static PublicKey ProgramID = new PublicKey("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");
    public static PublicKey GlobalConfig = new PublicKey("ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw");
    public static PublicKey EventAuthority = new PublicKey("GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR");
    public static PublicKey FeeRecipient = new PublicKey("62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV");
    public static PublicKey FeeRecipientTokenAccount = new PublicKey("94qWNrtmfn42h3ZjUZwWvK1MEo9uVmmrBPd2hpNjYDjb");
    public static PublicKey FeeRecipient_Dev = new PublicKey("9xvDPD6G7NRCEu7W2M9vCLeo8we23Ww7pzQEhXcuJAmA");
    public static PublicKey FeeRecipientTokenAccount_Dev = new PublicKey("AHEgRGXFn8JbhXccWM4i1meRGPFbx8kzb9BGN6ocqRFL");

    public static PublicKey WSOL = new PublicKey("So11111111111111111111111111111111111111112");
    
    public static PublicKey LookupTableAddress = new PublicKey("7BkHkW8LvMzrVqPvb61EY8G1NQomizKg9uNoDDvpq7Fb");
    public static PublicKey DevLookupTableAddress = new PublicKey("D2dU61XJC3GQvdAxALViY5eXEz7znKa3XuSFhCVVNfVK");
    
    /*
 * 62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV 94qWNrtmfn42h3ZjUZwWvK1MEo9uVmmrBPd2hpNjYDjb
 * 7hTckgnGnLQR6sdH7YkqFTAA7VwTfYFaZ6EhEsU3saCX X5QPJcpph4mBAJDzc4hRziFftSbcygV59kRb2Fu6Je1
 * FWsW1xNtWscwNmKv6wVsU1iTzRN6wmmk3MjxRP5tT7hz 7xQYoUjUJF1Kg6WVczoTAkaNhn5syQYcbvjmFrhjWpx
 * JCRGumoE9Qi5BBgULTgdgTLjSgkCMSbF62ZZfGs84JeU DWpvfqzGWuVy9jVSKSShdM2733nrEsnnhsUStYbkj6Nn
 * G5UZAVbAf46s7cKWoyKu8kYTip9DGTpbLZ2qa9Aq69dP BWXT6RUhit9FfJQM3pBmqeFLPYmuxgmyhMGC5sGr8RbA
 * 7VtfL8fvgNfhz17qKRMjzQEXgbdpnHHHQRh54R9jP2RJ 7GFUN3bWzJMKMRZ34JLsvcqdssDbXnp589SiE33KVwcC
 * 9rPYyANsfQZw3DnDmKE3YCQF5E8oD89UXoHn9JFEhJUz Bvtgim23rfocUzxVX9j9QFxTbBnH8JZxnaGLCEkXvjKS
 * [
     "62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV",
     "7VtfL8fvgNfhz17qKRMjzQEXgbdpnHHHQRh54R9jP2RJ",
     "7hTckgnGnLQR6sdH7YkqFTAA7VwTfYFaZ6EhEsU3saCX",
     "9rPYyANsfQZw3DnDmKE3YCQF5E8oD89UXoHn9JFEhJUz",
     "AVmoTthdrX6tKt4nDjco2D775W2YK3sDhxPcMmzUAmTY",
     "FWsW1xNtWscwNmKv6wVsU1iTzRN6wmmk3MjxRP5tT7hz",
     "G5UZAVbAf46s7cKWoyKu8kYTip9DGTpbLZ2qa9Aq69dP",
     "JCRGumoE9Qi5BBgULTgdgTLjSgkCMSbF62ZZfGs84JeU"
   ]
 */
    
    public static TransactionInstruction SetCUlimit(ulong units)
    {
        List<AccountMeta> keys = new List<AccountMeta>();
        byte[] data = new byte[9];
        data.WriteU8(2, 0);
        data.WriteU64(units, 1);
        return new TransactionInstruction
        {
            ProgramId = ComputeBudgetProgram.ProgramIdKey,
            Keys = keys,
            Data = data
        };
    }
    public static TransactionInstruction CreateBuyInstruction(SwapBaseAccount account, PublicKey user, ulong sol_amount, ulong sol_slippage,
        bool isDev = false,ulong tokenPrice=0,bool isRise=false)
    {
        List<AccountMeta> keys = new()
        {
            AccountMeta.ReadOnly(account.AmmPool, false),
            AccountMeta.Writable(user, true),
            AccountMeta.ReadOnly(GlobalConfig, false),
            AccountMeta.ReadOnly(account.BaseMint, false),
            AccountMeta.ReadOnly(account.QuoteMint, false),
            AccountMeta.Writable(account.UserBaseToken, false),
            AccountMeta.Writable(account.UserQuoteToken, false),
            AccountMeta.Writable(account.PoolBaseToken, false),
            AccountMeta.Writable(account.PoolQuoteToken, false),
            AccountMeta.ReadOnly(isDev?FeeRecipient_Dev: FeeRecipient, false),
            AccountMeta.Writable(isDev?FeeRecipientTokenAccount_Dev: FeeRecipientTokenAccount, false),
            AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false),
            AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false),
            AccountMeta.ReadOnly(SystemProgram.ProgramIdKey, false),
            AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false),
            AccountMeta.ReadOnly(AssociatedTokenAccountProgram.ProgramIdKey,false),
            AccountMeta.ReadOnly(EventAuthority, false),
            AccountMeta.ReadOnly(ProgramID, false),
            AccountMeta.Writable(account.CoinCreatorVaultAta, false),
            AccountMeta.Writable(account.CoinCreatorVaultAuthority, false),
        };

        byte[] instructionBuffer = new byte[] { 129, 59, 179, 195, 110, 135, 61, 2 };

        byte[] data = new byte[33];
        int offset = 0;
        data.WriteSpan(instructionBuffer, 0);
        offset += 8;
        data.WriteU64(sol_amount, offset);
        offset += 8;
        data.WriteU64(sol_slippage, offset);
        offset += 8;
        data.WriteU64(tokenPrice, offset);
        offset += 8;
        data.WriteU8((byte)(isRise?1:0), offset);
        offset += 1;
        return new TransactionInstruction
        {
            Keys = keys,
            ProgramId =isDev? AABotProgram.ID_DevNet:AABotProgram.ID_MainNet,
            Data = data
        };
    }
    public static TransactionInstruction CreateSellInstruction(SwapBaseAccount account, PublicKey user, 
        ulong token_amount, ulong token_percent, ulong token_slippage, bool isDev = false,ulong tokenPrice=0)
    {
        List<AccountMeta> keys = new()
        {
            AccountMeta.ReadOnly(account.AmmPool, false),
            AccountMeta.Writable(user, true),
            AccountMeta.ReadOnly(GlobalConfig, false),
            AccountMeta.ReadOnly(account.BaseMint, false),
            AccountMeta.ReadOnly(account.QuoteMint, false),
            AccountMeta.Writable(account.UserBaseToken, false),
            AccountMeta.Writable(account.UserQuoteToken, false),
            AccountMeta.Writable(account.PoolBaseToken, false),
            AccountMeta.Writable(account.PoolQuoteToken, false),
            AccountMeta.ReadOnly(isDev?FeeRecipient_Dev: FeeRecipient, false),
            AccountMeta.Writable(isDev?FeeRecipientTokenAccount_Dev: FeeRecipientTokenAccount, false),
            AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false),
            AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false),
            AccountMeta.ReadOnly(SystemProgram.ProgramIdKey, false),
            AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false),
            AccountMeta.ReadOnly(AssociatedTokenAccountProgram.ProgramIdKey,false),
            AccountMeta.ReadOnly(EventAuthority, false),
            AccountMeta.ReadOnly(ProgramID, false),
            AccountMeta.Writable(account.CoinCreatorVaultAta, false),
            AccountMeta.ReadOnly(account.CoinCreatorVaultAuthority, false),
        };

        byte[] instructionBuffer = new byte[] { 238, 234, 142, 38, 107, 206, 76, 195 };

        byte[] data = new byte[40];
        int offset = 0;
        data.WriteSpan(instructionBuffer, 0);
        offset += 8;
        data.WriteU64(token_amount, offset);
        offset += 8;
        data.WriteU64(token_percent, offset);
        offset += 8;
        data.WriteU64(token_slippage, offset);
        offset += 8;
        data.WriteU64(tokenPrice, offset);
        offset += 8;

        return new TransactionInstruction
        {
            Keys = keys,
            ProgramId =isDev? AABotProgram.ID_DevNet:AABotProgram.ID_MainNet,
            Data = data
        };
    }
}