using Solnet.Programs;
using Solnet.Wallet;

namespace Solnet.AABot.PumpSwap;

public class PumpSwapPDALookup
{
    public static PublicKey FindAssociatedBondingPDA(PublicKey User, PublicKey mintAddress)
    {
        PublicKey.TryFindProgramAddress(new List<byte[]>()
            {
                User.KeyBytes,
                TokenProgram.ProgramIdKey,
                mintAddress.KeyBytes
            },
            AssociatedTokenAccountProgram.ProgramIdKey,
            out PublicKey asscociated_bondingAddress,
            out _);

        return asscociated_bondingAddress;
    }

    public static PublicKey CoinCreatorVaultAuthority(PublicKey CoinCreator)
    {
        PublicKey.TryFindProgramAddress(
            new List<byte[]>()
            {
                System.Text.Encoding.UTF8.GetBytes("creator_vault"),
                CoinCreator.KeyBytes,
            },
            PumpSwapProgram.ProgramID,
            out PublicKey pumpPoolAuthority,
            out _
        );
        return pumpPoolAuthority;
    }

    public static PublicKey CoinCreatorVaultAta(PublicKey CoinCreator)
    {
        var pumpPoolAuthority = CoinCreatorVaultAuthority(CoinCreator);

       return AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(pumpPoolAuthority, PumpSwapProgram.WSOL);

    }
}