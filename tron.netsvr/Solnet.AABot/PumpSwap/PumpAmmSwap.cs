using System.Text.Json;
using Microsoft.Extensions.Logging;
using Solnet.AABot.PumpSwap.Model;
using Solnet.Programs;
using Solnet.Programs.Utilities;
using Solnet.Rpc;
using Solnet.Rpc.Models;
using Solnet.Rpc.Types;
using Solnet.Wallet;

namespace Solnet.AABot.PumpSwap;

public class PumpAmmSwap : AABotProgram, IPumpSwap, IAABot
{
    /*public IRpcClient RpcClient { get; set; }
    private Account trader { get; set; }
    private bool IsDev = true;
    protected readonly ILogger _logger;*/

    public PumpAmmSwap(IRpcClient rpc, Account _trader, ILogger logger) : base(rpc, null, _trader, logger)
    {
        this.RpcClient = rpc;
        this.Trader = _trader;
        IsDev = rpc.NodeAddress.Host.Contains("devnet");
        _logger = logger ?? new LoggerFactory().CreateLogger("PumpAmmSwap");
    }

    public PumpAmmSwap(IRpcClient rpc, IRpcClient zeroSlotRpcClient, Account _trader, ILogger logger) : base(rpc,
        zeroSlotRpcClient, _trader, logger)
    {
        this.RpcClient = rpc;
        this.Trader = _trader;
        IsDev = rpc.NodeAddress.Host.Contains("devnet");
        _logger = logger ?? new LoggerFactory().CreateLogger("PumpAmmSwap");
    }

    private async Task<PumpSwapAmmInfo> GetAmmInfo(string mint)
    {
        var list = new List<MemCmp> { new MemCmp { Bytes = mint, Offset = 8 + 1 + 2 + 32 } };
        var res = await RpcClient.GetProgramAccountsAsync(PumpSwapProgram.ProgramID.Key,
            Commitment.Confirmed, memCmpList: list, dataSize: 300);
        if (!res.WasSuccessful || res.Result == null || (res.Result.Count <= 0))
        {
            Console.WriteLine($"GetAmmInfo: {res}");
            _logger?.LogDebug($"GetAmmInfo: {res}");
            return null;
        }

        var accountData = res.Result[0].Account?.Data[0];
        var result = PumpSwapAmmInfo.Deserialize(Convert.FromBase64String(accountData));
        result.AmmPool = new PublicKey(res.Result[0].PublicKey);
        return result;
    }

    private (TransactionInstruction computeBudget, TransactionInstruction computePrice)
        GetComputeInstructions(decimal fee = 0, decimal mev = 0, decimal cu = 1_000_000m)
    {
        //var cu = 1_000_000m;
        fee = fee == 0 ? 0.000005m : fee;
        var microLamports = (ulong)((fee - 0.000005M) * cu);
        var jito = (ulong)(mev * 1e9m); // 未使用，可以保留以备将来扩展

        var computebudget = (ulong)cu;
        var computeprice = (ulong)microLamports * 1000;
        TransactionInstruction computeBudget = PumpSwapProgram.SetCUlimit(computebudget);
        TransactionInstruction computePrice = ComputeBudgetProgram.SetComputeUnitPrice(computeprice);

        return (computeBudget, computePrice);
    }

    private async Task<SwapBaseAccount> BuildSwapBase(string mintAddress, string[] accountAddresses = null)
    {
        var mint = new PublicKey(mintAddress);
        SwapBaseAccount accounts = new SwapBaseAccount();
        if (accountAddresses != null)
        {
            accounts.AmmPool = new PublicKey(accountAddresses[0]);
            accounts.BaseMint = new PublicKey(accountAddresses[1]);
            accounts.QuoteMint = new PublicKey(accountAddresses[2]);
            accounts.PoolBaseToken = new PublicKey(accountAddresses[3]);
            accounts.PoolQuoteToken = new PublicKey(accountAddresses[4]);
            accounts.CoinCreatorVaultAta = new PublicKey(accountAddresses[5]);
            accounts.CoinCreatorVaultAuthority = new PublicKey(accountAddresses[6]);
        }
        else
        {
            var ammInfo = await GetAmmInfo(mintAddress);
            if (ammInfo == null) return null;
            accounts.AmmPool = ammInfo.AmmPool;
            accounts.BaseMint = ammInfo.BaseMint;
            accounts.QuoteMint = ammInfo.QuoteMint;
            accounts.PoolBaseToken = ammInfo.PoolBaseTokenAccount;
            accounts.PoolQuoteToken = ammInfo.PoolQuoteTokenAccount;
            accounts.CoinCreatorVaultAta = PumpSwapPDALookup.CoinCreatorVaultAta(ammInfo.CoinCreator);
            accounts.CoinCreatorVaultAuthority = PumpSwapPDALookup.CoinCreatorVaultAuthority(ammInfo.CoinCreator);
        }

        accounts.UserBaseToken = PumpSwapPDALookup.FindAssociatedBondingPDA(Trader, accounts.BaseMint);
        accounts.UserQuoteToken = PumpSwapPDALookup.FindAssociatedBondingPDA(Trader, accounts.QuoteMint);
        return accounts;
    }

    /// <summary>
    /// 模拟 提交
    /// </summary>
    /// <param name="transactions"></param>
    /// <returns></returns>
    private async Task<(decimal, string?)> GetSimulationUnits(List<TransactionInstruction> transactions)
    {
        var blockhash = await RpcClient.GetLatestBlockHashAsync();
        VersionedTransaction tx = new VersionedTransaction
        {
            FeePayer = Trader,
            RecentBlockHash = blockhash?.Result?.Value?.Blockhash,
            Instructions = new(),
            AddressTableLookups = new()
        };

        tx.Instructions.AddRange(transactions);
        tx.Sign(new List<Solnet.Wallet.Account> { Trader });

        var result = await RpcClient.SimulateTransactionAsync(tx.Serialize(), commitment: Commitment.Confirmed);
        Console.WriteLine("GetSimulationUnits=>" + result.RawRpcResponse);
        if (result.WasSuccessful)
        {
            JsonDocument doc = JsonDocument.Parse(result.RawRpcResponse);
            JsonElement root = doc.RootElement;

            JsonElement resultElement = default;
            JsonElement valueElement = default;
            JsonElement unitsConsumedElement = default;

            if (result.Result.Value.Error != null)
            {
                _logger?.LogDebug("GetSimulationUnits=>" + result.RawRpcResponse);
                return (-1, result.Result.Value.Error.Type.ToString());
            }

            // 使用 TryGetProperty 减少嵌套
            bool success = root.TryGetProperty("result", out resultElement) &&
                           resultElement.TryGetProperty("value", out valueElement) &&
                           valueElement.TryGetProperty("unitsConsumed", out unitsConsumedElement) &&
                           unitsConsumedElement.ValueKind == JsonValueKind.Number;
            if (success) return (unitsConsumedElement.GetDecimal(), null);
            else return (0m, null);

            ulong unitsConsumed = doc.RootElement
                .GetProperty("result")
                .GetProperty("value")
                .GetProperty("unitsConsumed").GetUInt64();
        }

        return (0m, null);
    }

    private async Task<(bool Successful, string Signature, string ErrorMsg)> SendTransactionAsync(
        List<TransactionInstruction> transactions)
    {
        var blockhash = await RpcClient.GetLatestBlockHashAsync();
        var builder = new VersionedTransaction
        {
            FeePayer = Trader.PublicKey,
            RecentBlockHash = blockhash.Result.Value.Blockhash,
            Instructions = transactions,
            AddressTableLookups = new()
        };


        builder.Sign(new List<Account> { Trader });
        var transaction = await RpcClient.SendTransactionAsync(builder.Serialize(), commitment: Commitment.Confirmed);

        _logger?.LogDebug(transaction.RawRpcResponse);
        Console.WriteLine("SendTransaction=>" + transaction.RawRpcResponse);
        return (transaction.WasSuccessful, transaction.Result, transaction.Reason);
    }

    /// <summary>
    /// 解析 地址表 数据
    /// </summary>
    /// <param name="jsonData"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private List<PublicKey> ExtractAddressesFromJson(string jsonData)
    {
        try
        {
            using var document = JsonDocument.Parse(jsonData);
            var addressesElement = document.RootElement
                .GetProperty("parsed")
                .GetProperty("info")
                .GetProperty("addresses");

            var addresses = new List<PublicKey>();
            foreach (var address in addressesElement.EnumerateArray())
            {
                addresses.Add(new PublicKey(address.GetString()));
            }

            return addresses;
        }
        catch (Exception ex)
        {
            throw new Exception($"无法从 JSON 提取 addresses 数组：{ex.Message}");
        }
    }

    /// <summary>
    /// 封装交易构建和发送逻辑
    /// </summary>
    /// <param name="instructions"></param>
    /// <returns></returns>
    private async Task<(bool Successful, string Signature, string ErrorMsg)> BuildAndSendTableAddressTransaction(
        List<TransactionInstruction> instructions)
    {
        var lookupTableAddress = IsDev ? PumpSwapProgram.DevLookupTableAddress : PumpSwapProgram.LookupTableAddress;

        var programId = instructions.Select(s => new PublicKey(s.ProgramId)).ToList().Distinct().ToList();
        var writableSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && s.IsSigner && s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var readonlySigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !writableSigners.Contains(s.PublicKey) &&
                        s.IsSigner && !s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var writableNonSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !s.IsSigner && s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();
        var readonlyNonSigners = instructions.Select(s => s.Keys).SelectMany(s => s)
            .Where(s => !programId.Contains(new PublicKey(s.PublicKey)) && !s.IsSigner && !s.IsWritable)
            .Select(s => s.PublicKey).Distinct().ToList();

        //获取地址表信息
        //var accountInfo =
        //    await RpcClient.GetAccountInfoAsync(lookupTableAddress, Commitment.Finalized, BinaryEncoding.JsonParsed);
        //if (accountInfo.Result.Value == null)
        //{
        //    await Task.Delay(100);
        //    accountInfo =
        //        await RpcClient.GetAccountInfoAsync(lookupTableAddress, Commitment.Finalized,
        //            BinaryEncoding.JsonParsed);
        //    Console.WriteLine("获取地址表=>" + accountInfo.RawRpcResponse);
        //    if (accountInfo.Result.Value == null)
        //    {
        //        Console.WriteLine($"获取地址表失败  退出交易");
        //        return (false, "", "获取地址表失败");
        //    }
        //}

        //var tableAddresses = ExtractAddressesFromJson(accountInfo.Result.Value.Data[0]);

        var tableAddresses = AABotProgram.TableAddressPump(IsDev);
        var write_data = new List<byte>();
        var read_data = new List<byte>();
        for (int i = 0; i < instructions.Count; i++)
        {
            for (int j = 0; j < instructions[i].Keys.Count; j++)
            {
                var key = instructions[i].Keys[j];
                if (programId.Contains(new PublicKey(key.PublicKey))) continue;
                var index = tableAddresses.IndexOf(new PublicKey(key.PublicKey));
                if (index > -1)
                {
                    if (key.IsWritable)
                    {
                        if (!write_data.Contains(Convert.ToByte(index)))
                            write_data.Add(Convert.ToByte(index));
                    }
                    else
                    {
                        if (!read_data.Contains(Convert.ToByte(index)))
                            read_data.Add(Convert.ToByte(index));
                    }
                }
            }
        }

        var lookupTableAccount = new List<Message.MessageAddressTableLookup>
        {
            new Message.MessageAddressTableLookup()
            {
                AccountKey = lookupTableAddress,
                WritableIndexes = write_data.ToArray(),
                ReadonlyIndexes = read_data.ToArray()
            }
        };


        var AccountKeys = new List<PublicKey>();
        AccountKeys.AddRange(writableSigners.Select(s => new PublicKey(s)).ToList());
        AccountKeys.AddRange(readonlySigners.Select(s => new PublicKey(s)).ToList());
        var AccountKeysWritableNonSigners = writableNonSigners.Where(s => !tableAddresses.Contains(new PublicKey(s)))
            .Select(s => new PublicKey(s)).ToList();
        AccountKeys.AddRange(AccountKeysWritableNonSigners);
        AccountKeys.AddRange(programId);
        var AccountKeysReadonlyNonSigners = readonlyNonSigners.Where(s => !tableAddresses.Contains(new PublicKey(s)))
            .Select(s => new PublicKey(s)).ToList();
        AccountKeys.AddRange(AccountKeysReadonlyNonSigners);
        AccountKeys = AccountKeys.Distinct().ToList();
        var StaticAccountKeys = new List<PublicKey>();
        StaticAccountKeys.AddRange(AccountKeys);
        StaticAccountKeys.AddRange(writableNonSigners.Select(s => new PublicKey(s)).ToList());
        StaticAccountKeys.AddRange(readonlyNonSigners.Select(s => new PublicKey(s)).ToList());
        StaticAccountKeys = StaticAccountKeys.Distinct().ToList();

        var compileInstructions = new List<CompiledInstruction>();

        for (int i = 0; i < instructions.Count; i++)
        {
            var item = instructions[i];
            var KeyIndices = item.Keys.Select(s =>
                {
                    return Convert.ToByte(StaticAccountKeys.IndexOf(new PublicKey(s.PublicKey)));
                }
            ).ToArray();
            var compileIns = new CompiledInstruction()
            {
                ProgramIdIndex = Convert.ToByte(AccountKeys.IndexOf(new PublicKey(item.ProgramId))),
                Data = item.Data,
                DataLength = new byte[] { Convert.ToByte(item.Data.Length) },
                KeyIndices = KeyIndices,
                KeyIndicesCount = new byte[] { Convert.ToByte(KeyIndices.Length) },
            };
            compileInstructions.Add(compileIns);
        }

        var res = await RpcClient.GetLatestBlockHashAsync();
        LatestBlockHash latestBlockHash = res?.Result?.Value;
        if (latestBlockHash == null)
        {
            await Task.Delay(100);
            res = await RpcClient.GetLatestBlockHashAsync();
            latestBlockHash = res?.Result?.Value;
        }

        var message = new Message.VersionedMessage()
        {
            Header = new MessageHeader()
            {
                RequiredSignatures = Convert.ToByte(writableSigners.Count + readonlySigners.Count),
                ReadOnlySignedAccounts = Convert.ToByte(readonlySigners.Count),
                ReadOnlyUnsignedAccounts = Convert.ToByte(AccountKeys.Count - AccountKeysWritableNonSigners.Count -
                                                          readonlySigners.Count - writableSigners.Count),
            },
            AccountKeys = AccountKeys,
            AddressTableLookups = lookupTableAccount,
            Instructions = compileInstructions,
            RecentBlockhash = latestBlockHash?.Blockhash,
        };

        var sign = new List<Solnet.Wallet.Account> { Trader };
        //sign.AddRange(player);

        var CompileTx = VersionedTransaction.Populate(message);
        CompileTx.Sign(sign);

        var response =
            await RpcClient.SendTransactionAsync(CompileTx.VersionedSerialize(), commitment: Commitment.Confirmed);
        _logger?.LogDebug(response.RawRpcResponse);
        Console.WriteLine(response.RawRpcResponse);
        return (response.WasSuccessful, response.Result, response.Reason);
    }


    public async Task<(bool Successful, string Signature, string ErrorMsg)> Buy(string mint, decimal sol,
        decimal slippage, decimal fee = 0, decimal mev = 0, string[] accountAddresses = null, decimal tokenPrice = 0,
        bool isRise = false, string blockHash = "")
    {


        var transactions = await GetBuy(mint, sol, slippage, fee, mev, accountAddresses, tokenPrice, isRise, blockHash);//new List<TransactionInstruction>() { computeBudget, computePrice, buyInstructions };
        var lookupTableAddress = IsDev ? PumpSwapProgram.DevLookupTableAddress : PumpSwapProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev, blockHash);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> Sell(string mint, decimal tokenamount,
        decimal slippage, decimal fee = 0, decimal mev = 0, string[] accountAddresses = null, decimal tokenPrice = 0,
        string blockHash = "")
    {
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var tokenAmountLamports = (ulong)(tokenamount * 1e6m);
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);
        var swapBaseInAccounts = await BuildSwapBase(mint, accountAddresses: accountAddresses);
        if (swapBaseInAccounts == null) return (false, string.Empty, "Accounts is null");
        var sellInstructions =
            PumpSwapProgram.CreateSellInstruction(swapBaseInAccounts, Trader, tokenAmountLamports, 0ul,
                slippagePercentage, IsDev, tokenPriceUl);
        /*var (cu,err) = await GetSimulationUnits(new List<TransactionInstruction> { sellInstructions });
        if (err != null)
        {
            return (false, string.Empty, err);
        }
        if (cu > 0)
        {
            cu = Math.Ceiling(cu * 1.1m); // 增加 10% 缓冲
            (computeBudget, computePrice) = GetComputeInstructions(fee, mev, cu);
        }*/

        var transactions = new List<TransactionInstruction>() { computeBudget, computePrice, sellInstructions };
        var lookupTableAddress = IsDev ? PumpSwapProgram.DevLookupTableAddress : PumpSwapProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev, blockHash);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> SellPercentage(string mint,
        decimal percentage, decimal slippage, decimal fee = 0, decimal mev = 0, string[] accountAddresses = null,
        decimal tokenPrice = 0, string blockHash = "")
    {


        var transactions =await GetSellPercentage(mint, percentage, slippage, fee, mev, accountAddresses, tokenPrice,
            blockHash);//new List<TransactionInstruction>() { computeBudget, computePrice, sellInstructions };
        var lookupTableAddress = IsDev ? PumpSwapProgram.DevLookupTableAddress : PumpSwapProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev: mev,
            blockHash: blockHash);
    }

    public async Task<(bool Successful, string Signature, string ErrorMsg)> BuySell(string mintAddress, decimal sol,
        decimal slippage, decimal fee = 0, decimal mev = 0, string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();

        transactions=await GetBuySell(mintAddress, sol, slippage, fee, mev, null, blockHash);
        var lookupTableAddress = IsDev ? PumpSwapProgram.DevLookupTableAddress : PumpSwapProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(transactions, null, lookupTableAddress, mev, blockHash);
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="mint"></param>
    /// <param name="sol"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="accountAddresses"></param>
    /// <param name="tokenPrice"></param>
    /// <param name="isRise"></param>
    /// <param name="blockHash"></param>
    /// <returns></returns>
    public async Task<List<TransactionInstruction>> GetBuy(string mint, decimal sol, decimal slippage, decimal fee = 0,
        decimal mev = 0, string[] accountAddresses = null, decimal tokenPrice = 0,
        bool isRise = false, string blockHash = "")
    {
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);
        var swapBaseInAccounts = await BuildSwapBase(mint, accountAddresses: accountAddresses);
        if (swapBaseInAccounts == null) return null;
        var buyInstructions =
            PumpSwapProgram.CreateBuyInstruction(swapBaseInAccounts, Trader, solAmount, slippagePercentage, IsDev,
                tokenPriceUl, isRise);

        var transactions = new List<TransactionInstruction>() { computeBudget, computePrice, buyInstructions };
        return transactions;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="mintAddress"></param>
    /// <param name="percentage"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="accountAddresses"></param>
    /// <param name="tokenPrice"></param>
    /// <param name="blockHash"></param>
    /// <returns></returns>
    public async Task<List<TransactionInstruction>> GetSellPercentage(string mintAddress, decimal percentage,
        decimal slippage, decimal fee = 0, decimal mev = 0, string[] accountAddresses = null,
        decimal tokenPrice = 0, string blockHash = "")
    {
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var tokenPercentLamports = (ulong)percentage;
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);
        var swapBaseInAccounts = await BuildSwapBase(mintAddress, accountAddresses: accountAddresses);
        if (swapBaseInAccounts == null) return null;
        var sellInstructions =
            PumpSwapProgram.CreateSellInstruction(swapBaseInAccounts, Trader, 0ul, tokenPercentLamports,
                slippagePercentage, IsDev, tokenPriceUl);

        var transactions = new List<TransactionInstruction>() { computeBudget, computePrice, sellInstructions };
        return transactions;
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="mintAddress"></param>
    /// <param name="sol"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="accountAddresses"></param>
    /// <param name="blockHash"></param>
    /// <returns></returns>
    public async Task<List<TransactionInstruction>> GetBuySell(string mintAddress, decimal sol, decimal slippage,
        decimal fee = 0, decimal mev = 0, string[] accountAddresses = null,
        string blockHash = "")
    {
        var transactions = new List<TransactionInstruction>();
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var swapAccounts = await BuildSwapBase(mintAddress, accountAddresses: accountAddresses);
        if (swapAccounts == null) return null;
        var buy = PumpSwapProgram.CreateBuyInstruction(swapAccounts, Trader, solAmount, slippagePercentage, IsDev);
        transactions.Add(buy);
        var sell = PumpSwapProgram.CreateSellInstruction(swapAccounts, Trader, 0ul, 100, slippagePercentage, IsDev);
        transactions.Add(sell);
        transactions.Insert(0, computeBudget);
        transactions.Insert(1, computePrice);
        return transactions;
    }
}