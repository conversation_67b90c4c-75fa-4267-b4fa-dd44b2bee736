using System.Numerics;

namespace Solnet.AABot.MeteoraDLMM;

partial class MeteoraDlmmImpl
{
    // 假设的常量，需根据实际协议定义
    private const int MAX_BIN_ARRAY_SIZE = 70; // 每个 bin array 包含的 bin 数量
    private const int BIN_ARRAY_BITMAP_SIZE = 512; // bitmap 大小
    private const int EXTENSION_BINARRAY_BITMAP_SIZE = 12; // 扩展 bitmap 范围因子

    // 计算 bin array 的上下限 bin ID
    public (BigInteger lowerBinId, BigInteger upperBinId) GetBinArrayLowerUpperBinId(BigInteger binArrayIndex)
    {
        BigInteger lowerBinId = binArrayIndex * MAX_BIN_ARRAY_SIZE;
        BigInteger upperBinId = lowerBinId + MAX_BIN_ARRAY_SIZE - 1;

        return (lowerBinId, upperBinId);
    }


    // 计算默认 bitmap 的索引范围
    public (BigInteger lowerBinArrayIndex, BigInteger upperBinArrayIndex) InternalBitmapRange()
    {
        BigInteger lowerBinArrayIndex = -BIN_ARRAY_BITMAP_SIZE;
        BigInteger upperBinArrayIndex = BIN_ARRAY_BITMAP_SIZE - 1;

        return (lowerBinArrayIndex, upperBinArrayIndex);
    }

    public BigInteger BinIdToBinArrayIndex(BigInteger binId)
    {
        // 除法和取模
        BigInteger idx = BigInteger.DivRem(binId, MAX_BIN_ARRAY_SIZE, out BigInteger mod);

        // 处理负数情况
        if (binId < 0 && mod != 0)
        {
            return idx - 1;
        }

        return idx;
    }

    public bool IsOverflowDefaultBinArrayBitmap(BigInteger binArrayIndex)
    {
        var (minBinArrayIndex, maxBinArrayIndex) = InternalBitmapRange();
        return binArrayIndex > maxBinArrayIndex || binArrayIndex < minBinArrayIndex;
    }

    public (decimal, decimal) BitmapTypeDetail(BitmapType type)
    {
        if (type == BitmapType.U1024)
        {
            return (1024, 1024m / 8m);
        }
        else
        {
            return (512, 512m / 8m);
        }
    }

    public BigInteger BuildBitmapFromU64Arrays(ulong[] u64Arrays, BitmapType bitmapType)
    {
        // 将每个 64 位整数转换为 8 字节数组（小端序）
        var buffers = u64Arrays.Select(n =>
        {
            byte[] bytes = BitConverter.GetBytes(n);
            if (!BitConverter.IsLittleEndian)
            {
                Array.Reverse(bytes); // 确保小端序
            }

            return bytes;
        }).ToList();

        // 合并所有字节数组
        byte[] buffer = buffers.SelectMany(b => b).ToArray();

        // 将合并的字节数组转换为 BigInteger（小端序）
        return new BigInteger(buffer, isUnsigned: true);
    }

    public int? MostSignificantBit(BigInteger number, int bitLength)
    {
        int highestIndex = bitLength - 1;

        if (number == 0)
        {
            return null;
        }

        for (int i = highestIndex; i >= 0; i--)
        {
            if (((number >> i) & 1) != 0)
            {
                return highestIndex - i;
            }
        }

        return null;
    }

    // 查找最低有效位
    public int? LeastSignificantBit(BigInteger number, int bitLength)
    {
        if (number == 0)
        {
            return null;
        }

        for (int i = 0; i < bitLength; i++)
        {
            if (((number >> i) & 1) != 0)
            {
                return i;
            }
        }

        return null;
    }
    /// <summary>
    /// 计算扩展 bitmap 的 bin array 索引范围。
    /// </summary>
    /// <returns>一个元组，包含最小和最大 bin array 索引。</returns>
    /// <exception cref="ArgumentException">如果 bitmap 大小常量无效，则抛出。</exception>
    public (BigInteger minBinArrayIndex, BigInteger maxBinArrayIndex) ExtensionBitmapRange()
    {
        if (BIN_ARRAY_BITMAP_SIZE <= 0 || EXTENSION_BINARRAY_BITMAP_SIZE < 0)
        {
            throw new ArgumentException("Invalid bitmap size constants.");
        }

        BigInteger minBinArrayIndex = -BIN_ARRAY_BITMAP_SIZE * (EXTENSION_BINARRAY_BITMAP_SIZE + 1);
        BigInteger maxBinArrayIndex = BIN_ARRAY_BITMAP_SIZE * (EXTENSION_BINARRAY_BITMAP_SIZE + 1) - 1;

        return (minBinArrayIndex, maxBinArrayIndex);
    }
    // 查找下一个有流动性的 bin array 索引
    public BigInteger? FindNextBinArrayIndexWithLiquidity(
        bool swapForY,
        BigInteger activeId,
        Lpair lbPairState,
        BinArrayBitmapExtensionAccount? binArrayBitmapExtension)
    {
        var (lowerBinArrayIndex, upperBinArrayIndex) = InternalBitmapRange();
        var startBinArrayIndex = BinIdToBinArrayIndex(activeId);
        while (true)
        {
            if (IsOverflowDefaultBinArrayBitmap(startBinArrayIndex))
            {
                if (binArrayBitmapExtension == null)
                {
                    return null;
                }

                var (minBinArrayIndex, maxBinArrayIndex) = ExtensionBitmapRange();

                if (startBinArrayIndex < 0)
                {
                    if (swapForY)
                    {
                        var binArrayIndex = FindSetBit(
                            (int)startBinArrayIndex,
                            (int)minBinArrayIndex,
                            binArrayBitmapExtension);

                        if (binArrayIndex.HasValue)
                        {
                            return binArrayIndex.Value;
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        var binArrayIndex = FindSetBit(
                            (int)startBinArrayIndex,
                            -BIN_ARRAY_BITMAP_SIZE - 1,
                            binArrayBitmapExtension);

                        if (binArrayIndex.HasValue)
                        {
                            return binArrayIndex.Value;
                        }
                        else
                        {
                            startBinArrayIndex = -BIN_ARRAY_BITMAP_SIZE;
                        }
                    }
                }
                else
                {
                    if (swapForY)
                    {
                        var binArrayIndex = FindSetBit(
                            (int)startBinArrayIndex,
                            BIN_ARRAY_BITMAP_SIZE,
                            binArrayBitmapExtension);

                        if (binArrayIndex.HasValue)
                        {
                            return binArrayIndex.Value;
                        }
                        else
                        {
                            startBinArrayIndex = BIN_ARRAY_BITMAP_SIZE - 1;
                        }
                    }
                    else
                    {
                        var binArrayIndex = FindSetBit(
                            (int)startBinArrayIndex,
                            (int)maxBinArrayIndex,
                            binArrayBitmapExtension);

                        if (binArrayIndex.HasValue)
                        {
                            return binArrayIndex.Value;
                        }
                        else
                        {
                            return null;
                        }
                    }
                }
            }
            else
            {
                var bitmapType = BitmapType.U1024;
                var (bits, bytes) = BitmapTypeDetail(bitmapType);
                var offset = startBinArrayIndex + BIN_ARRAY_BITMAP_SIZE;

                var bitmap = BuildBitmapFromU64Arrays(lbPairState.BinArrayBitmap, bitmapType);

                if (swapForY)
                {
                    var upperBitRange = new BigInteger(bits - 1) - offset;
                    var croppedBitmap = bitmap << (int)upperBitRange;

                    var msb = MostSignificantBit(croppedBitmap, (int)bits);

                    if (msb.HasValue)
                    {
                        return startBinArrayIndex - msb.Value;
                    }
                    else
                    {
                        startBinArrayIndex = lowerBinArrayIndex - 1;
                    }
                }
                else
                {
                    var lowerBitRange = offset;
                    var croppedBitmap = bitmap >> (int)lowerBitRange;
                    var lsb = LeastSignificantBit(croppedBitmap, (int)bits);

                    if (lsb.HasValue)
                    {
                        return startBinArrayIndex + lsb.Value;
                    }
                    else
                    {
                        startBinArrayIndex = upperBinArrayIndex + 1;
                    }
                }
            }
        }
    }
    /// <summary>
    /// 查找设置了位的bin数组索引
    /// </summary>
    /// <param name="startIndex">开始索引</param>
    /// <param name="endIndex">结束索引</param>
    /// <param name="binArrayBitmapExtension">bin数组位图扩展账户</param>
    /// <returns>找到的索引，如果没有找到则返回null</returns>
    public int? FindSetBit(
        int startIndex,
        int endIndex,
        BinArrayBitmapExtensionAccount binArrayBitmapExtension)
    {
        // 获取bin数组偏移量
        BigInteger GetBinArrayOffset(BigInteger binArrayIndex)
        {
            return binArrayIndex > 0
                ? binArrayIndex % BIN_ARRAY_BITMAP_SIZE
                : BigInteger.Negate(binArrayIndex + 1) % BIN_ARRAY_BITMAP_SIZE;
        }

        // 获取位图偏移量
        BigInteger GetBitmapOffset(BigInteger binArrayIndex)
        {
            return binArrayIndex > 0
                ? (binArrayIndex / BIN_ARRAY_BITMAP_SIZE) - 1
                : (BigInteger.Negate(binArrayIndex + 1) / BIN_ARRAY_BITMAP_SIZE) - 1;
        }

        // 升序查找
        if (startIndex <= endIndex)
        {
            for (int i = startIndex; i <= endIndex; i++)
            {
                int binArrayOffset = (int)GetBinArrayOffset(i);
                int bitmapOffset = (int)GetBitmapOffset(i);
                ulong[] bitmapChunks = i > 0
                    ? binArrayBitmapExtension.PositiveBinArrayBitmap[bitmapOffset]
                    : binArrayBitmapExtension.NegativeBinArrayBitmap[bitmapOffset];
                
                BigInteger bitmap = BuildBitmapFromU64Arrays(bitmapChunks, BitmapType.U512);
                
                // 检查指定位是否设置
                if (((bitmap >> binArrayOffset) & 1) != 0)
                {
                    return i;
                }
            }
        }
        // 降序查找
        else
        {
            for (int i = startIndex; i >= endIndex; i--)
            {
                int binArrayOffset = (int)GetBinArrayOffset(i);
                int bitmapOffset = (int)GetBitmapOffset(i);
                ulong[] bitmapChunks = i > 0
                    ? binArrayBitmapExtension.PositiveBinArrayBitmap[bitmapOffset]
                    : binArrayBitmapExtension.NegativeBinArrayBitmap[bitmapOffset];
                
                BigInteger bitmap = BuildBitmapFromU64Arrays(bitmapChunks, BitmapType.U512);
                
                // 检查指定位是否设置
                if (((bitmap >> binArrayOffset) & 1) != 0)
                {
                    return i;
                }
            }
        }

        return null;
    }
}
