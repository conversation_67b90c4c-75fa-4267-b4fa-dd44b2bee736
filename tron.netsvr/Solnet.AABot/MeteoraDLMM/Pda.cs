using System.Numerics;
using System.Text;
using Solnet.Wallet;

namespace Solnet.AABot.MeteoraDLMM;

public class Pda
{
    /// <summary>
    /// 派生BinArrayBitmapExtension账户的PDA地址
    /// </summary>
    /// <param name="lbPair">LB对的公钥</param>
    /// <param name="programId">程序ID</param>
    /// <returns>PDA地址和碰撞种子</returns>
    public static ProgramAddress DeriveBinArrayBitmapExtension(PublicKey lbPair, PublicKey programId)
    {
        if (lbPair == null) throw new ArgumentNullException(nameof(lbPair));
        if (programId == null) throw new ArgumentNullException(nameof(programId));

        var seeds = new[]
        {
            Encoding.UTF8.GetBytes("bitmap"),
            lbPair.KeyBytes
        };

        return TryFindProgramAddress(seeds, programId);
    }

    /// <summary>
    /// 尝试查找程序派生地址
    /// </summary>
    /// <param name="seeds">种子数组</param>
    /// <param name="programId">程序ID</param>
    /// <returns>程序派生地址和碰撞种子</returns>
    private static ProgramAddress TryFindProgramAddress(byte[][] seeds, PublicKey programId)
    {
        if (PublicKey.TryFindProgramAddress(seeds, programId, out PublicKey address, out byte bump))
        {
            return new ProgramAddress { Address = address, Bump = bump };
        }

        throw new InvalidOperationException("无法找到有效的程序派生地址");
    }
    
    /// <summary>
    /// 派生BinArray账户的PDA地址
    /// </summary>
    /// <param name="lbPair">LB对的公钥</param>
    /// <param name="index">索引值</param>
    /// <param name="programId">程序ID</param>
    /// <returns>PDA地址和碰撞种子</returns>
    public static ProgramAddress DeriveBinArray(PublicKey lbPair, BigInteger index, PublicKey programId)
    {
        if (lbPair == null) throw new ArgumentNullException(nameof(lbPair));
        if (programId == null) throw new ArgumentNullException(nameof(programId));

        byte[] binArrayBytes;
    
        // 处理负数和正数的情况
        if (index < 0)
        {
            // 对于负数，需要使用二进制补码表示
            // 在C#中，BigInteger已经使用二进制补码表示负数
            // 我们只需要确保取8字节的小端序表示
            byte[] tempBytes = index.ToByteArray();
            binArrayBytes = new byte[8];
        
            // 复制字节，确保小端序
            int length = Math.Min(tempBytes.Length, 8);
            Array.Copy(tempBytes, 0, binArrayBytes, 0, length);
        
            // 如果原始字节数组小于8字节，需要填充符号位
            if (tempBytes.Length < 8)
            {
                byte fillByte = (byte)(index < 0 ? 0xFF : 0x00);
                for (int i = length; i < 8; i++)
                {
                    binArrayBytes[i] = fillByte;
                }
            }
        }
        else
        {
            // 对于正数，直接获取小端序字节数组
            byte[] tempBytes = index.ToByteArray();
            binArrayBytes = new byte[8];
        
            // 复制字节，确保小端序
            int length = Math.Min(tempBytes.Length, 8);
            Array.Copy(tempBytes, 0, binArrayBytes, 0, length);
        
            // 如果原始字节数组小于8字节，需要用0填充
            if (tempBytes.Length < 8)
            {
                for (int i = length; i < 8; i++)
                {
                    binArrayBytes[i] = 0;
                }
            }
        }

        var seeds = new[]
        {
            Encoding.UTF8.GetBytes("bin_array"),
            lbPair.KeyBytes,
            binArrayBytes
        };

        return TryFindProgramAddress(seeds, programId);
    }
}

/// <summary>
/// 程序派生地址结构
/// </summary>
public class ProgramAddress
{
    /// <summary>
    /// 派生的地址
    /// </summary>
    public PublicKey Address { get; set; }

    /// <summary>
    /// 碰撞种子
    /// </summary>
    public byte Bump { get; set; }
}
