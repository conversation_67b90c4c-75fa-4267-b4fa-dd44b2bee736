using Solnet.Programs;
using Solnet.Programs.Utilities;
using Solnet.Rpc.Models;
using Solnet.Wallet;

namespace Solnet.AABot.Pumpfun;

public class PumpfunProgram
{
    public static PublicKey ProgramID = new PublicKey("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P");
    public static PublicKey FeeReceiver = new PublicKey("CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM");

    public static PublicKey DevFeeReceiver = new PublicKey("68yFSZxzLWJXkxxRGydZ63C6mHx1NLEDWmwN9Lb5yySg");
    public static PublicKey GlobalAccount = new PublicKey("4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf");
    public static PublicKey RentProgram = new PublicKey("SysvarRent111111111111111111111111111111111");

    public static PublicKey EventAuthority = new PublicKey("Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1");

    //GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR
    public static PublicKey mint_authority = new PublicKey("TSLvdd1pWpHVjahSpsvCXUbgwsL3JAcvokwaKt1eokM");
    public static PublicKey mplTokenMetadata = new PublicKey("metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s");
    public static PublicKey LookupTableAddress = new PublicKey("7BkHkW8LvMzrVqPvb61EY8G1NQomizKg9uNoDDvpq7Fb");
    public static PublicKey DevLookupTableAddress = new PublicKey("D2dU61XJC3GQvdAxALViY5eXEz7znKa3XuSFhCVVNfVK");

    public static TransactionInstruction SetCUlimit(ulong units)
    {
        List<AccountMeta> keys = new List<AccountMeta>();
        byte[] data = new byte[9];
        data.WriteU8(2, 0);
        data.WriteU64(units, 1);
        return new TransactionInstruction
        {
            ProgramId = ComputeBudgetProgram.ProgramIdKey,
            Keys = keys,
            Data = data
        };
    }

    public static TransactionInstruction CreateBuyInstruction(PublicKey tokenMint, PublicKey bonding,
        PublicKey assocBondingAddr, PublicKey ataUser, PublicKey user, ulong sol_amount, ulong sol_slippage,
        bool isDev = false, ulong tokenPrice=0,bool isRise=false, string create = "")
    {
        PublicKey.TryFindProgramAddress(new List<byte[]>()
                    {
                        //creator_vault
                        new byte[]{99, 114, 101, 97, 116, 111, 114, 45, 118, 97, 117, 108, 116},
                        //user.KeyBytes,
                        new PublicKey(create).KeyBytes,
                        //TokenProgram.ProgramIdKey,
                        //mintAddress.KeyBytes
                    },
                PumpfunProgram.ProgramID,
                out PublicKey createvalut,
                out _);

        List<AccountMeta> keys = new()
        {
            AccountMeta.ReadOnly(GlobalAccount, false),
            AccountMeta.Writable(isDev ? DevFeeReceiver : FeeReceiver, false),
            AccountMeta.ReadOnly(tokenMint, false),
            AccountMeta.Writable(bonding, false),
            AccountMeta.Writable(assocBondingAddr, false),
            AccountMeta.Writable(ataUser, false),
            AccountMeta.Writable(user, true),
            AccountMeta.ReadOnly(SystemProgram.ProgramIdKey, false),
            AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false),
            AccountMeta.Writable(createvalut, false),
            AccountMeta.ReadOnly(EventAuthority, false),
            AccountMeta.ReadOnly(ProgramID, false),
            AccountMeta.ReadOnly(AssociatedTokenAccountProgram.ProgramIdKey, false)
        };

        byte[] instructionBuffer = new byte[] { 82, 225, 119, 231, 78, 29, 45, 70 };

        byte[] data = new byte[33];
        int offset = 0;
        data.WriteSpan(instructionBuffer, 0);
        offset += 8;
        data.WriteU64(sol_amount, offset);
        offset += 8;
        data.WriteU64(sol_slippage, offset);
        offset += 8;
        data.WriteU64(tokenPrice, offset);
        offset += 8;
        data.WriteU8((byte)(isRise?1:0), offset);
        offset += 1;
        return new TransactionInstruction
        {
            Keys = keys,
            ProgramId = isDev ? AABotProgram.ID_DevNet : AABotProgram.ID_MainNet,
            Data = data
        };
    }

    public static TransactionInstruction CreateSellInstruction(PublicKey tokenMint, PublicKey bonding,
        PublicKey assocBondingAddr, PublicKey ataUser, PublicKey user, ulong token_amount, ulong token_percentage,
        ulong sol_slippage, bool isDev = false, ulong tokenPrice=0, string create = "")
    {

        PublicKey.TryFindProgramAddress(new List<byte[]>()
                    {
                        //creator_vault
                        new byte[]{99, 114, 101, 97, 116, 111, 114, 45, 118, 97, 117, 108, 116},
                        //user.KeyBytes,
                        new PublicKey(create).KeyBytes,
                        //TokenProgram.ProgramIdKey,
                        //mintAddress.KeyBytes
                    },
                PumpfunProgram.ProgramID,
                out PublicKey createvalut,
                out _);
        // Define the keys
        List<AccountMeta> keys = new()
        {
            AccountMeta.ReadOnly(GlobalAccount, false),
            AccountMeta.Writable(isDev ? DevFeeReceiver : FeeReceiver, false),
            AccountMeta.ReadOnly(tokenMint, false),
            AccountMeta.Writable(bonding, false),
            AccountMeta.Writable(assocBondingAddr, false),
            AccountMeta.Writable(ataUser, false),
            AccountMeta.Writable(user, true),
            AccountMeta.ReadOnly(SystemProgram.ProgramIdKey, false),
            AccountMeta.Writable(createvalut, false),
            AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false),
            AccountMeta.ReadOnly(EventAuthority, false),
            AccountMeta.ReadOnly(ProgramID, false)
        };

        byte[] instructionBuffer = new byte[] { 93, 88, 60, 34, 91, 18, 86, 197 };


        byte[] data = new byte[40];
        int offset = 0;
        data.WriteSpan(instructionBuffer, 0);
        offset += 8;
        data.WriteU64(token_amount, offset);
        offset += 8;
        data.WriteU64(token_percentage, offset);
        offset += 8;
        data.WriteU64(sol_slippage, offset);
        offset += 8;
        data.WriteU64(tokenPrice, offset);
        offset += 8;
        return new TransactionInstruction
        {
            Keys = keys,
            ProgramId = isDev ? AABotProgram.ID_DevNet : AABotProgram.ID_MainNet,
            Data = data
        };
    }



    public static TransactionInstruction CreateBuyInstructionPump(PublicKey tokenMint, PublicKey bonding, PublicKey assocBondingAddr, PublicKey ataUser, PublicKey user, ulong sol, ulong slippage,string create, bool isDev = false)
    {
        
        PublicKey.TryFindProgramAddress(new List<byte[]>()
            {
                //creator_vault
                new byte[]{99, 114, 101, 97, 116, 111, 114, 45, 118, 97, 117, 108, 116},
                //user.KeyBytes,
                new PublicKey(create).KeyBytes,
                //TokenProgram.ProgramIdKey,
                //mintAddress.KeyBytes
            },
            PumpfunProgram.ProgramID,
            out PublicKey createvalut,
            out _);
        
        List<AccountMeta> keys = new()
            {
                AccountMeta.ReadOnly(GlobalAccount, false),
                AccountMeta.Writable(isDev?DevFeeReceiver:FeeReceiver, false),
                AccountMeta.ReadOnly(tokenMint, false),
                AccountMeta.Writable(bonding, false),
                AccountMeta.Writable(assocBondingAddr, false),
                AccountMeta.Writable(ataUser, false),
                AccountMeta.Writable(user, true),
                AccountMeta.ReadOnly(SystemProgram.ProgramIdKey, false),
                AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false),
                AccountMeta.ReadOnly(createvalut, false),
                AccountMeta.ReadOnly(EventAuthority, false),
                AccountMeta.ReadOnly(ProgramID, false)
            };
        //66 06 3d 12 01 da eb ea
        byte[] instructionBuffer = new byte[] { 0x66, 0x06, 0x3d, 0x12, 0x01, 0xda, 0xeb, 0xea };

        byte[] data = new byte[24];
        int offset = 0;
        data.WriteSpan(instructionBuffer, 0);
        offset += 8;
        data.WriteU64(sol, offset);
        offset += 8;
        data.WriteU64(slippage, offset);
        offset += 8;


        return new TransactionInstruction
        {
            Keys = keys,
            ProgramId = ProgramID,
            Data = data
        };
    }
}