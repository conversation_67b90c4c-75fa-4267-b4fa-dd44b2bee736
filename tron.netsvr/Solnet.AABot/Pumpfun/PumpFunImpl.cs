using System.Collections.Generic;
using System.Diagnostics;
using System.Numerics;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Solnet.Metaplex.Utilities;
using Solnet.Programs;
using Solnet.Programs.Models;
using Solnet.Programs.Utilities;
using Solnet.Rpc;
using Solnet.Rpc.Builders;
using Solnet.Rpc.Core.Http;
using Solnet.Rpc.Models;
using Solnet.Rpc.Types;
using Solnet.Wallet;
using static System.Runtime.InteropServices.JavaScript.JSType;
using static Solnet.AABot.Pumpfun.Accounts;
using static Solnet.Rpc.Models.Message;

namespace Solnet.AABot.Pumpfun;

public class PumpFunImpl : AABotProgram, IPumpFun, IAABot
{
    /*
    public IRpcClient RpcClient { get; set; }
    private Account trader { get; set; }
    private bool IsDev = true;
    protected readonly ILogger _logger;
    */

    public PumpFunImpl(IRpcClient rpc, Account _trader, ILogger logger) : base(rpc, null, _trader, logger)
    {
        RpcClient = rpc;
        Trader = _trader;
        IsDev = rpc.NodeAddress.Host.Contains("devnet");
        _logger = logger ?? new LoggerFactory().CreateLogger("PumpFunImpl");
    }

    public PumpFunImpl(IRpcClient rpc, IRpcClient zeroSlotRpcClient, Account _trader, ILogger logger) : base(rpc,
        zeroSlotRpcClient, _trader, logger)
    {
        RpcClient = rpc;
        Trader = _trader;
        IsDev = rpc.NodeAddress.Host.Contains("devnet");
        _logger = logger ?? new LoggerFactory().CreateLogger("PumpFunImpl");
    }

    private (PublicKey mint, PublicKey associatedUser, PublicKey bondingCurveAddress, PublicKey associatedCurveAddress)
        GetAccountKeys(string mintAddress)
    {
        PublicKey mint = new PublicKey(mintAddress);
        PublicKey associatedUser = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, mint);
        PublicKey bondingCurveAddress = PDALookup.FindBondingPDA(mint);
        PublicKey associatedCurveAddress = PDALookup.FindAssociatedBondingPDA(bondingCurveAddress, mint);

        return (mint, associatedUser, bondingCurveAddress, associatedCurveAddress);
    }

    private (PublicKey mint, PublicKey associatedUser, PublicKey bondingCurveAddress, PublicKey associatedCurveAddress)
        GetMultipleAccountKeys(string mintAddress, Account player)
    {
        PublicKey mint = new PublicKey(mintAddress);
        PublicKey associatedUser = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(player, mint);
        PublicKey bondingCurveAddress = PDALookup.FindBondingPDA(mint);
        PublicKey associatedCurveAddress = PDALookup.FindAssociatedBondingPDA(bondingCurveAddress, mint);

        return (mint, associatedUser, bondingCurveAddress, associatedCurveAddress);
    }

    private async Task<BondingCurve> GetBondingCurveAsync(string accountAddress,
        Commitment commitment = Commitment.Processed)
    {
        /*
        try
        {
            var res = await RpcClient.GetAccountInfoAsync(accountAddress, commitment);
            if (!res.WasSuccessful)
                return new AccountResultWrapper<BondingCurve>(res);
            if (res.Result.Value == null)
            {
                return null;
            }

            var resultingAccount = BondingCurve.Deserialize(Convert.FromBase64String(res.Result.Value.Data[0]));
            return new AccountResultWrapper<BondingCurve>(res, resultingAccount);
        }
        catch (Exception exception)
        {
            Debug.WriteLine(exception);
            return null;
        }*/
        var res = await RpcClient.GetAccountInfoAsync(accountAddress, commitment);
        while (res.WasSuccessful == false || res.Result == null || res.Result.Value == null)
        {
            res = await RpcClient.GetAccountInfoAsync(accountAddress, commitment);
            await Task.Delay(200);
        }

        var resultingAccount = BondingCurve.Deserialize(Convert.FromBase64String(res.Result.Value.Data[0]));
        return resultingAccount; //new AccountResultWrapper<BondingCurve>(res, resultingAccount);
    }

    private (TransactionInstruction computeBudget, TransactionInstruction computePrice)
        GetComputeInstructions(decimal fee = 0, decimal mev = 0, decimal cu = 1_000_000m)
    {
        //var cu = 1_000_000m;
        fee = fee == 0 ? 0.000005m : fee;
        var microLamports = (ulong)((fee - 0.000005M) * cu);
        var jito = (ulong)(mev * 1e9m); // 未使用，可以保留以备将来扩展

        var computebudget = (ulong)cu;
        var computeprice = (ulong)microLamports * 1000;
        TransactionInstruction computeBudget = PumpfunProgram.SetCUlimit(computebudget);
        TransactionInstruction computePrice = ComputeBudgetProgram.SetComputeUnitPrice(computeprice);

        return (computeBudget, computePrice);
    }

    /// <summary>
    /// 提取公共的初始化逻辑
    /// </summary>
    private (PublicKey mint, PublicKey associatedUser, PublicKey bondingCurveAddress, PublicKey associatedCurveAddress,
        TransactionInstruction computeBudget, TransactionInstruction computePrice)
        InitializeTransaction(string mintAddress, decimal fee = 0, decimal mev = 0)
    {
        var cu = 1_000_000m;
        fee = fee == 0 ? 0.000005m : fee;
        var microLamports = (ulong)((fee - 0.000005m) * ((decimal)Math.Pow(10, 15) / cu));
        var jito = (ulong)(mev * 1e9m); // 未使用，可以保留以备将来扩展

        PublicKey mint = new PublicKey(mintAddress);
        PublicKey associatedUser = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(Trader, mint);
        PublicKey bondingCurveAddress = PDALookup.FindBondingPDA(mint);
        PublicKey associatedCurveAddress = PDALookup.FindAssociatedBondingPDA(bondingCurveAddress, mint);

        var computebudget = (ulong)cu;
        var computeprice = (ulong)microLamports;
        TransactionInstruction computeBudget = PumpfunProgram.SetCUlimit(computebudget);
        TransactionInstruction computePrice = ComputeBudgetProgram.SetComputeUnitPrice(computeprice);

        return (mint, associatedUser, bondingCurveAddress, associatedCurveAddress, computeBudget, computePrice);
    }

    /// <summary>
    /// 模拟 提交
    /// </summary>
    /// <param name="transactions"></param>
    /// <returns></returns>
    private async Task<(decimal, string?)> GetSimulationUnits(List<TransactionInstruction> transactions)
    {
        var blockhash = await RpcClient.GetLatestBlockHashAsync();
        VersionedTransaction tx = new VersionedTransaction
        {
            FeePayer = Trader,
            RecentBlockHash = blockhash?.Result?.Value?.Blockhash,
            Instructions = new(),
            AddressTableLookups = new()
        };

        tx.Instructions.AddRange(transactions);
        tx.Sign(new List<Solnet.Wallet.Account> { Trader });

        var result = await RpcClient.SimulateTransactionAsync(tx.Serialize(), commitment: Commitment.Confirmed);
        Console.WriteLine("GetSimulationUnits=>" + result.RawRpcResponse);
        if (result.WasSuccessful)
        {
            JsonDocument doc = JsonDocument.Parse(result.RawRpcResponse);
            JsonElement root = doc.RootElement;

            JsonElement resultElement = default;
            JsonElement valueElement = default;
            JsonElement unitsConsumedElement = default;
            if (result.Result.Value.Error != null)
            {
                _logger?.LogDebug("GetSimulationUnits=>" + result.RawRpcResponse);
                return (-1, result.Result.Value.Error.Type.ToString());
            }

            // 使用 TryGetProperty 减少嵌套
            bool success = root.TryGetProperty("result", out resultElement) &&
                           resultElement.TryGetProperty("value", out valueElement) &&
                           valueElement.TryGetProperty("unitsConsumed", out unitsConsumedElement) &&
                           unitsConsumedElement.ValueKind == JsonValueKind.Number;
            if (success) return (unitsConsumedElement.GetDecimal(), null);
            else return (0m, null);

            ulong unitsConsumed = doc.RootElement
                .GetProperty("result")
                .GetProperty("value")
                .GetProperty("unitsConsumed").GetUInt64();
        }

        return (0m, null);
    }

    /// <summary>
    /// 封装交易构建和发送逻辑
    /// </summary>
    /// <param name="instructions"></param>
    /// <returns></returns>
    private async Task<(bool Successful, string Signature, string ErrorMsg)> BuildAndSendTransaction(
        List<TransactionInstruction> instructions)
    {
        var res = await RpcClient.GetLatestBlockHashAsync();
        LatestBlockHash latestBlockHash = res?.Result?.Value;
        if (latestBlockHash == null)
        {
            await Task.Delay(100);
            res = await RpcClient.GetLatestBlockHashAsync();
            latestBlockHash = res?.Result?.Value;
        }

        VersionedTransaction tx = new VersionedTransaction
        {
            FeePayer = Trader,
            RecentBlockHash = latestBlockHash.Blockhash,
            Instructions = new(),
            AddressTableLookups = new()
        };

        tx.Instructions.AddRange(instructions);
        tx.Sign(new List<Solnet.Wallet.Account> { Trader });

        var response = await RpcClient.SendTransactionAsync(tx.Serialize(), commitment: Commitment.Confirmed);

        _logger?.LogDebug(response.RawRpcResponse);
        Console.WriteLine(response.RawRpcResponse);
        return (response.WasSuccessful, response.Result, response.Reason);
    }


    private bool? GetTableLookupIndexs(List<TransactionInstruction> instructions, PublicKey address)
    {
        var accountMeta = instructions.Select(s => s.Keys).SelectMany(s => s).ToList();
        var data = instructions.Where(s => new PublicKey(s.ProgramId) == address).FirstOrDefault();
        if (data != null) return null;
        var itemData = accountMeta.Where(s => s.PublicKey == address.Key).FirstOrDefault();
        if (itemData == null) return null;
        if (itemData.IsSigner == true) return null;
        if (itemData.IsWritable) return true;
        else return false;
    }

    /// <summary>
    /// Buy 方法
    /// </summary>
    /// <param name="mintAddress"></param>
    /// <param name="sol"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="tokenPrice"></param>
    /// <param name="isRise"></param>
    /// <param name="blockHash"></param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> Buy(string mintAddress, decimal sol,
        decimal slippage, decimal fee = 0, decimal mev = 0, decimal tokenPrice = 0, bool isRise = false,
        string blockHash = "")
    {
        var instructions = await GetBuy(mintAddress, sol, slippage, fee, mev, null, tokenPrice, isRise, blockHash);
        //var instructions = new List<TransactionInstruction> { computeBudget, computePrice, buyInstruction };
        var lookupTableAddress = IsDev ? PumpfunProgram.DevLookupTableAddress : PumpfunProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(instructions, new List<Account>(), lookupTableAddress, mev,
            blockHash);
    }

    /// <summary>
    /// 买入
    /// </summary>
    /// <param name="mint"></param>
    /// <param name="sol"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> MultipleBuy(
        List<(Account, decimal)> player1, string mintAddress, decimal sol, decimal slippage, decimal fee = 0,
        decimal mev = 0, decimal tokenPrice = 0, bool isRise = false, string blockHash = "")
    {
        var (mint, associatedUser, bondingCurveAddress, associatedCurveAddress) =
            GetAccountKeys(mintAddress);
        fee = fee == 0 ? 0.00006M : 0;
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);

        var Instructions = new List<TransactionInstruction>();
        Instructions.Insert(0, computePrice);
        Instructions.Insert(0, computeBudget);
        var player = player1.OrderBy(s => s.Item1.PublicKey.Key).ToList();

        var create = await GetBondingCurveAsync(bondingCurveAddress);
        var buyInstruction = PumpfunProgram.CreateBuyInstruction(
            mint,
            bondingCurveAddress,
            associatedCurveAddress,
            associatedUser,
            Trader.PublicKey,
            solAmount,
            slippagePercentage,
            IsDev, tokenPriceUl, isRise, create.Creater);
        Instructions.Add(buyInstruction);

        for (int i = 0; i < player.Count; i++)
        {
            var item = player[i];
            var (mintMultiple, associatedUserMultiple, bondingCurveAddressMultiple, associatedCurveAddressMultiple) =
                GetMultipleAccountKeys(mintAddress, item.Item1);
            //create = await GetBondingCurveAsync(bondingCurveAddress);
            Instructions.Add(
                PumpfunProgram.CreateBuyInstruction(
                    mintMultiple,
                    bondingCurveAddressMultiple,
                    associatedCurveAddressMultiple,
                    associatedUserMultiple,
                    item.Item1.PublicKey,
                    (ulong)(item.Item2 * SolHelper.LAMPORTS_PER_SOL),
                    slippagePercentage,
                    IsDev, tokenPriceUl, isRise, create.Creater));
        }

        var lookupTableAddress = IsDev ? PumpfunProgram.DevLookupTableAddress : PumpfunProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(Instructions, player.Select(s => s.Item1).ToList(),
            lookupTableAddress, mev, blockHash);
    }

    // Sell 方法
    public async Task<(bool Successful, string Signature, string ErrorMsg)> Sell(string mintAddress,
        decimal tokenAmount, decimal slippage, decimal fee = 0,
        decimal mev = 0, decimal tokenPrice = 0, string blockHash = "")
    {
        var (mint, associatedUser, bondingCurveAddress, associatedCurveAddress) =
            GetAccountKeys(mintAddress);
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var tokenAmountUl = (ulong)(tokenAmount);
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);

        var create = await GetBondingCurveAsync(bondingCurveAddress);

        TransactionInstruction sellInstruction = PumpfunProgram.CreateSellInstruction(
            mint,
            bondingCurveAddress,
            associatedCurveAddress,
            associatedUser,
            Trader.PublicKey,
            tokenAmountUl,
            0,
            slippagePercentage,
            IsDev,
            tokenPriceUl, create.Creater
        );
        //var (cu, err) = await GetSimulationUnits(new List<TransactionInstruction> { sellInstruction });
        //if (err != null)
        //{
        //    return (false, string.Empty, err);
        //}

        //if (cu > 0)
        //{
        //    cu = Math.Ceiling(cu * 1.1m); // 增加 10% 缓冲
        //    (computeBudget, computePrice) = GetComputeInstructions(fee, mev, cu);
        //}

        var instructions = new List<TransactionInstruction> { computeBudget, computePrice, sellInstruction };
        var lookupTableAddress = IsDev ? PumpfunProgram.DevLookupTableAddress : PumpfunProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(instructions, new List<Account>(), lookupTableAddress, mev,
            blockHash);
    }

    // SellPercentage 方法
    public async Task<(bool Successful, string Signature, string ErrorMsg)> SellPercentage(string mintAddress,
        decimal percentage, decimal slippage, decimal fee = 0,
        decimal mev = 0, decimal tokenPrice = 0, string blockHash = "")
    {
        var instructions =
            await GetSellPercentage(mintAddress, percentage, slippage, fee, mev, null, tokenPrice, blockHash);
        //new List<TransactionInstruction> { computeBudget, computePrice, sellInstruction });
        var lookupTableAddress = IsDev ? PumpfunProgram.DevLookupTableAddress : PumpfunProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(instructions, new List<Account>(), lookupTableAddress, mev,
            blockHash);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="mintAddress"></param>
    /// <param name="sol"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="blockHash"></param>
    /// <returns></returns>
    public async Task<(bool Successful, string Signature, string ErrorMsg)> BuySell(string mintAddress, decimal sol,
        decimal slippage, decimal fee = 0, decimal mev = 0, string blockHash = "")
    {
        var instructions =
            await GetBuySell(mintAddress, sol, slippage, fee, mev, null,
                blockHash); //new List<TransactionInstruction> { computeBudget, computePrice, buyInstruction, sellInstruction };
        var lookupTableAddress = IsDev ? PumpfunProgram.DevLookupTableAddress : PumpfunProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(instructions, new List<Account>(), lookupTableAddress, mev,
            blockHash);
    }

    public async Task<string> Create()
    {
        _logger.LogDebug($"{nameof(Create)}");
        return string.Empty;
    }


    /// <summary>
    /// Buy 方法
    /// </summary>
    /// <param name="mintAddress"></param>
    /// <param name="sol"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="tokenPrice"></param>
    /// <param name="isRise"></param>
    /// <param name="blockHash"></param>
    /// <returns></returns>
    public async Task<List<Solnet.Rpc.Models.TransactionInstruction>> GetBuy(string mintAddress, decimal sol,
        decimal slippage, decimal fee = 0, decimal mev = 0, string[] accountAddresses = null, decimal tokenPrice = 0,
        bool isRise = false,
        string blockHash = "")
    {
        var (mint, associatedUser, bondingCurveAddress, associatedCurveAddress) =
            GetAccountKeys(mintAddress);
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);

        var create = await GetBondingCurveAsync(bondingCurveAddress);

        TransactionInstruction buyInstruction = PumpfunProgram.CreateBuyInstruction(
            mint,
            bondingCurveAddress,
            associatedCurveAddress,
            associatedUser,
            Trader.PublicKey,
            solAmount,
            slippagePercentage,
            IsDev, tokenPriceUl, isRise, create.Creater);
        //var (cu, err) = await GetSimulationUnits(new List<TransactionInstruction> { buyInstruction });
        //if (err != null)
        //{
        //    return (false, string.Empty, err);
        //}

        //if (cu > 0)
        //{
        //    cu = Math.Ceiling(cu * 1.1m); // 增加 10% 缓冲
        //    (computeBudget, computePrice) = GetComputeInstructions(fee, mev, cu);
        //}

        var instructions = new List<TransactionInstruction> { computeBudget, computePrice, buyInstruction };
        return instructions;
    }

    /// <summary>
    /// 给其他账号购买
    /// </summary>
    /// <param name="mintAddress"></param>
    /// <param name="sol"></param>
    /// <param name="slippage"></param>
    /// <param name="user"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="accountAddresses"></param>
    /// <param name="blockHash"></param>
    /// <returns></returns>
    public async Task<List<Solnet.Rpc.Models.TransactionInstruction>> GetBuyToUser(string mintAddress, decimal sol,
        decimal slippage, string user, decimal fee = 0, decimal mev = 0, string blockHash = "")
    {
        var instructions = new List<TransactionInstruction>();
        var (mint, associatedUser, bondingCurveAddress, associatedCurveAddress) =
            GetAccountKeys(mintAddress);
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var create = await GetBondingCurveAsync(bondingCurveAddress);
        var toOwner = Trader.PublicKey;
        if (!string.IsNullOrWhiteSpace(user))
        {
            toOwner = new PublicKey(user);
            associatedUser = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(toOwner, mint);
        }

        var tokenAmount =
            await RpcClient.GetTokenAccountsByOwnerAsync(toOwner.Key, mint,
                commitment: Commitment.Confirmed);
        if (tokenAmount.Result?.Value?.Count == 0)
            instructions.Add(
                AssociatedTokenAccountProgram.CreateAssociatedTokenAccount(Trader, toOwner, mint));
        TransactionInstruction buyInstruction = PumpfunProgram.CreateBuyInstructionPump(
            mint,
            bondingCurveAddress,
            associatedCurveAddress,
            associatedUser,
            toOwner,
            solAmount,
            slippagePercentage,
            create.Creater,
            IsDev);
        instructions.Add(buyInstruction);
        instructions.Insert(0, computePrice);
        instructions.Insert(0, computeBudget);
        //var instructions = new List<TransactionInstruction> { computeBudget, computePrice, buyInstruction };
        return instructions;
    }

    /// <summary>
    /// 买入
    /// </summary>
    /// <param name="mint"></param>
    /// <param name="sol"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <returns></returns>
    public async Task<List<Solnet.Rpc.Models.TransactionInstruction>> GetMultipleBuy(List<(Account, decimal)> player1,
        string mintAddress, decimal sol, decimal slippage, decimal fee = 0, decimal mev = 0, decimal tokenPrice = 0,
        bool isRise = false, string blockHash = "")
    {
        var (mint, associatedUser, bondingCurveAddress, associatedCurveAddress) =
            GetAccountKeys(mintAddress);
        fee = fee == 0 ? 0.00006M : 0;
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);

        var Instructions = new List<TransactionInstruction>();
        Instructions.Insert(0, computePrice);
        Instructions.Insert(0, computeBudget);
        var player = player1.OrderBy(s => s.Item1.PublicKey.Key).ToList();

        var create = await GetBondingCurveAsync(bondingCurveAddress);
        var buyInstruction = PumpfunProgram.CreateBuyInstruction(
            mint,
            bondingCurveAddress,
            associatedCurveAddress,
            associatedUser,
            Trader.PublicKey,
            solAmount,
            slippagePercentage,
            IsDev, tokenPriceUl, isRise, create.Creater);
        Instructions.Add(buyInstruction);

        for (int i = 0; i < player.Count; i++)
        {
            var item = player[i];
            var (mintMultiple, associatedUserMultiple, bondingCurveAddressMultiple, associatedCurveAddressMultiple) =
                GetMultipleAccountKeys(mintAddress, item.Item1);
            //create = await GetBondingCurveAsync(bondingCurveAddress);
            Instructions.Add(
                PumpfunProgram.CreateBuyInstruction(
                    mintMultiple,
                    bondingCurveAddressMultiple,
                    associatedCurveAddressMultiple,
                    associatedUserMultiple,
                    item.Item1.PublicKey,
                    (ulong)(item.Item2 * SolHelper.LAMPORTS_PER_SOL),
                    slippagePercentage,
                    IsDev, tokenPriceUl, isRise, create.Creater));
        }

        /*var lookupTableAddress = IsDev ? PumpfunProgram.DevLookupTableAddress : PumpfunProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(Instructions, player.Select(s => s.Item1).ToList(),lookupTableAddress,mev, blockHash);*/
        return Instructions;
    }

    // Sell 方法
    public async Task<List<Solnet.Rpc.Models.TransactionInstruction>> GetSell(string mintAddress, decimal tokenAmount,
        decimal slippage, decimal fee = 0, decimal mev = 0, decimal tokenPrice = 0, string blockHash = "")
    {
        var (mint, associatedUser, bondingCurveAddress, associatedCurveAddress) =
            GetAccountKeys(mintAddress);
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var tokenAmountUl = (ulong)(tokenAmount);
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);

        var create = await GetBondingCurveAsync(bondingCurveAddress);

        TransactionInstruction sellInstruction = PumpfunProgram.CreateSellInstruction(
            mint,
            bondingCurveAddress,
            associatedCurveAddress,
            associatedUser,
            Trader.PublicKey,
            tokenAmountUl,
            0,
            slippagePercentage,
            IsDev,
            tokenPriceUl, create.Creater
        );
        //var (cu, err) = await GetSimulationUnits(new List<TransactionInstruction> { sellInstruction });
        //if (err != null)
        //{
        //    return (false, string.Empty, err);
        //}

        //if (cu > 0)
        //{
        //    cu = Math.Ceiling(cu * 1.1m); // 增加 10% 缓冲
        //    (computeBudget, computePrice) = GetComputeInstructions(fee, mev, cu);
        //}

        var instructions = new List<TransactionInstruction> { computeBudget, computePrice, sellInstruction };
        /*var lookupTableAddress = IsDev ? PumpfunProgram.DevLookupTableAddress : PumpfunProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(instructions, new List<Account>(),lookupTableAddress,mev, blockHash);*/
        return instructions;
    }

    // SellPercentage 方法
    public async Task<List<Solnet.Rpc.Models.TransactionInstruction>> GetSellPercentage(string mintAddress,
        decimal percentage, decimal slippage, decimal fee = 0, decimal mev = 0, string[] accountAddresses = null,
        decimal tokenPrice = 0,
        string blockHash = "")
    {
        var (mint, associatedUser, bondingCurveAddress, associatedCurveAddress) =
            GetAccountKeys(mintAddress);
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);

        var tokenPercentage = (ulong)(percentage);
        var slippagePercentage = (ulong)(slippage);
        var tokenPriceUl = (ulong)(tokenPrice * 1e19m);

        var create = await GetBondingCurveAsync(bondingCurveAddress);

        TransactionInstruction sellInstruction = PumpfunProgram.CreateSellInstruction(
            mint,
            bondingCurveAddress,
            associatedCurveAddress,
            associatedUser,
            Trader.PublicKey,
            0,
            tokenPercentage,
            slippagePercentage,
            IsDev, tokenPriceUl, create.Creater);
        //var (cu, err) = await GetSimulationUnits(new List<TransactionInstruction> { sellInstruction });
        //if (err != null)
        //{
        //    return (false, string.Empty, err);
        //}

        //if (cu > 0)
        //{
        //    cu = Math.Ceiling(cu * 1.1m); // 增加 10% 缓冲
        //    (computeBudget, computePrice) = GetComputeInstructions(fee, mev, cu);
        //}

        var instructions = new List<TransactionInstruction> { computeBudget, computePrice, sellInstruction };
        /*var lookupTableAddress = IsDev ? PumpfunProgram.DevLookupTableAddress : PumpfunProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(instructions, new List<Account>(),lookupTableAddress,mev, blockHash);*/
        return instructions;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="mintAddress"></param>
    /// <param name="sol"></param>
    /// <param name="slippage"></param>
    /// <param name="fee"></param>
    /// <param name="mev"></param>
    /// <param name="blockHash"></param>
    /// <returns></returns>
    public async Task<List<Solnet.Rpc.Models.TransactionInstruction>> GetBuySell(string mintAddress, decimal sol,
        decimal slippage, decimal fee = 0, decimal mev = 0, string[] accountAddresses = null, string blockHash = "")
    {
        var (mint, associatedUser, bondingCurveAddress, associatedCurveAddress) =
            GetAccountKeys(mintAddress);
        var (computeBudget, computePrice) = GetComputeInstructions(fee, mev);
        var solAmount = (ulong)(sol * SolHelper.LAMPORTS_PER_SOL);
        var slippagePercentage = (ulong)(slippage);
        var create = await GetBondingCurveAsync(bondingCurveAddress);
        TransactionInstruction buyInstruction = PumpfunProgram.CreateBuyInstruction(
            mint,
            bondingCurveAddress,
            associatedCurveAddress,
            associatedUser,
            Trader.PublicKey,
            solAmount,
            slippagePercentage,
            IsDev, 0, false, create.Creater);
        TransactionInstruction sellInstruction = PumpfunProgram.CreateSellInstruction(
            mint,
            bondingCurveAddress,
            associatedCurveAddress,
            associatedUser,
            Trader.PublicKey,
            0,
            100,
            slippagePercentage,
            IsDev, 0, create.Creater);
        var instructions = new List<TransactionInstruction>
            { computeBudget, computePrice, buyInstruction, sellInstruction };
        /*var lookupTableAddress = IsDev ? PumpfunProgram.DevLookupTableAddress : PumpfunProgram.LookupTableAddress;
        return await BuildAndSendTableAddressTransaction(instructions, new List<Account>(),lookupTableAddress,mev,blockHash);*/
        return instructions;
    }
}