namespace Tran.Abp.WrokTest;

//public record MsgType(string msgType,string clientId, object msgData);

public class NatsTronMsgType<T>
{
    public string MsgType { get; set; }
    public string ClientId { get; set; }
    public T MsgData { get; set; }

    public NatsTronMsgType(string msgType, string clientId, T msgData)
    {
        this.MsgType = msgType;
        this.ClientId = clientId;
        this.MsgData = msgData;
    }
}

public class NatsMsgTronOn
{
    public NatsMsgTronOn(string channel, string uId)
    {
        Channel = channel;
        UId = uId;
    }

    public string Channel { get; set; }
    public string UId { get; set; }
}