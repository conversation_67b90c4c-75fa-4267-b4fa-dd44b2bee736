using System.Collections.Concurrent;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using NATS.Net;
using Tran.Abp.WrokTest;
using Volo.Abp.DependencyInjection;

namespace Tron.Abp.Nats;

public class Nats : INats, ISingletonDependency, IAsyncDisposable
{
    private readonly NatsClient _natsClient;
    ConcurrentDictionary<string, string> dictionary = new ConcurrentDictionary<string, string>();
    public ILogger<Nats> Logger { get; set; }

    public Nats(NatsClient natsClient)
    {
        _natsClient = natsClient;
        Logger = NullLogger<Nats>.Instance;
    }

    public async Task SubscribeAsync(string channel)
    {
        await foreach (var msg in _natsClient.SubscribeAsync<NatsTronMsgType<object>>(channel))
        {
            Logger.LogDebug($"Nats 收到消息: {msg.Subject}: {msg.Data.MsgType} {msg.Data.MsgData} ");
            if (msg.Data.MsgType == NatsMsgType.TronOn)
            {
                var data =JsonSerializer.Deserialize<NatsMsgTronOn>(msg.Data.MsgData.ToString()) ;
                dictionary[data.UId] = data.Channel;
            }
        }
    }
    /// <summary>
    /// 下发消息
    /// </summary>
    /// <param name="key"></param>
    /// <param name="msg"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<bool> PublishAsync<T>(string key, T msg)
    {
        if (dictionary.TryGetValue(key, out var channel))
        {
            await _natsClient.PublishAsync<T>(channel, msg);
        }

        return true;
    }

    public async ValueTask DisposeAsync()
    {
        await _natsClient.DisposeAsync();
    }
}