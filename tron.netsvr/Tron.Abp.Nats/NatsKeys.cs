namespace Tron.Abp.Nats;

public class NatsKeys
{
    /// <summary>
    /// 客户端发布消息
    /// </summary>
    public const string SysSubscribeTronMsgType = "tron.msgtype";
    /// <summary>
    /// 客户端订阅消息
    /// </summary>
    public const string UserSubscribeTx = "tron.solana.tx.";
    public const string UserSubscribeNewCoinCreated = "tron.solana.newcoincreated";
    
    /// <summary>
    /// 
    /// </summary>
    public const string AppPublishMarketCapTokenInfoCreate = "token.info.create";
    /// <summary>
    /// 
    /// </summary>
    public const string AppPublishMarketCapTokenInfoDelete = "token.info.delete";
}

public class NatsMsgType
{
    /// <summary>
    /// 交易
    /// </summary>
    public const string Tx="tron.solana.tx";
    /// <summary>
    /// 订阅交易
    /// </summary>
    public const string TronOn="trade.on";
    /// <summary>
    /// 取消订阅
    /// </summary>
    public const string TronOff="tron.off";
}