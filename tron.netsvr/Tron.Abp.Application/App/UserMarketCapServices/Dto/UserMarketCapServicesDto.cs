using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tron.Abp.Domain;
using Tron.Abp.Domain.Entity;

namespace Tron.Abp.Application.App.UserMyTokensServices.Dto
{
    public class UserMarketCapInputDto
    {
        public string Chain { get; set; } = "Solana";
        public int? TaskId { get; set; } // 任务ID
        public string? TokenAddress { get; set; } // Token地址
        public List<int> GroupId { get; set; } // 钱包组
        public int Mode { get; set; } // 1拉盘/买入，2砸盘/卖出，3防夹刷量
        public List<decimal?> AmountData { get; set; } = new List<decimal?> { null, null }; // 金额区间
        public int? SellPercent { get; set; } // 砸盘百分比
        public List<decimal?> IntervalData { get; set; } = new List<decimal?> { null, null }; // 时间区间
        public int? Slippage { get; set; } // 滑点
        public decimal? Gas { get; set; } // Gas费
        public bool IsMev { get; set; } = false; // 是否开启MEV
        public decimal? Mev { get; set; } // MEV费
        public int? Status { get; set; } // -1终止，0暂停，1运行
        public string? TargetPrice { get; set; } // 目标价格
        public int? BrushCount { get; set; } // 刷量次数
        public BuyModel BuyData { get; set; } = new ();
        public bool IsValidate { get; set; } = true;
    }

    public class BuyModel
    {
        /// <summary>
        /// 是否开启 开启后当外部买入总金额超过配置的总金额 暂停任务
        /// </summary>
        public bool IsOpen { get; set; }
        /// <summary>
        /// 外部买入总金额
        /// </summary>
        public decimal? AmountAll { get; set; }
        /// <summary>
        /// 当前外部买入总金额
        /// </summary>
        public decimal? CurrentAmountAll { get; set; }
    }

    /// <summary>
    /// 获取任务列表输入参数
    /// </summary>
    public class UserMarketCapListInput
    {
        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;
        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 10;
        /// <summary>
        /// 搜索关键字
        /// </summary>
        public string? SearchKey { get; set; }
    }

    /// <summary>
    /// 任务列表输出
    /// </summary>
    public class UserMarketCapTaskOutput
    {
        public int Id { get; set; }
        public string TokenAddress { get; set; }
        public string TokenName { get; set; }
        public string TokenSymbol { get; set; }
        public string? TokenIcon { get; set; }
        public decimal MarketCap { get; set; }
        public decimal CurrentPrice { get; set; }
        public decimal Amount24H { get; set; }
        public decimal HolderAmount { get; set; }
        public decimal ClearHolderSol { get; set; }
        public decimal ClearHolderProfit { get; set; }
        public HolderItem OwnerHolderData { get; set; }
        public HolderItem OutsideHolderData { get; set; }

        public int OrderIndex { get; set; }
        public List<UserMarketCapSubTaskDto> taskData { get; set; } = new();
        public DateTime CreateTime { get; set; }
        public string? Mark { get; set; }
    }

    public class HolderItem
    {
        public decimal HolderAmount { get; set; }
        public int WallerCount { get; set; }
    }

    public class UserMarketCapSubTaskDto
    {
        public int Id { get; set; }
        /// <summary>
        /// 所属主任务ID
        /// </summary>
        public int TaskId { get; set; }
        /// <summary>
        /// 所属用户ID
        /// </summary>
        public int UId { get; set; }
        /// <summary>
        /// 链
        /// </summary>
        public string Chain { get; set; }
        /// <summary>
        /// 代币地址
        /// </summary>
        public string TokenAddress { get; set; }
        /// <summary>
        /// 代币名称
        /// </summary>
        public string TokenName { get; set; }
        /// <summary>
        /// 代币缩写
        /// </summary>
        public string TokenSymbol { get; set; }
        /// <summary>
        /// 代币Icon
        /// </summary>
        public string TokenIcon { get; set; }
        /// <summary>
        /// 分组Ids
        /// </summary>
        [SugarColumn(ColumnDescription = "分组Ids", ColumnDataType = "text", IsJson = true, IsNullable = true)]
        public List<int> GroupId { get; set; }
        /// <summary>
        /// 模式 1拉盘/买入，2砸盘/卖出，3防夹刷量
        /// </summary>
        public int Mode { get; set; }
        /// <summary>
        /// 拉盘买入金额区间[开始，结束]单位SOL
        /// </summary>
        [SugarColumn(ColumnDescription = "拉盘买入金额区间", IsJson = true, IsNullable = true)]
        public List<decimal?> AmountData { get; set; } = new() { null, null };
        /// <summary>
        /// 砸盘卖出百分比
        /// </summary>
        [SugarColumn(ColumnDescription = "砸盘百分比", IsNullable = true)]
        public int? SellPercent { get; set; }
        /// <summary>
        /// 时间区间[开始，结束]单位秒
        /// </summary>
        [SugarColumn(ColumnDescription = "时间区间", IsJson = true, IsNullable = true)]
        public List<int> IntervalData { get; set; }
        /// <summary>
        /// 滑点
        /// </summary>
        [SugarColumn(ColumnDescription = "滑点", IsNullable = true)]
        public int Slippage { get; set; }
        /// <summary>
        /// gas费
        /// </summary>
        [SugarColumn(ColumnDescription = "gas费", IsNullable = true)]
        public decimal? Gas { get; set; }
        /// <summary>
        /// 是否开启mev
        /// </summary>
        [SugarColumn(ColumnDescription = "是否开启mev", IsNullable = true)]
        public bool IsMev { get; set; }
        /// <summary>
        /// mev费
        /// </summary>
        [SugarColumn(ColumnDescription = "mev费", IsNullable = true)]
        public decimal? Mev { get; set; }
        /// <summary>
        /// 目标价格
        /// </summary>
        public string? TargetPrice { get; set; }
        /// <summary>
        /// 当前价格
        /// </summary>
        public string? CurrentPrice { get; set; }
        /// <summary>
        /// 刷量次数
        /// </summary>
        [SugarColumn(ColumnDescription = "刷量次数", IsNullable = true)]
        public int? BrushCount { get; set; }
        /// <summary>
        /// 当前刷量次数
        /// </summary>
        [SugarColumn(ColumnDescription = "当前刷量次数", IsNullable = true)]
        public int? CurrentBrushCount { get; set; }
        /// <summary>
        /// 状态 -1终止，0暂停，1运行
        /// </summary>
        [SugarColumn(ColumnDescription = "状态", IsNullable = true)]
        public int Status { get; set; }
        /// <summary>
        /// 是否开启 开启后当外部买入总金额超过配置的总金额 暂停任务
        /// </summary>
        public bool IsOpen { get; set; }
        /// <summary>
        /// 外部买入总金额
        /// </summary>
        [SugarColumn(ColumnDescription = "当前价格", ColumnDataType = "decimal(30,6)", IsNullable = true)]
        public decimal? AmountAll { get; set; }
        /// <summary>
        /// 当前外部买入总金额
        /// </summary>
        [SugarColumn(ColumnDescription = "当前价格", ColumnDataType = "decimal(30,6)", IsNullable = true)]
        public decimal? CurrentAmountAll { get; set; }
        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public string? Mark { get; set; }
    }

    /// <summary>
    /// 更新任务状态输入参数
    /// </summary>
    public class UpdateTaskStatusInput
    {
        /// <summary>
        /// 子任务ID
        /// </summary>
        public int SubTaskId { get; set; }
    }

    /// <summary>
    /// 清仓请求参数
    /// </summary>
    public class ClearTokenInput
    {
        /// <summary>
        /// 链
        /// </summary>
        public string Chain { get; set; } = "Solana";
        /// <summary>
        /// 代币地址
        /// </summary>
        public string TokenAddress { get; set; }
        /// <summary>
        /// 任务ID（可选，如果指定则只清仓该任务相关的代币）
        /// </summary>
        public int? TaskId { get; set; }
        /// <summary>
        /// 滑点
        /// </summary>
        public int Slippage { get; set; } = 10;
        /// <summary>
        /// Gas费
        /// </summary>
        public decimal? Gas { get; set; }
        /// <summary>
        /// 是否开启MEV
        /// </summary>
        public bool IsMev { get; set; } = false;
        /// <summary>
        /// MEV费
        /// </summary>
        public decimal? Mev { get; set; }
    }

    /// <summary>
    /// 更新代币排序输入参数
    /// </summary>
    public class UpdateTokenOrderInput
    {
        /// <summary>
        /// 代币排序列表
        /// </summary>
        public List<TokenOrderItem> TokenOrders { get; set; } = new();
    }

    /// <summary>
    /// 代币排序项
    /// </summary>
    public class TokenOrderItem
    {
        /// <summary>
        /// 主任务ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 排序索引
        /// </summary>
        public int OrderIndex { get; set; }
    }
    /// <summary>
    /// 删除任务 
    /// </summary>
    public class DeleteMarkCapInput
    {
        /// <summary>
        /// 任务ID 或者子任务 ID
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// 区分任务true 还是子任务false
        /// </summary>
        public bool IsMasterTask { get; set; } = false;
    }
}
