using FreeRedis;
using Jaina;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using NUglify.Helpers;
using SqlSugar;
using System.Linq;
using NATS.Net;
using Tron.Abp.Application.App.CopyTradeServices.Dto;
using Tron.Abp.Application.App.UserMyTokensServices.Dto;
using Tron.Abp.Core;
using Tron.Abp.Domain;
using Tron.Abp.Multiplex.Contracts;
using Tron.Abp.Multiplex.Solana;
using Tron.Abp.Nats;
using Tron.Abp.SqlSugar;

namespace Tron.Abp.Application.App.UserMyTokensServices
{
    [ApiExplorerSettings(GroupName = "app_v1")]
    public class UserMarketCapServices : ApplicationService
    {
        private readonly AppUserManager _userManager;
        private IRedisClient _redisClient;
        private ISolanaApi _solanaApi;
        private IEventPublisher _eventPublisher;
        private readonly SqlSugarRepository<User> _userRepository;
        private readonly SqlSugarRepository<UserWallet> _userWalletRepository;
        private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;
        private readonly SqlSugarRepository<UserGroup> _userUserGroupRepository;
        private readonly SqlSugarRepository<UserMarketCapTask> _userUserMarketCapTaskRepository;
        private readonly SqlSugarRepository<UserMarketCapSubTask> _userUserMarketCapSubTaskRepository;
        private NatsClient _natsClient;
        public UserMarketCapServices(IRedisClient redisClient,
            ISolanaApi solanaApi, IEventPublisher eventPublisher
            , AppUserManager userManager,
            SqlSugarRepository<User> userRepository,
            SqlSugarRepository<UserWallet> userWalletRepository,
            SqlSugarRepository<UserAllTraderLog> userAllTraderLogRepository,
            SqlSugarRepository<UserGroup> userUserGroupRepository,
            SqlSugarRepository<UserMarketCapTask> userUserMarketCapTaskRepository,
            SqlSugarRepository<UserMarketCapSubTask> userUserMarketCapSubTaskRepository, NatsClient natsClient)
        {
            _redisClient = redisClient;
            _solanaApi = solanaApi;
            _eventPublisher = eventPublisher;
            _userManager = userManager;
            _userRepository = userRepository;
            _userWalletRepository = userWalletRepository;
            _userAllTraderLogRepository = userAllTraderLogRepository;
            _userUserGroupRepository = userUserGroupRepository;
            _userUserMarketCapTaskRepository = userUserMarketCapTaskRepository;
            _userUserMarketCapSubTaskRepository = userUserMarketCapSubTaskRepository;
            _natsClient = natsClient;
        }


        /// <summary>
        /// 新建任务
        /// </summary>
        [HttpPost]
        [Route("/marketcap/add")]
        public async Task<UserMarketCapSubTask> MaketCapAdd(UserMarketCapInputDto input)
        {
            var curUid = (int)_userManager.UserId;

            // 参数验证
            if (string.IsNullOrEmpty(input.TokenAddress))
                throw Oops.Bah("请输入代币地址");

            if (input.IsValidate)
            {
                if (input.Mode < 1 || input.Mode > 3)
                    throw Oops.Bah("请选择正确的模式");

                if (input.GroupId == null || !input.GroupId.Any())
                    throw Oops.Bah("请选择钱包组");

                // 验证钱包组是否属于当前用户
                var userGroups = await _userUserGroupRepository.AsQueryable()
                    .Where(g => g.UId == curUid && input.GroupId.Contains(g.Id))
                    .ToListAsync();

                if (userGroups.Count != input.GroupId.Count)
                    throw Oops.Bah("选择的钱包组不存在或不属于当前用户");

                // 模式特定验证
                switch (input.Mode)
                {
                    case 1: // 拉盘/买入
                        if (input.AmountData == null || input.AmountData.Count != 2 ||
                            !input.AmountData[0].HasValue || !input.AmountData[1].HasValue ||
                            input.AmountData[0] <= 0 || input.AmountData[1] <= 0 ||
                            input.AmountData[0] >= input.AmountData[1])
                            throw Oops.Bah("请输入正确的金额区间");

                        if (string.IsNullOrEmpty(input.TargetPrice) ||
                            !decimal.TryParse(input.TargetPrice, out var targetPrice) || targetPrice <= 0)
                            throw Oops.Bah("请输入正确的目标价格");
                        break;

                    case 2: // 砸盘/卖出
                        if (!input.SellPercent.HasValue || input.SellPercent <= 0 || input.SellPercent > 100)
                            throw Oops.Bah("请输入正确的卖出百分比(1-100)");

                        if (string.IsNullOrEmpty(input.TargetPrice) ||
                            !decimal.TryParse(input.TargetPrice, out var sellTargetPrice) || sellTargetPrice <= 0)
                            throw Oops.Bah("请输入正确的目标价格");
                        break;

                    case 3: // 防夹刷量
                        if (!input.BrushCount.HasValue || input.BrushCount <= 0)
                            throw Oops.Bah("请输入正确的刷量次数");

                        if (input.AmountData == null || input.AmountData.Count != 2 ||
                            !input.AmountData[0].HasValue || !input.AmountData[1].HasValue ||
                            input.AmountData[0] <= 0 || input.AmountData[1] <= 0 ||
                            input.AmountData[0] > input.AmountData[1])
                            throw Oops.Bah("请输入正确的金额区间");
                        break;
                }

                // 验证时间区间
                if (input.IntervalData == null || input.IntervalData.Count != 2 ||
                    !input.IntervalData[0].HasValue || !input.IntervalData[1].HasValue ||
                    input.IntervalData[0] <= 0 || input.IntervalData[1] <= 0 ||
                    input.IntervalData[0] > input.IntervalData[1])
                    throw Oops.Bah("请输入正确的时间区间");

                if ((await _userWalletRepository.AsQueryable().Where(s => input.GroupId.Contains(s.GId)).CountAsync()) <= 0)
                    throw Oops.Bah("请选择钱包组钱包数量不能为0");
            }

            UserMarketCapTask mainTask;

            // 检查是否已存在主任务
            if (input.TaskId.HasValue)
            {
                // 添加到现有任务
                mainTask = await _userUserMarketCapTaskRepository.AsQueryable()
                    .Where(t => t.Id == input.TaskId.Value && t.UId == curUid)
                    .FirstAsync();

                if (mainTask == null)
                    throw Oops.Bah("指定的任务不存在或不属于当前用户");

                // 验证代币地址是否匹配
                if (mainTask.TokenAddress != input.TokenAddress)
                    throw Oops.Bah("代币地址与现有任务不匹配");
            }
            else
            {
                // 创建新的主任务
                // 检查是否已存在相同代币的任务
                var existingTask = await _userUserMarketCapTaskRepository.AsQueryable()
                    .Where(t => t.UId == curUid && t.TokenAddress == input.TokenAddress)
                    .FirstAsync();

                if (existingTask != null)
                {
                    mainTask = existingTask;
                }
                else
                {
                    // 获取代币信息 (这里需要根据实际的API调用来获取代币信息)
                    // 暂时使用默认值，实际项目中应该调用Solana API获取代币信息
                    var tokenInfo = await _solanaApi.GetMint(input.TokenAddress);

                    mainTask = new UserMarketCapTask
                    {
                        UId = curUid,
                        Chain = input.Chain,
                        TokenAddress = input.TokenAddress,
                        TokenName = tokenInfo?.Name ?? "",
                        TokenSymbol = tokenInfo?.Symbol ?? "",
                        TokenIcon = tokenInfo?.Icon ?? "",
                        OrderIndex = 0,
                        CreateTime = DateTime.Now,
                        Mark = ""
                    };

                    mainTask.Id = await _userUserMarketCapTaskRepository.AsInsertable(mainTask)
                        .ExecuteReturnIdentityAsync();
                    
                    /*
                     * // 创建代币订阅 {uId:number,tokenAddress:string}
                            tokenInfoCreate = 'token.info.create',
                            // 删除代币订阅 {uId:number,tokenAddress:string}
                            tokenInfoDelete = 'token.info.delete',
                     * 
                     */
                   await _natsClient.PublishAsync(NatsKeys.AppPublishMarketCapTokenInfoCreate, new
                    {
                        uId = curUid,
                        tokenAddress = input.TokenAddress
                    });
                }
            }
            var subTask = new UserMarketCapSubTask();

            if (!input.IsValidate)
            {
                if (input.GroupId != null)
                {
                    // 验证钱包组是否属于当前用户
                    var userGroups = await _userUserGroupRepository.AsQueryable()
                        .Where(g => g.UId == curUid && input.GroupId.Contains(g.Id))
                        .ToListAsync();
                    if (userGroups.Count == input.GroupId.Count)
                    {
                        // 验证时间区间
                        if (!(input.IntervalData == null || input.IntervalData.Count != 2 ||
                            !input.IntervalData[0].HasValue || !input.IntervalData[1].HasValue ||
                            input.IntervalData[0] <= 0 || input.IntervalData[1] <= 0 ||
                            input.IntervalData[0] > input.IntervalData[1]))
                        {
                            if (vaildModel(input))
                            {
                                // 验证钱包组钱包数量
                                if ((await _userWalletRepository.AsQueryable().Where(s => input.GroupId.Contains(s.GId)).CountAsync()) > 0)
                                {
                                    goto createsub;
                                }
                            }
                        }
                    }
                }

                return subTask;
            }

        createsub:
            // 创建子任务
            subTask = new UserMarketCapSubTask
            {
                TaskId = mainTask.Id,
                UId = curUid,
                Chain = input.Chain,
                TokenAddress = input.TokenAddress,
                TokenName = mainTask.TokenName,
                TokenSymbol = mainTask.TokenSymbol,
                TokenIcon = mainTask.TokenIcon,
                GroupId = input.GroupId,
                Mode = input.Mode,
                AmountData = input.AmountData?.Select(x => (decimal?)x).ToList() ?? new List<decimal?> { null, null },
                SellPercent = input.SellPercent,
                IntervalData = input.IntervalData?.Select(x => (int)x.Value).ToList() ?? new List<int>(),
                Slippage = input.Slippage ?? 10,
                Gas = input.Gas,
                IsMev = input.IsMev,
                Mev = input.IsMev ? input.Mev : null,
                TargetPrice = string.IsNullOrEmpty(input.TargetPrice) ? null : decimal.Parse(input.TargetPrice),
                CurrentPrice = null, // 需要从市场获取当前价格
                BrushCount = input.BrushCount,
                CurrentBrushCount = 0,
                Status = input.Status ?? 0, // 默认运行状态
                IsOpen = input.BuyData?.IsOpen ?? false,
                AmountAll = input.BuyData?.AmountAll,
                CurrentAmountAll = 0,
                CreateTime = DateTime.Now,
                Mark = ""
            };

            subTask.Id = await _userUserMarketCapSubTaskRepository.AsInsertable(subTask)
                .ExecuteReturnIdentityAsync();
            if (subTask.Status == 1)
            {
                // 发送任务到消息队列进行处理
                var taskMessage = new Dictionary<string, string>
                {
                    ["uid"] = curUid.ToString(),
                    ["taskId"] = subTask.Id.ToString(),
                    ["Status"] = subTask.Status.ToString(),
                };

                // 这里需要根据实际的消息队列配置来发送消息
                await _redisClient.XAddAsync(StreamKey.MarketCapActionStream, taskMessage);
            }

            return subTask;
        }

        private bool vaildModel(UserMarketCapInputDto input)
        {
            var result = true;
            // 模式特定验证
            switch (input.Mode)
            {
                case 1: // 拉盘/买入
                    if (input.AmountData == null || input.AmountData.Count != 2 ||
                        !input.AmountData[0].HasValue || !input.AmountData[1].HasValue ||
                        input.AmountData[0] <= 0 || input.AmountData[1] <= 0 ||
                        input.AmountData[0] >= input.AmountData[1])
                        return false;

                    if (string.IsNullOrEmpty(input.TargetPrice) ||
                        !decimal.TryParse(input.TargetPrice, out var targetPrice) || targetPrice <= 0)
                        return false;
                    break;

                case 2: // 砸盘/卖出
                    if (!input.SellPercent.HasValue || input.SellPercent <= 0 || input.SellPercent > 100)
                        return false;

                    if (string.IsNullOrEmpty(input.TargetPrice) ||
                        !decimal.TryParse(input.TargetPrice, out var sellTargetPrice) || sellTargetPrice <= 0)
                        return false;
                    break;

                case 3: // 防夹刷量
                    if (!input.BrushCount.HasValue || input.BrushCount <= 0)
                        return false;

                    if (input.AmountData == null || input.AmountData.Count != 2 ||
                        !input.AmountData[0].HasValue || !input.AmountData[1].HasValue ||
                        input.AmountData[0] <= 0 || input.AmountData[1] <= 0 ||
                        input.AmountData[0] > input.AmountData[1])
                        return false;
                    break;
            }
            return result;
        }

        /// <summary>
        /// 获取任务列表
        /// </summary>
        [HttpPost]
        [Route("/marketcap/list")]
        public async Task<SqlSugarPagedList<UserMarketCapTaskOutput>> GetMarketCapList(UserMarketCapListInput input)
        {
            var curUid = (int)_userManager.UserId;

            var queryable = _userUserMarketCapTaskRepository.AsQueryable()
                .Where(t => t.UId == curUid)
                .WhereIF(!string.IsNullOrEmpty(input.SearchKey), t =>
                    t.TokenAddress.Contains(input.SearchKey) ||
                    t.TokenName.Contains(input.SearchKey) ||
                    t.TokenSymbol.Contains(input.SearchKey))
                .OrderBy(t => t.OrderIndex)
                .OrderByDescending(t => t.CreateTime);

            var pageData = await queryable.Select(t => new UserMarketCapTaskOutput
            {
                Id = t.Id,
                TokenAddress = t.TokenAddress,
                TokenName = t.TokenName,
                TokenSymbol = t.TokenSymbol,
                TokenIcon = t.TokenIcon,
                OrderIndex = t.OrderIndex,
                CreateTime = t.CreateTime,
                Mark = t.Mark
            }).ToPagedListAsync(input.PageIndex, input.PageSize);

            var mints = pageData.Items.Select(s => s.TokenAddress).ToArray();
            var tokenInfos = await _solanaApi.GetMints(mints);
            var solUsdtPrice = await _redisClient.GetAsync<decimal>(RedisKey.SolanaWSOLPrice);

            pageData.Items.ForEach(async it =>
            {
                var tokenInfo = tokenInfos.FirstOrDefault(s => s.Mint == it.TokenAddress);
                var TokenUsdtPrice = Convert.ToDecimal(tokenInfo?.Price ?? 0);
                var TokenPrice = tokenInfo == null ? 0 : await _solanaApi.GetMintPrice(it.TokenAddress, tokenInfo.Type, tokenInfo.PoolId);
                //市值
                it.MarketCap = 0;
                //单价SOL
                //it.CurrentPrice = solUsdtPrice == 0 ? 0 : TokenUsdtPrice / solUsdtPrice;
                it.CurrentPrice = TokenPrice;
                //23小成交额SOL
                it.Amount24H = 0;
                //持有金额
                it.HolderAmount = 0;
                //清仓预计获取SOL
                it.ClearHolderSol = it.HolderAmount * (it.CurrentPrice == 0 ? 1 : it.CurrentPrice);
                //清仓收益
                it.ClearHolderProfit = 0;
                //内部持有数据
                it.OwnerHolderData = new HolderItem() { WallerCount = 0, HolderAmount = 0 };
                //外部持有数据
                it.OutsideHolderData = new HolderItem() { WallerCount = 0, HolderAmount = 0 };
            });


            // 获取每个主任务的子任务
            var taskIds = pageData.Items.Select(t => t.Id).ToList();
            var subTasks = await _userUserMarketCapSubTaskRepository.AsQueryable()
                .Where(st => taskIds.Contains(st.TaskId))
                .OrderByDescending(st => st.CreateTime)
                .ToListAsync();

            // 将子任务分组并赋值给主任务
            var subTaskGroups = subTasks.GroupBy(st => st.TaskId)
                .ToDictionary(g => g.Key, g => g.ToList().Adapt<List<UserMarketCapSubTaskDto>>());

            foreach (var task in pageData.Items)
            {
                task.taskData = subTaskGroups.ContainsKey(task.Id)
                    ? subTaskGroups[task.Id]
                    : new List<UserMarketCapSubTaskDto>();
            }

            return pageData;
        }

        /// <summary>
        /// 暂停/启动任务
        /// </summary>
        [HttpPost]
        [Route("/marketcap/status")]
        public async Task<bool> UpdateTaskStatus(UpdateTaskStatusInput input)
        {
            var curUid = (int)_userManager.UserId;

            var subTask = await _userUserMarketCapSubTaskRepository.AsQueryable()
                .Where(st => st.Id == input.SubTaskId && st.UId == curUid)
                .FirstAsync();

            if (subTask == null)
                throw Oops.Bah("任务不存在或不属于当前用户");

            if (subTask.Status == -1)
                throw Oops.Bah("已终止的任务无法操作");

            // 切换状态：运行(1) <-> 暂停(0)
            subTask.Status = subTask.Status == 1 ? 0 : 1;

            await _userUserMarketCapSubTaskRepository.AsUpdateable()
                .SetColumns(st => st.Status == subTask.Status)
                .SetColumns(st => st.CurrentAmountAll == 0)
                .Where(st => st.Id == subTask.Id)
                .ExecuteCommandAsync();

            // 发送状态更新消息到队列
            var statusMessage = new Dictionary<string, string>
            {
                ["uid"] = curUid.ToString(),
                ["taskId"] = subTask.Id.ToString(),
                ["Status"] = subTask.Status.ToString()
            };

            await _redisClient.XAddAsync(StreamKey.MarketCapActionStream, statusMessage);

            return true;
        }

        /// <summary>
        /// 清仓代币
        /// </summary>
        [HttpPost]
        [Route("/marketcap/clear")]
        public async Task<bool> ClearToken(ClearTokenInput input)
        {
            var curUid = (int)_userManager.UserId;

            // 参数验证
            if (string.IsNullOrEmpty(input.TokenAddress))
                throw Oops.Bah("请输入代币地址");

            if (input.Slippage <= 0 || input.Slippage > 100)
                throw Oops.Bah("滑点必须在1-100之间");


            var walletList = new List<string>();

            if (input.TaskId != null)
            {
                // 获取相关的子任务
                var subTasks = await _userUserMarketCapSubTaskRepository.AsQueryable()
                    .Where(st => st.Id == input.TaskId) // 排除已终止的任务
                    .FirstAsync();
                if (subTasks == null) throw Oops.Bah("任务不存在！");
                await _userUserMarketCapSubTaskRepository.AsUpdateable().SetColumns(s => s.Status == 0)
                    .Where(s => s.Id == input.TaskId).ExecuteCommandAsync();
                var walletEntity = await _userWalletRepository.AsQueryable()
                    .Where(s => subTasks.GroupId.Contains(s.GId)).Select(s => s.SecretKey).ToListAsync();
                walletList.AddRange(walletEntity);

                var statusMessage = new Dictionary<string, string>
                {
                    ["uid"] = curUid.ToString(),
                    ["taskId"] = subTasks.Id.ToString(),
                    ["Status"] = "0"
                };

                await _redisClient.XAddAsync(StreamKey.MarketCapActionStream, statusMessage);

            }

            if (input.TaskId == null)
            {
                await _userUserMarketCapSubTaskRepository.AsUpdateable().SetColumns(s => s.Status == 0)
                    .Where(s => s.UId == curUid && s.Status == 1 && s.TokenAddress == input.TokenAddress)
                    .ExecuteCommandAsync();
                var TaskEntity = await _userUserMarketCapSubTaskRepository.AsQueryable()
                    .Where(s => s.UId == curUid && s.TokenAddress == input.TokenAddress).Select(s => new { s.GroupId, s.Id })
                    .ToListAsync();
                foreach (var taskItem in TaskEntity)
                {
                    var statusMessage = new Dictionary<string, string>
                    {
                        ["uid"] = curUid.ToString(),
                        ["taskId"] = taskItem.Id.ToString(),
                        ["Status"] = "0"
                    };

                    await _redisClient.XAddAsync(StreamKey.MarketCapActionStream, statusMessage);
                }

                var gids = TaskEntity.SelectMany(s => s.GroupId).ToList();
                var walletEntity = await _userWalletRepository.AsQueryable().Where(s => gids.Contains(s.GId))
                    .Select(s => s.SecretKey).ToListAsync();
                walletList.AddRange(walletEntity);
            }

            // 发送清仓消息到队列
            var clearMessage = new Dictionary<string, string>
            {
                ["uid"] = curUid.ToString(),
                ["tokenAddress"] = input.TokenAddress,
                ["walletList"] = string.Join(",", walletList),
                ["slippage"] = input.Slippage.ToString(),
                ["gas"] = (input.Gas ?? 0).ToString(),
                ["isMev"] = input.IsMev.ToString(),
                ["mev"] = (input.Mev ?? 0).ToString(),
            };

            await _redisClient.XAddAsync(StreamKey.MarketCapClearTokenStream, clearMessage);

            return true;
        }

        /// <summary>
        /// 更新代币排序
        /// </summary>
        [HttpPost]
        [Route("/marketcap/updateorder")]
        public async Task<bool> UpdateTokenOrder(UpdateTokenOrderInput input)
        {
            var curUid = (int)_userManager.UserId;

            // 参数验证
            if (input.TokenOrders == null || !input.TokenOrders.Any())
                throw Oops.Bah("排序数据不能为空");

            // 验证所有任务都属于当前用户
            var taskIds = input.TokenOrders.Select(t => t.Id).ToList();
            var userTasks = await _userUserMarketCapTaskRepository.AsQueryable()
                .Where(t => t.UId == curUid && taskIds.Contains(t.Id))
                .ToListAsync();

            if (userTasks.Count != taskIds.Count)
                throw Oops.Bah("部分任务不存在或不属于当前用户");

            // 批量更新排序
            foreach (var orderItem in input.TokenOrders)
            {
                await _userUserMarketCapTaskRepository.AsUpdateable()
                    .SetColumns(t => t.OrderIndex == orderItem.OrderIndex)
                    .Where(t => t.Id == orderItem.Id && t.UId == curUid)
                    .ExecuteCommandAsync();
            }

            return true;
        }
        
        public async Task<bool> DeleteMarketCapTask(DeleteMarkCapInput input)
        {
            var curUid = (int)_userManager.UserId;

            // 验证任务是否存在
            var task = await _userUserMarketCapTaskRepository.AsQueryable()
                .Where(t => t.Id == id && t.UId == curUid)
                .FirstAsync();

            if (task == null)
                throw Oops.Bah("任务不存在或不属于当前用户");

            // 删除主任务
            await _userUserMarketCapTaskRepository.DeleteAsync(t => t.Id == id && t.UId == curUid);

            // 删除子任务
            await _userUserMarketCapSubTaskRepository.DeleteAsync(st => st.TaskId == id && st.UId == curUid);

            return true;
        }
    }
}