using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tron.Abp.Domain;

namespace Tron.Abp.Application.App.UserBatchTradeService.Dto
{
    public class UserBatchTradeInputDto
    {
        /// <summary>
        /// 链
        /// </summary>
        public string Chain { get; set; }

        /// <summary>
        /// 所属用户ID
        /// </summary>
        public int UId { get; set; }

        public string TokenAddress { get; set; }

        /// <summary>
        /// 是否购买
        /// </summary>
        public bool IsBuy { get; set; }

        /// <summary>
        /// 钱包分组ID
        /// </summary>
        public List<int> GroupId { get; set; }

        /// <summary>
        /// 钱包ID
        /// </summary>
        public int? WalletId { get; set; }

        /// <summary>
        /// 交易模式1高速，2防夹,3混合模式
        /// </summary>
        public int TradeModel { get; set; }

        /// <summary>
        /// 高速模式费用为空自动费用
        /// </summary>
        public decimal? Gas { get; set; } // 使用 decimal? 以支持 null  
        /// <summary>
        /// 是否开启防夹
        /// </summary>
        public bool IsMev { get; set; } = false;
        /// <summary>
        /// 防夹类型只有在IsMev开启时有效 1:0solt:2:nextblock
        /// </summary>
        [SugarColumn(ColumnDescription = "防夹类型", IsJson = true, IsNullable = true)]
        public List<int> MevType { get; set; } = new List<int>();
        /// <summary>
        /// 防夹模式用为空自动费用
        /// </summary>
        public decimal? Mev { get; set; } // 使用 decimal? 以支持 null  
        /// <summary>
        /// 滑点
        /// </summary>
        public int? Slippage { get; set; }

        /// <summary>
        /// 止盈配置
        /// </summary>
        [SugarColumn(ColumnDescription = "止盈配置", ColumnDataType = "text", IsJson = true, IsNullable = true)]
        public StopLimitModel StoplimitData { get; set; }

        /// <summary>
        ///  止盈止损过期自动卖出 
        /// </summary>
        public bool ExpiredAuto { get; set; }

        /// <summary>
        /// 过期时间（小时）
        /// </summary>
        public int? ExpiredTime { get; set; } = 120;

        /// <summary>
        /// 金额配置信息
        /// </summary>
        [SugarColumn(ColumnDescription = "金额配置信息", ColumnDataType = "text", IsJson = true, IsNullable = true)]
        public AmountModel AmountData { get; set; }

        /// <summary>
        /// 状态 0 已停止，1运行中,2已完成
        /// </summary>
        public int Status { get; set; } = 0;
    }

    public class UserBatchTradeOutputDto
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 链
        /// </summary>
        public string Chain { get; set; }

        /// <summary>
        /// 所属用户ID
        /// </summary>
        public int UId { get; set; }

        /// <summary>
        /// 代币名称
        /// </summary>
        public string TokenName { get; set; }
        /// <summary>
        /// 代币缩写
        /// </summary>
        public string TokenSymbol { get; set; }
        /// <summary>
        /// 代币地址
        /// </summary>
        public string TokenAddress { get; set; }
        /// <summary>
        /// 代币Icon
        /// </summary>
        public string TokenIcon { get; set; }

        /// <summary>
        /// 是否购买
        /// </summary>
        public bool IsBuy { get; set; }

        /// <summary>
        /// 钱包分组ID
        /// </summary>
        public List<int> GroupId { get; set; }

        /// <summary>
        /// 钱包分组ID
        /// </summary>
        public List<string> GroupName { get; set; }

        /// <summary>
        /// 钱包列表
        /// </summary>
        [SugarColumn(ColumnDescription = "钱包列表", ColumnDataType = "text", IsJson = true, IsNullable = true)]
        public List<WalletModel> Wallets { get; set; }

        /// <summary>
        /// 交易模式1高速，2防夹,3混合模式
        /// </summary>
        public int TradeModel { get; set; }

        /// <summary>
        /// 高速模式费用为空自动费用
        /// </summary>
        public decimal? Gas { get; set; } // 使用 decimal? 以支持 null  
        /// <summary>
        /// 防夹模式用为空自动费用
        /// </summary>
        public decimal? Mev { get; set; } // 使用 decimal? 以支持 null  
        /// <summary>
        /// 滑点
        /// </summary>
        public int? Slippage { get; set; }

        /// <summary>
        /// 止盈配置
        /// </summary>
        [SugarColumn(ColumnDescription = "止盈配置", ColumnDataType = "text", IsJson = true, IsNullable = true)]
        public StopLimitModel StoplimitData { get; set; }

        /// <summary>
        ///  止盈止损过期自动卖出 
        /// </summary>
        public bool ExpiredAuto { get; set; }

        /// <summary>
        /// 过期时间（小时）
        /// </summary>
        public int? ExpiredTime { get; set; } = 120;

        /// <summary>
        /// 金额配置信息
        /// </summary>
        [SugarColumn(ColumnDescription = "金额配置信息", ColumnDataType = "text", IsJson = true, IsNullable = true)]
        public AmountModel AmountData { get; set; }

        /// <summary>
        /// 状态 0 已停止，1运行中,2已完成
        /// </summary>
        public int Status { get; set; } = 1;

        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime? CompleteTime { get; set; }

        public string? Mark { get; set; }
        public DateTime? CoinCreateTime { get; set; }
    }

    public class WalletModel
    {
        public string Name { get; set; }
        /// <summary>
        /// 公钥
        /// </summary>
        public string PubKey { get; set; }
    }

    public class InputPageDto
    {
        /// <summary>
        /// 链
        /// </summary>
        public string Chain { get; set; }
        public bool? IsHidenFail { get; set; }

        public int PageSize { get; set; } = 10;
        public int PageIndex { get; set; } = 1;
    }

    public class UserBatchTradeLogOutput
    {

        public int Id { get; set; }
        public string LogId => Cryptography.Obfuscation.Factory.ObfuscatorFactory.NewInstance.Obfuscate(Id);
        /// <summary>
        /// 链
        /// </summary>
        public string Chain { get; set; }

        /// <summary>
        /// 代币名称
        /// </summary>
        public string TokenName { get; set; }

        /// <summary>
        /// 代币缩写
        /// </summary>
        public string TokenSymbol { get; set; }

        /// <summary>
        /// 代币地址
        /// </summary>
        public string TokenAddress { get; set; }

        /// <summary>
        /// 代币Icon
        /// </summary>
        public string TokenIcon { get; set; }
        /// <summary>
        /// 是否购买
        /// </summary>
        public bool IsBuy { get; set; }

        public decimal SolAmount { get; set; }
        public decimal TokenAmount { get; set; }
        public decimal Gas { get; set; }
        public decimal Mev { get; set; }
        public decimal TokenSolPrice { get; set; }
        /// <summary>
        /// 代币价格(USDT)
        /// </summary>
        public decimal TokenUsdtPrice { get; set; }
        public string MyWalletAddress { get; set; }
        public string? MyWalletMark { get; set; }
        /// <summary>
        /// 聪明钱包
        /// </summary>
        public string FollowWalletAddress { get; set; }
        /// <summary>
        /// 聪明钱包备注
        /// </summary>
        public string? FollowWalletMark { get; set; }
        public string? Mark { get; set; }
        public string? Signature { get; set; }
        public string Status { get; set; }
        public virtual DateTime? CreateTime { get; set; }

        public DateTime? CoinCreateTime { get; set; }
    }

    public class UserBatchTradeTaskListOutput
    {
        public int Id { get; set; }
        public string LogId { get; set; }
        public string Chain { get; set; }
        /// <summary>
        /// 代币名称
        /// </summary>
        public string TokenName { get; set; }
        /// <summary>
        /// 代币缩写
        /// </summary>
        public string TokenSymbol { get; set; }
        /// <summary>
        /// 代币地址
        /// </summary>
        public string TokenAddress { get; set; }
        /// <summary>
        /// 代币Icon
        /// </summary>
        public string TokenIcon { get; set; }
        /// <summary>
        /// 是否购买
        /// </summary>
        public bool IsBuy { get; set; }
        public string PubKey { get; set; }
        public int TradeMode { get; set; } = 1;
        public decimal Gas { get; set; }
        public decimal Mev { get; set; }
        /// <summary>
        /// 滑点
        /// </summary>
        public int Slippage { get; set; } = 10;
        public decimal BuyPrice { get; set; }
        public decimal HighestPrice { get; set; }
        /// <summary>
        /// 自动模式数据自动止盈/止损数据
        /// </summary>
        [SugarColumn(ColumnDescription = "自动模式数据自动止盈/止损数据", ColumnDataType = "text", IsJson = true, IsNullable = true)]
        public AutoModel AutoData { get; set; }

        /// <summary>
        /// 追踪模式数据
        /// </summary>
        [SugarColumn(ColumnDescription = "追踪模式数据", ColumnDataType = "text", IsJson = true, IsNullable = true)]
        public MoveModel MoveData { get; set; }
        public int Expired { get; set; }
        public DateTime ExpiredTime { get; set; }
        public string Status { get; set; }
        public bool IsRuning { get; set; }
        public string? Signature { get; set; }
        public DateTime? CreateTime { get; set; }
        public DateTime? CompleteTime { get; set; }
        /// <summary>
        /// 自动止盈/损失 1止盈，2止损
        /// </summary>
        public int? AutoMode { get; set; }
        public DateTime? CoinCreateTime { get; set; }
        
    }

    public class UserBatchTradeTaskInput
    {
        public string Chain { get; set; } = "all";
        public string Status { get; set; }
        public int PageSize { get; set; } = 10;
        public int PageIndex { get; set; } = 1;
    }

    public class UserBatchTradeTaskRunningInput
    {
        public string Ids { get; set; }
        public bool IsRun { get; set; }
    }

    public class UserBatchTradeTaskDelInput
    {
        public string Ids { get; set; }
    }
}
