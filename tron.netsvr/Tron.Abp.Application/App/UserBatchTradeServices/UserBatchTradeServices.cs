using FreeRedis;
using Jaina;
using LiteDB;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using NUglify.Helpers;
using SqlSugar;
using Tron.Abp.Application.App.UserBatchTradeService.Dto;
using Tron.Abp.Core;
using Tron.Abp.Domain;
using Tron.Abp.Multiplex.Contracts;
using Tron.Abp.Multiplex.Solana;
using Tron.Abp.SqlSugar;

namespace Tron.Abp.Application.App.QuickTradeServices;

[ApiExplorerSettings(GroupName = "app_v1")]
public class UserBatchTradeService : ApplicationService
{
    private IRedisClient _redisClient;
    private ISolanaApi _solanaApi;
    private IEventPublisher _eventPublisher;
    private readonly AppUserManager _userManager;

    private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;
    private readonly SqlSugarRepository<UserBatchTrade> _userBatchTradeRepository;
    private readonly SqlSugarRepository<UserBatchTradeTask> _userBatchTradeTaskRepository;
    private readonly SqlSugarRepository<UserWallet> _userWalletRepository;
    private readonly SqlSugarRepository<UserGroup> _userGroupRepository;
    public ILogger<UserBatchTradeService> Logger { get; set; }

    public UserBatchTradeService(AppUserManager userManager, IEventPublisher eventPublisher, ISolanaApi solanaApi,
        IRedisClient redisClient, SqlSugarRepository<UserAllTraderLog> userAllTraderLogRepository,
        SqlSugarRepository<UserWallet> userWalletRepository,
        SqlSugarRepository<UserBatchTrade> userBatchTradeRepository, SqlSugarRepository<UserGroup> userGroupRepository, SqlSugarRepository<UserBatchTradeTask> userBatchTradeTaskRepository)
    {
        _userManager = userManager;
        _eventPublisher = eventPublisher;
        _solanaApi = solanaApi;
        _redisClient = redisClient;
        _userAllTraderLogRepository = userAllTraderLogRepository;
        _userWalletRepository = userWalletRepository;
        _userBatchTradeRepository = userBatchTradeRepository;
        Logger = NullLogger<UserBatchTradeService>.Instance;
        _userGroupRepository = userGroupRepository;
        _userBatchTradeTaskRepository = userBatchTradeTaskRepository;
    }

    #region 批量交易

    /// <summary>
    /// 新增批量交易创建任务
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("/batchtrade/task/create")]
    public async Task<bool> BatchTradeTaskCreate(UserBatchTradeInputDto input)
    {
        var curUid = (int)_userManager.UserId;
        if (string.IsNullOrWhiteSpace(input.TokenAddress)) throw Oops.Bah("代币地址不能为空！");

        if (input.GroupId > 0 && !await _userGroupRepository.AsQueryable().AnyAsync(s => s.UId == curUid && s.Id == input.GroupId)) throw Oops.Bah("钱包分组不存在！");

        var Wallets = await _userWalletRepository.AsQueryable()
            .Where(s => s.UId == curUid && s.GId == input.GroupId)
            .OrderByDescending(it=>it.OrderIndex)
            .ToListAsync();
        if (Wallets.Count <= 0) throw Oops.Bah("选择的分组钱包数不足！");

        var tokenInfo = await _solanaApi.GetMint(input.TokenAddress);
        var Model = input.Adapt<UserBatchTrade>();
        Model.UId = curUid;
        Model.TokenName = tokenInfo?.Name ?? "-";
        Model.TokenSymbol = tokenInfo?.Symbol ?? "-";
        Model.TokenIcon = tokenInfo?.Icon ?? "-";
        Model.Status = 1;
        Model.CreateTime = DateTime.Now;
        Model.Wallets = Wallets.Select(s => new Domain.WalletModel() { WalletId = s.Id, Name = s.Name, PubKey = s.PubKey, SecretKey = s.SecretKey }).ToList();

        Model = await _userBatchTradeRepository.AsInsertable(Model).ExecuteReturnEntityAsync();
        if (Model.Id < 0) throw Oops.Bah("入库失败！");

        //TODO: 发送消息创建任务
        var dictPrice = new Dictionary<string, string>()
        {
            ["Id"] = $"{Model.Id}",
        };
        await _redisClient.XAddAsync(StreamKey.BatchTradeStream, dictPrice);
        return true;
    }


    /// <summary>
    /// 批量交易任务列表
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("/batchtrade/task/list")]
    public async Task<SqlSugarPagedList<UserBatchTradeOutputDto>> BatchTradeTaskList(InputPageDto input)
    {
        var curUid = (int)_userManager.UserId;

        var queryable = _userBatchTradeRepository.AsQueryable()
           .Where(s => s.UId == curUid)
           .WhereIF(input.Chain != "all" && !string.IsNullOrWhiteSpace(input.Chain), s => s.Chain == input.Chain)
           .OrderByDescending(s => s.CreateTime).Select<UserBatchTradeOutputDto>();

        var pageData = await queryable.ToPagedListAsync(input.PageIndex, input.PageSize);
        
        var mints = pageData.Items.Select(it => it.TokenAddress).Distinct().ToList();
        var mintCreateTimeDict=new Dictionary<string, DateTime>();
        using (var pipe=_redisClient.StartPipe())
        {
            foreach (var mint in mints)
            {
                var mintInfoval = pipe.HGet<Dictionary<string, JupAgStreamResult.PoolData>>(RedisKey.SolanaTokenInfo, mint);
            }
            var mintInfos=pipe.EndPipe();
            foreach (Dictionary<string, JupAgStreamResult.PoolData> o in mintInfos)
            {
                if(o==null) continue;
                var first=o.Values.OrderBy(it=>it.CreatedAt).First();
                mintCreateTimeDict[first.BaseAsset.Id] = first.CreatedAt;
            }
        }
        pageData.Items.ForEach(it =>
        {
            if (mintCreateTimeDict.ContainsKey(it.TokenAddress))
            {
                it.CoinCreateTime = mintCreateTimeDict[it.TokenAddress];
            }
            else
            {
                // 可选：为 CoinCreateTime 设置默认值，例如 DateTime.MinValue 或 null
                it.CoinCreateTime = default(DateTime); // 或者其他默认值
            }
        });
        return pageData;
    }

    /// <summary>
    /// 交易日志
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/batchtrade/task/logs")]
    public async Task<SqlSugarPagedList<UserBatchTradeLogOutput>> BatchTradeTaskLog(InputPageDto input)
    {
        var curUid = (int)_userManager.UserId;
        var queryable = _userAllTraderLogRepository.AsQueryable()
            .Where(it => it.UId == curUid && it.TraderType == TraderType.Trader_03)
            .WhereIF(input.IsHidenFail != null && input.IsHidenFail == true, it => SqlFunc.CharIndexNew(it.Status, "成功") > 0)
            .OrderByDescending(it => it.CreateTime);
        var pageData = await queryable.Select(it => new UserBatchTradeLogOutput()
        {
            Id = it.Id,
            Chain = it.Chain,
            TokenAddress = it.TokenAddress,
            TokenName = it.TokenName,
            TokenSymbol = it.TokenSymbol,
            TokenIcon = it.TokenIcon,
            IsBuy = it.IsBuy,
            Gas = it.Gas,
            Mev = it.Mev,
            SolAmount = it.SolAmount,
            Signature = it.Signature,
            Status = it.Status,
            TokenSolPrice = SqlFunc.ToDecimal(it.TokenSolPrice),
            FollowWalletAddress = it.FollowWalletAddress,
            FollowWalletMark = it.FollowWalletMark,
            MyWalletAddress = it.MyWalletAddress,
            MyWalletMark = it.FollowWalletMark,
            TokenAmount = it.TokenAmount,
            TokenUsdtPrice = SqlFunc.ToDecimal(it.TokenUsdtPrice),
            Mark = it.Mark,
            CreateTime = it.CreateTime,
        }).ToPagedListAsync(input.PageIndex, input.PageSize);

        var MyWalletAddress = pageData.Items.Select(s => s.MyWalletAddress).ToList().Distinct().ToArray();
        var myWallet = await _userWalletRepository.AsQueryable()
            .Where(s => s.UId == curUid && MyWalletAddress.Contains(s.PubKey))
            .OrderByDescending(it=>it.OrderIndex)
            .ToListAsync();

        
        var mints = pageData.Items.Select(it => it.TokenAddress).Distinct().ToList();
        var mintCreateTimeDict=new Dictionary<string, DateTime>();
        using (var pipe=_redisClient.StartPipe())
        {
            foreach (var mint in mints)
            {
                var mintInfoval = pipe.HGet<Dictionary<string, JupAgStreamResult.PoolData>>(RedisKey.SolanaTokenInfo, mint);
            }
            var mintInfos=pipe.EndPipe();
            foreach (Dictionary<string, JupAgStreamResult.PoolData> o in mintInfos)
            {
                if(o==null) continue;
                var first=o.Values.OrderBy(it=>it.CreatedAt).First();
                mintCreateTimeDict[first.BaseAsset.Id] = first.CreatedAt;
            }
        }
 
        
        pageData.Items.ForEach(it =>
        {
            it.MyWalletMark = myWallet.FirstOrDefault(s => s.PubKey == it.MyWalletAddress)?.Name ?? "";
            if (mintCreateTimeDict.ContainsKey(it.TokenAddress))
            {
                it.CoinCreateTime = mintCreateTimeDict[it.TokenAddress];
            }
            else
            {
                // 可选：为 CoinCreateTime 设置默认值，例如 DateTime.MinValue 或 null
                it.CoinCreateTime = default(DateTime); // 或者其他默认值
            }
        });

        return pageData;
    }


    /// <summary>
    /// 自动止盈止损列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/batchtrade/task/list/auto")]
    public async Task<SqlSugarPagedList<UserBatchTradeTaskListOutput>> BatchTradeTaskListByAuto(
        UserBatchTradeTaskInput input)
    {
        var curUid = (int)_userManager.UserId;
        var queryable = _userBatchTradeTaskRepository.AsQueryable()
            .WhereIF((!string.IsNullOrWhiteSpace(input.Chain) && input.Chain != "all"), it => it.Chain == input.Chain)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Status), it => it.Status == input.Status)
            .Where(it => it.UId == curUid && it.StopLimitMode == 1)
            .OrderByDescending(it => it.CreateTime).Select<UserBatchTradeTaskListOutput>();
        var pageData = await queryable.ToPagedListAsync(input.PageIndex, input.PageSize);
        
        
        var mints = pageData.Items.Select(it => it.TokenAddress).Distinct().ToList();
        var mintCreateTimeDict=new Dictionary<string, DateTime>();
        using (var pipe=_redisClient.StartPipe())
        {
            foreach (var mint in mints)
            {
                var mintInfoval = pipe.HGet<Dictionary<string, JupAgStreamResult.PoolData>>(RedisKey.SolanaTokenInfo, mint);
            }
            var mintInfos=pipe.EndPipe();
            foreach (Dictionary<string, JupAgStreamResult.PoolData> o in mintInfos)
            {
                if(o==null) continue;
                var first=o.Values.OrderBy(it=>it.CreatedAt).First();
                mintCreateTimeDict[first.BaseAsset.Id] = first.CreatedAt;
            }
        }
        pageData.Items.ForEach(it =>
        {
            if (mintCreateTimeDict.ContainsKey(it.TokenAddress))
            {
                it.CoinCreateTime = mintCreateTimeDict[it.TokenAddress];
            }
            else
            {
                // 可选：为 CoinCreateTime 设置默认值，例如 DateTime.MinValue 或 null
                it.CoinCreateTime = default(DateTime); // 或者其他默认值
            }
        });
        
        return pageData;
    }

    /// <summary>
    ///   追踪止盈止损
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/batchtrade/task/list/move")]
    public async Task<SqlSugarPagedList<UserBatchTradeTaskListOutput>> BatchTradeTaskListByMove(
        UserBatchTradeTaskInput input)
    {
        var curUid = (int)_userManager.UserId;

        var queryable = _userBatchTradeTaskRepository.AsQueryable()
            .WhereIF((!string.IsNullOrWhiteSpace(input.Chain) && input.Chain != "all"), it => it.Chain == input.Chain)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Status), it => it.Status == input.Status)
            .Where(it => it.UId == curUid && it.StopLimitMode == 2)
            .OrderByDescending(it => it.CreateTime).Select<UserBatchTradeTaskListOutput>();
        var pageData = await queryable.ToPagedListAsync(input.PageIndex, input.PageSize);
       
        var mints = pageData.Items.Select(it => it.TokenAddress).Distinct().ToList();
        var mintCreateTimeDict=new Dictionary<string, DateTime>();
        using (var pipe=_redisClient.StartPipe())
        {
            foreach (var mint in mints)
            {
                var mintInfoval = pipe.HGet<Dictionary<string, JupAgStreamResult.PoolData>>(RedisKey.SolanaTokenInfo, mint);
            }
            var mintInfos=pipe.EndPipe();
            foreach (Dictionary<string, JupAgStreamResult.PoolData> o in mintInfos)
            {
                if(o==null) continue;
                var first=o.Values.OrderBy(it=>it.CreatedAt).First();
                mintCreateTimeDict[first.BaseAsset.Id] = first.CreatedAt;
            }
        }
        pageData.Items.ForEach(it =>
        {
            if (mintCreateTimeDict.ContainsKey(it.TokenAddress))
            {
                it.CoinCreateTime = mintCreateTimeDict[it.TokenAddress];
            }
            else
            {
                // 可选：为 CoinCreateTime 设置默认值，例如 DateTime.MinValue 或 null
                it.CoinCreateTime = default(DateTime); // 或者其他默认值
            }
        });
        return pageData;
    }

    /// <summary>
    /// 设置任务是否运行
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/batchtrade/task/running")]
    public async Task<bool> BatchTradeTaskRunning(UserBatchTradeTaskRunningInput input)
    {
        var curUid = (int)_userManager.UserId;

        var ids = input.Ids.Split(',').Select(s => Convert.ToInt32(s)).ToArray();
        if (ids.Length <= 0) throw Oops.Bah("请选择要操作的任务");

        var update = await _userBatchTradeTaskRepository.AsUpdateable()
            .SetColumns(it => it.IsRuning == input.IsRun)
            .Where(it => ids.Contains(it.Id) && it.UId == curUid)
            .ExecuteCommandAsync();

        //if (!input.IsRun)
        //{
        //    var model = await _userBatchTradeTaskRepository.AsQueryable().FirstAsync(s => s.Id == ids[0]);
        //    var dictPrice = new Dictionary<string, string>()
        //    {
        //        ["mint"] = model.TokenAddress,
        //        ["type"] = "0",
        //    };
        //    ////是否是pump
        //    //var dexType = (await _solanaApi.GetMintDexType(model.TokenAddress));
        //    //var streamKey = dexType switch
        //    //{
        //    //    DexType.RaydiumAmm => StreamKey.EnhancedWsRaydiumStream,
        //    //    DexType.Pumpfun => StreamKey.EnhancedWsPumpFunStream,
        //    //    //DexType.PumpSwapAmm => "mint",
        //    //};
        //    ////发送消息
        //    //await _redisClient.XAddAsync(streamKey, dictPrice);
        //}
        //else
        //{
        //    var model = await _userBatchTradeTaskRepository.AsQueryable().FirstAsync(s => s.Id == ids[0]);
        //    //var dictPrice = new Dictionary<string, string>()
        //    //{
        //    //    ["mint"] = model.TokenAddress,
        //    //    ["type"] = "1",
        //    //};
        //    //var dexType = (await _solanaApi.GetMintDexType(model.TokenAddress));
        //    //var streamKey = dexType switch
        //    //{
        //    //    DexType.RaydiumAmm => StreamKey.EnhancedWsRaydiumStream,
        //    //    DexType.Pumpfun => StreamKey.EnhancedWsPumpFunStream,
        //    //    //DexType.PumpSwapAmm => "mint",
        //    //};
        //    ////发送消息
        //    //await _redisClient.XAddAsync(streamKey, dictPrice);
        //}

        var batchTrade = await _userBatchTradeTaskRepository.AsQueryable().Where(s => ids.Contains(s.Id)).ToListAsync();
        var batchTradeDict = batchTrade.Select(s => s.LogId).Distinct().ToList();

        foreach (var item in batchTradeDict)
        {
            var entiy = batchTrade.Where(s => s.LogId == item).FirstOrDefault();
            if (entiy == null) continue;

            var tokenInfo = await _solanaApi.GetMint(entiy.TokenAddress);
            var dexType = tokenInfo.Type;
            var poolId = await _solanaApi.GetMintPoolId(entiy.TokenAddress);

            var dict = new Dictionary<string, string>()
            {
                ["Uid"] = curUid.ToString(),
                ["LogId"] = item,
                ["DexType"] = dexType.ToString(),
                ["Mint"] = entiy.TokenAddress,
                ["PoolId"] = poolId,
                ["Status"] = input.IsRun ? "1" : "2",
            };
            //发送消息
            await _redisClient.XAddAsync(StreamKey.BathchTradeSubTaskStream, dict);
        }


        return true;
    }

    /// <summary>
    /// 删除任务
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/batchtrade/task/del")]
    public async Task<bool> BatchTradeTaskDel(UserBatchTradeTaskDelInput input)
    {
        var curUid = (int)_userManager.UserId;

        var ids = input.Ids.Split(',').Select(s => Convert.ToInt32(s)).ToArray();
        if (ids.Length <= 0) throw Oops.Bah("请选择要操作的任务");

        var batchTrade = await _userBatchTradeTaskRepository.AsQueryable().Where(s => ids.Contains(s.Id)).ToListAsync();
        var batchTradeDict = batchTrade.Select(s => s.LogId).Distinct().ToList();

        foreach (var item in batchTradeDict)
        {
            var entiy = batchTrade.Where(s => s.LogId == item).FirstOrDefault();
            if (entiy == null) continue;

            var tokenInfo = await _solanaApi.GetMint(entiy.TokenAddress);
            var dexType = tokenInfo.Type;
            var poolId = await _solanaApi.GetMintPoolId(entiy.TokenAddress);

            var dict = new Dictionary<string, string>()
            {
                ["Uid"] = curUid.ToString(),
                ["LogId"] = item,
                ["DexType"] = dexType.ToString(),
                ["Mint"] = entiy.TokenAddress,
                ["PoolId"] = poolId,
                ["Status"] = "3",
            };
            //发送消息
            await _redisClient.XAddAsync(StreamKey.BathchTradeSubTaskStream, dict);
        }


        var update = await _userBatchTradeTaskRepository.AsDeleteable()
            .Where(it => ids.Contains(it.Id) && it.UId == curUid)
            .ExecuteCommandAsync();


        return true;
    }


    #endregion
    
    
    //批量卖出
    public async Task<bool> BatchSell(UserBatchSellInput input)
    {
        var curUid = (int)_userManager.UserId;
        return true;
    }
    
    
}