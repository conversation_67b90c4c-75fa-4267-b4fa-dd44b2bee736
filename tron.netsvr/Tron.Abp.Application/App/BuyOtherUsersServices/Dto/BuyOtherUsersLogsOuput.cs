namespace Tron.Abp.Application.App.BuyOtherUsersServices.Dto;

public class BuyOtherUsersLogsInput
{
    /// <summary>
    /// 链
    /// </summary>
    public string Chain { get; set; }
    public bool? IsHidenFail { get; set; }

    public int PageSize { get; set; } = 10;
    public int PageIndex { get; set; } = 1;
}

public class BuyOtherUsersLogsOuput
{
    
    public int Id { get; set; }
    public string LogId => Cryptography.Obfuscation.Factory.ObfuscatorFactory.NewInstance.Obfuscate(Id);
    /// <summary>
    /// 链
    /// </summary>
    public string Chain { get; set; }

    /// <summary>
    /// 代币名称
    /// </summary>
    public string TokenName { get; set; }

    /// <summary>
    /// 代币缩写
    /// </summary>
    public string TokenSymbol { get; set; }

    /// <summary>
    /// 代币地址
    /// </summary>
    public string TokenAddress { get; set; }

    /// <summary>
    /// 代币Icon
    /// </summary>
    public string TokenIcon { get; set; }
    /// <summary>
    /// 是否购买
    /// </summary>
    public bool IsBuy { get; set; }

    public decimal SolAmount { get; set; }
    public decimal TokenAmount { get; set; }
    public decimal Gas { get; set; }
    public decimal Mev { get; set; }
    public decimal TokenSolPrice { get; set; }
    /// <summary>
    /// 代币价格(USDT)
    /// </summary>
    public decimal TokenUsdtPrice { get; set; }
    public string MyWalletAddress { get; set; }
    public string? MyWalletMark { get; set; }
    /// <summary>
    /// 聪明钱包
    /// </summary>
    public string FollowWalletAddress { get; set; }
    /// <summary>
    /// 聪明钱包备注
    /// </summary>
    public string? FollowWalletMark { get; set; }
    public string? Mark { get; set; }
    public string? Signature { get; set; }
    public string Status { get; set; }
    public virtual DateTime? CreateTime { get; set; }

    public DateTime? CoinCreateTime { get; set; }
}