using DeviceDetectorNET.Class.Device;
using FreeRedis;
using Jaina;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using NUglify.Helpers;
using Solnet.Wallet;
using SqlSugar;
using System.Drawing;
using System.Text;
using System.Text.Json;
using Tron.Abp.Application.App.UserServices.Dto;
using Tron.Abp.Application.AppEventHandlers.Dto;
using Tron.Abp.Core;
using Tron.Abp.Domain;
using Tron.Abp.Domain.Contracts.Enum;
using Tron.Abp.Multiplex.Contracts;
using Tron.Abp.Multiplex.Solana;
using Tron.Abp.SqlSugar;
using Volo.Abp;
using static Tron.Abp.Application.App.UserServices.Dto.TokenCreateInput;

namespace Tron.Abp.Application.App.UserServices;

[ApiExplorerSettings(GroupName = "app_v1")]
public class UserService : ApplicationService
{
    private IRedisClient _redisClient;
    private readonly SqlSugarRepository<User> _userRepository;

    private readonly SqlSugarRepository<UserWallet> _userWalletRepository;

    //private readonly SqlSugarRepository<UserTag> _userTagRepository;
    private readonly SqlSugarRepository<UserGroup> _userGroupRepository;

    private readonly SqlSugarRepository<UserTransferLog> _userTransferLogRepository;

    //private readonly SqlSugarRepository<UserTradeLog> _userUserTradeLogRepository;
    private readonly SqlSugarRepository<UserTransferTask> _userTransferTaskRepository;
    private readonly SqlSugarRepository<UserTokens> _userTokensRepository;
    private readonly SqlSugarRepository<UserCoinsBase> _userCoinsBaseRepository;
    private readonly SqlSugarRepository<UserCoins> _userCoinsRepository;
    private readonly SqlSugarRepository<UserTokenInventory> _userTokenInventoryRepository;
    private readonly AppUserManager _userManager;
    private readonly SqlSugarRepository<UserSmartTrade> _userSmartTradeRepository;
    private readonly SqlSugarRepository<UserConfig> _userConfigRepository;
    private readonly SqlSugarRepository<UserMyWallet> _userMyWalletRepository;
    private readonly SqlSugarRepository<UserBatchTrade> _userBatchTradeRepository;
    private ISolanaApi _solanaApi;
    private IEventPublisher _eventPublisher;
    private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;

    public UserService(IRedisClient redisClient, SqlSugarRepository<User> userRepository,
        SqlSugarRepository<UserWallet> userWalletRepository, AppUserManager userManager,
        //SqlSugarRepository<UserTag> userTagRepository,
        SqlSugarRepository<UserGroup> userGroupRepository,
        ISolanaApi solanaApi, IEventPublisher eventPublisher,
        SqlSugarRepository<UserTransferLog> userTransferLogRepository,
        SqlSugarRepository<UserTransferTask> userTransferTaskRepository,
        //SqlSugarRepository<UserTradeLog> userUserTradeLogRepository,
        SqlSugarRepository<UserTokens> userTokensRepository, SqlSugarRepository<UserCoinsBase> userCoinsBaseRepository,
        SqlSugarRepository<UserCoins> userCoinsRepository,
        SqlSugarRepository<UserTokenInventory> userTokenInventoryRepository,
        SqlSugarRepository<UserSmartTrade> userSmartTradeRepository,
        SqlSugarRepository<UserConfig> userConfigRepository, SqlSugarRepository<UserMyWallet> userMyWalletRepository,
        SqlSugarRepository<UserAllTraderLog> userAllTraderLogRepository,
        SqlSugarRepository<UserBatchTrade> userBatchTradeRepository)
    {
        _redisClient = redisClient;
        _userRepository = userRepository;
        _userWalletRepository = userWalletRepository;
        _userManager = userManager;
        //_userTagRepository = userTagRepository;
        _userGroupRepository = userGroupRepository;
        _solanaApi = solanaApi;
        _eventPublisher = eventPublisher;
        _userTransferLogRepository = userTransferLogRepository;
        _userTransferTaskRepository = userTransferTaskRepository;
        //_userUserTradeLogRepository = userUserTradeLogRepository;
        _userTokensRepository = userTokensRepository;
        _userCoinsBaseRepository = userCoinsBaseRepository;
        _userCoinsRepository = userCoinsRepository;
        _userTokenInventoryRepository = userTokenInventoryRepository;
        _userSmartTradeRepository = userSmartTradeRepository;
        _userConfigRepository = userConfigRepository;
        _userMyWalletRepository = userMyWalletRepository;
        _userAllTraderLogRepository = userAllTraderLogRepository;
        _userBatchTradeRepository = userBatchTradeRepository;
    }

    #region 钱包

    /// <summary>
    /// 获取
    /// </summary>
    [HttpPost]
    [Route("/user/getwallets")]
    public async Task<List<UserWalletOutput>> GetUserWalletsAsync(UserWalletGetInput input)
    {
        var list = new List<UserWalletOutput>();
        var uw = await _userWalletRepository.AsQueryable()
            .Where(it => it.UId == _userManager.UserId)
            .WhereIF(input.Gid > -1, it => it.GId == input.Gid)
            .OrderBy(it => it.OrderIndex)
            //.WhereIF(input.TId > 0, it => SqlFunc.JsonArrayAny(it.TId, input.TId))
            .OrderBy(it => it.CreateTime)
            .ToListAsync();
        list = uw.Adapt<List<UserWalletOutput>>();

        if (input.IsShowSol == true)
        {
            var address = list.Select(it => it.PubKey).ToList();

            var total = address.Count / 100 + (address.Count % 100 > 0 ? 1 : 0);
            var dict = new Dictionary<string, double>();
            for (int ii = 0; ii < total; ii++)
            {
                var userAddress = address.Skip(ii * 100).Take(100).ToList();
                var balances = await _solanaApi.GetMultipleAccountsBalanceAsync(userAddress);

                foreach (var balance in balances)
                {
                    dict[balance.Key] = balance.Value;
                }
            }


            for (int i = 0; i < list.Count; i++)
            {
                var item = list[i];
                if (dict.ContainsKey(item.PubKey))
                {
                    item.Sol = Convert.ToDecimal(dict[item.PubKey]);
                }
            }
        }

        return list;
    }

    /// <summary>
    /// 创建钱包
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/user/createwallet")]
    public async Task<List<UserWalletOutput>> CreateUserWalletsAsync(UserWalletCreateInput input)
    {
        if (input.Num == 0) input.Num = 10;
        var curId = (int)_userManager.UserId;
        var groupName = await _userGroupRepository.AsQueryable()
            .Where(it => it.Id == input.Gid && it.UId == curId)
            .FirstAsync();
        //var tagName = await _userTagRepository.AsQueryable()
        //    .Where(it => it.Id == input.TId && (it.UId == curId || it.UId == 0))
        //    .ToListAsync();
        var list = Enumerable.Range(0, input.Num).Select(it =>
            {
                var wallet = new Solnet.Wallet.Account();
                return
                    new UserWallet()
                    {
                        UId = curId,
                        GId = groupName?.Id ?? 0,
                        GName = groupName?.Name ?? "",
                        //TId = tagName.Select(it => it.Id).ToList(),
                        //TName = tagName.Select(it => it.Name).ToList(),
                        PubKey = wallet.PublicKey,
                        SecretKey = wallet.PrivateKey,
                        CreateTime = DateTime.Now,
                    };
            }
        ).ToList();

        await _userWalletRepository.AsInsertable(list).ExecuteReturnIdentityAsync();

        var wallets = list.Select(s => s.PubKey).ToArray();
        var resultList = await _userWalletRepository.AsQueryable().Where(s => wallets.Contains(s.PubKey))
            .OrderByDescending(it => it.OrderIndex).ToListAsync();

        var result = resultList.Adapt<List<UserWalletOutput>>();

        return result;
    }

    /// <summary>
    /// 指量修改分组
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/user/bachwalletgroups")]
    public async Task<bool> BachWalletsGroupsAsync(BachWalletGroups input)
    {
        var curId = (int)_userManager.UserId;
        var group = await _userGroupRepository.AsQueryable()
            .FirstAsync(s => s.Id == input.GId && s.UId == curId);

        var gId = group?.Id ?? 0;
        var gName = group?.Name ?? "";
        var isUpdate = await _userWalletRepository.AsUpdateable()
            .SetColumns(s => s.GId == gId)
            .SetColumns(s => s.GName == gName)
            .Where(s => s.UId == curId && input.WalletId.Contains(s.Id) && s.GId != gId).ExecuteCommandAsync();
        return isUpdate > 0;
    }

    ///// <summary>
    ///// 批量修改标签
    ///// </summary>
    ///// <param name="input"></param>
    ///// <returns></returns>
    //[HttpPost]
    //[Route("/user/bachwallettags")]
    //public async Task<bool> BachWalletstagsAsync(BachWalletTags input)
    //{
    //    var curId = (int)_userManager.UserId;
    //    var tag = await _userTagRepository.AsQueryable()
    //        .FirstAsync(s => s.Id == input.TId && (s.UId == curId || s.UId == 0));

    //    var wallets = await _userWalletRepository.AsQueryable()
    //        .Where(s => s.UId == curId && input.WalletId.Contains(s.Id)).ToListAsync();
    //    wallets.ForEach(it =>
    //    {
    //        if (!it.TId.Contains(tag.Id))
    //        {
    //            it.TId.Add(tag.Id);
    //            it.TName.Add(tag.Name);
    //        }
    //    });

    //    var isUpdate = await _userWalletRepository.AsUpdateable(wallets).UpdateColumns(s => new { s.TId, s.TName })
    //        .ExecuteCommandAsync();
    //    return isUpdate > 0;
    //}

    ///// <summary>
    ///// 删除标签
    ///// </summary>
    ///// <param name="input"></param>
    ///// <returns></returns>
    //[HttpPost]
    //[Route("/user/deleteWalletTag")]
    //public async Task<bool> deleteWalletTagAsync(DeleteWalletTags input)
    //{
    //    var curId = (int)_userManager.UserId;
    //    var tag = await _userTagRepository.AsQueryable()
    //        .FirstAsync(s => s.Id == input.TId && (s.UId == curId || s.UId == 0));

    //    var wallets = await _userWalletRepository.AsQueryable().Where(s => s.UId == curId && s.Id == input.WalletId)
    //        .FirstAsync();
    //    wallets.TId.Remove(tag.Id);
    //    wallets.TName.Remove(tag.Name);

    //    var isUpdate = await _userWalletRepository.AsUpdateable(wallets).UpdateColumns(s => new { s.TId, s.TName })
    //        .ExecuteCommandAsync();
    //    return isUpdate > 0;
    //}

    /// <summary>
    /// 获取余额
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [Route("/user/getBalance")]
    public async Task<decimal> getBalanceAsync(WalletInput input)
    {
        try
        {
            decimal sol = await _solanaApi.GetBalanceAsync(input.PublicKey);

            return Convert.ToDecimal(sol);
        }
        catch (Exception ex)
        {
            throw Oops.Bah("分组不存在！");
        }
    }

    /// <summary>
    /// 获取持仓
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [Route("/user/GetTokenBalance")]
    public async Task<List<TokenBalanceOutput>> GetTokenBalance(TokenBalanceInput input)
    {
        if (string.IsNullOrWhiteSpace(input.PubKey)) throw Oops.Bah("请输入公钥！");
        if (!Solnet.Wallet.PublicKey.IsValid(input.PubKey)) throw Oops.Bah("输入的公钥不正确！");
        var outList = new List<TokenBalanceOutput>();
        var curId = (int)_userManager.UserId;

        var tokens = await _solanaApi.GetTokenAccountByOwnerAsync(input.PubKey);

        var queryTokens = tokens.Where(s =>
            s.Account.Data.Parsed.Info.Mint != "So11111111111111111111111111111111111111112" &&
            s.Account.Data.Parsed.Info.TokenAmount.AmountUlong > 0).ToList();
        var mints = queryTokens.Select(s => s.Account.Data.Parsed.Info.Mint).ToArray();
        if (mints.Length <= 0) return outList;
        var solUsdtPrice = await _redisClient.GetAsync<decimal>(RedisKey.SolanaWSOLPrice);
        var tokenInfos = await _solanaApi.GetMints(mints);
        foreach (var item in queryTokens)
        {
            var tokenInfo = tokenInfos.FirstOrDefault(s => s.Mint == item.Account.Data.Parsed.Info.Mint);
            var tbo = new TokenBalanceOutput()
            {
                Amount = item.Account.Data.Parsed.Info.TokenAmount.AmountDecimal,
                Name = tokenInfo?.Name ?? "",
                Symbol = tokenInfo?.Symbol ?? "",
                Icon = tokenInfo?.Icon ?? "",
                Mint = item.Account.Data.Parsed.Info.Mint,
            };
            //更新持仓
            outList.Add(tbo);
        }


        //var list = await _solanaApi.GetAccountsAsync(input.PubKey);
        ////list[0].Account.Data.Parsed.Info.TokenAmount.AmountDecimal;
        //var mints = list.Select(it => it.Account.Data.Parsed.Info.Mint).ToList();
        //var mintInfos = await _solanaApi.GetMultipleMint(mints);
        //var dictMintInfo = mintInfos.ToDictionary(it => it.Mint, it => it);
        //for (int i = 0; i < list.Count; i++)
        //{
        //    var item = list[i];
        //    var mint = item.Account.Data.Parsed.Info.Mint;
        //    TokenInfo tokenInfo = dictMintInfo[mint];
        //    // var isMintCahce=await _redisClient.HExistsAsync("mint", mint);
        //    //
        //    // if (isMintCahce)
        //    // {
        //    //     var tokenInfoStr= await _redisClient.HGetAsync("mint", mint);
        //    //     tokenInfo=JsonSerializer.Deserialize<TokenInfo>(tokenInfoStr);
        //    // }
        //    // else
        //    // {
        //    //      tokenInfo = await _solanaApi.GetMint(mint);
        //    //      await _redisClient.HSetAsync("mint", mint, tokenInfo);
        //    // }
        //    var tbo = new TokenBalanceOutput()
        //    {
        //        Amount = item.Account.Data.Parsed.Info.TokenAmount.AmountDecimal,
        //        Name = tokenInfo?.Name,
        //        Symbol = tokenInfo?.Symbol,
        //        Icon = tokenInfo?.Icon,
        //        Mint = mint,
        //    };
        //    //更新持仓
        //    outList.Add(tbo);
        //}


        return outList;
    }


    private (int GroupCount, List<int> WalletsPerGroup) DistributeWallets(int totalWallets, int? groupCount = null)
    {
        if (totalWallets < 0) throw new ArgumentException("Total wallets cannot be negative.");

        // 确定分组数
        int groups;
        if (groupCount.HasValue)
        {
            groups = groupCount.Value;
            if (groups < 1 || totalWallets / groups > 10)
                throw new ArgumentException("Invalid group count or exceeds 10 wallets per group.");
        }
        else
        {
            // 动态选择：优先完全均分，否则最小方差，满足每组≤10
            groups = Math.Max(1, (int)Math.Ceiling(totalWallets / 10.0));
            double minVariance = double.MaxValue;
            for (int k = groups; k <= totalWallets && k <= Math.Ceiling(Math.Sqrt(totalWallets)) + 1; k++)
            {
                if (totalWallets / k > 10) continue;
                if (totalWallets % k == 0)
                {
                    groups = k; // 优先完全均分
                    break;
                }

                int remainder = totalWallets % 10;
                int fullGroups = totalWallets / 10;
                if (k < fullGroups || remainder > 10 || (k == fullGroups && remainder == 0)) continue;
                double variance = ((k - 1) * 100.0 + remainder * remainder) / k - Math.Pow(totalWallets / (double)k, 2);
                if (variance < minVariance)
                {
                    minVariance = variance;
                    groups = k;
                }
            }
        }

        // 分配钱包
        var result = new List<int>();
        if (totalWallets % groups == 0)
        {
            int groupSize = totalWallets / groups;
            for (int i = 0; i < groups; i++) result.Add(groupSize);
        }
        else
        {
            int fullGroups = totalWallets / 10;
            int remainder = totalWallets % 10;
            int neededGroups = fullGroups + (remainder > 0 ? 1 : 0);
            if (groups < neededGroups)
                throw new ArgumentException("Group count too small for max 10 wallets per group.");
            for (int i = 0; i < fullGroups && i < groups - 1; i++)
                result.Add(10);
            if (remainder > 0 && result.Count < groups)
                result.Add(remainder);
            // 填充剩余组（如果分组数多于neededGroups）
            while (result.Count < groups)
                result.Add(0);
        }

        return (groups, result);
    }

    /// <summary>
    /// 混淆 钱包 重新 组合
    /// </summary>
    [HttpPost]
    [Route("/user/reintegrationwallet")]
    public async Task<bool> ReintegrationWallet(ReintegrationInput input)
    {
        #region 内部方法

        Random random = new Random(Guid.NewGuid().GetHashCode());

        //Fisher-Yates 洗牌算法
        List<T> GetRandomElementsOptimized<T>(List<T> list, int count)
        {
            if (count > list.Count)
                throw new ArgumentException("count 不能大于列表元素的数量");

            List<T> copy = new List<T>(list);
            for (int i = 0; i < count; i++)
            {
                int randomIndex = random.Next(i, copy.Count);
                (copy[i], copy[randomIndex]) = (copy[randomIndex], copy[i]);
            }

            return copy.Take(count).ToList();
        }

        #endregion

        var curUid = (int)_userManager.UserId;

        if (input.selectedGroups == null || input.selectedGroups.Count <= 0) throw Oops.Bah("请选择分组!");
        if (input.operationType == 0 || input.operationType == 1)
        {
            if (string.IsNullOrWhiteSpace(input.targetGroup)) throw Oops.Bah("分组名称不能为空");
            var isAny = await _userGroupRepository.AsQueryable()
            .Where(it => it.Name == input.targetGroup && it.UId == curUid)
            .AnyAsync();
            if (isAny) throw Oops.Bah("分组名称已经存在");
        }

        //混淆
        if (input.operationType == 0)
        {
            if (input.count <= 0) throw Oops.Bah("请输入目标数量!");
            var groupIds = input.selectedGroups.Distinct().ToList();
            var sourceTagQueryable = _userWalletRepository.AsQueryable();
            var expable = Expressionable.Create<UserWallet>();
            foreach (var gId in groupIds)
            {
                expable.Or(it => it.GId == gId);
            }

            var exp = expable.ToExpression();
            var sourceTags = await sourceTagQueryable.Where(exp).Where(it => it.UId == curUid)
                .OrderByDescending(it => it.OrderIndex).ToListAsync();
            if (sourceTags.Count < input.count) throw Oops.Bah("选的分组钱包数小于目标数量!");

            //sourceTags=sourceTags.GroupBy(s => s.PubKey).Select(s => s.FirstOrDefault()).ToList();

            var toTagsTmp = new List<UserWallet>();
            // 计算每个列表的初始抽取数量
            int baseCount = input.count.Value / groupIds.Count;
            int remaining = input.count.Value % groupIds.Count;

            foreach (var gid in groupIds)
            {
                var list = sourceTags
                    .Where(it => it.GId == gid && !toTagsTmp.Select(s => s.PubKey).ToList().Contains(it.PubKey))
                    .ToList();
                int countToTake = Math.Min(baseCount + (remaining > 0 ? 1 : 0), list.Count);
                toTagsTmp.AddRange(list.OrderBy(it => Guid.NewGuid().GetHashCode()).Take(countToTake).ToList());
            }

            var toTags = GetRandomElementsOptimized(toTagsTmp, input.count.Value);

            //新建分组
            var groupEndity = await _userGroupRepository
                .AsInsertable(new UserGroup() { UId = curUid, Name = input.targetGroup }).ExecuteReturnEntityAsync();
            toTags.ForEach(it =>
            {
                it.GId = groupEndity.Id;
                it.GName = groupEndity.Name;
            });

            await _userWalletRepository.AsUpdateable(toTags).UpdateColumns(s => new { s.GId, s.GName })
                .ExecuteCommandAsync();
        }
        //合并
        else if (input.operationType == 1)
        {
            //选择1-多个分组
            //合并到一个分组
            //1.查询出所有分组的钱包

            var list = await _userWalletRepository.AsQueryable()
               .Where(it => it.UId == curUid && input.selectedGroups.Contains(it.GId)).ToListAsync();
            if (list == null || list.Count <= 0) throw Oops.Bah("请选择有效的分组合并");

            var newGroup = new UserGroup()
            {
                Name = input.targetGroup,
                UId = curUid
            };
            newGroup.Id = await _userGroupRepository.AsInsertable(newGroup).ExecuteReturnIdentityAsync();
            //2.更新分组  并重新排序


            //var ids = list.Select(it => it.Id).ToList();
            //var orderIndexDict = list.ToDictionary(it => it.Id, it => random.Next(0, ids.Count));
            list.ForEach(it =>
            {
                //it.OrderIndex = orderIndexDict[it.Id];
                it.GId = newGroup.Id;
                it.GName = newGroup.Name;
            });
            await _userWalletRepository.AsUpdateable(list)
                .UpdateColumns(s => new { s.GId, s.GName })
                /*.SetColumns(it=>it.GId==newGroup.Id)
                .SetColumns(it=>it.GName==newGroup.Name)
                .Where(it=>ids.Contains(it.Id))*/
                .ExecuteCommandAsync();
            if (input.isDelete == true)
            {
                await _userGroupRepository.AsDeleteable().Where(s => input.selectedGroups.Contains(s.Id)).ExecuteCommandAsync();
            }
        }
        //分割
        else if (input.operationType == 2)
        {
            //分割分组：把一个分组的钱包分割到多个分组里 数量等分 如果是97 分成 10个分组最后一个分组7个钱包
            if (input.count <= 0) throw Oops.Bah("请输入分割分组数量!");

            var list = await _userWalletRepository.AsQueryable()
                .Where(it => it.UId == curUid && input.selectedGroups.Contains(it.GId)).OrderBy(s => s.OrderIndex).ToListAsync();
            var ids = list.Select(it => it.Id).ToList();

            if (input.count > ids.Count) throw Oops.Bah("请输入分割分组数量不能大于分组钱包数量!");

            var groupWalletNum = input.count.Value;
            var loopCount = ids.Count / groupWalletNum;
            loopCount += ids.Count % groupWalletNum > 0 ? 1 : 0;
            int index = 0;
            var dict = new Dictionary<int, List<int>>();

            for (int i = 0; i < loopCount; i++)
            {
                dict[i] = (ids.Skip(index).Take(groupWalletNum).ToList());
                index += groupWalletNum;
            }

            //if (remainder > 0 && dict.Keys.Count < neededGroups)
            //{
            //    dict[neededGroups] = (ids.Skip(index).Take(remainder).ToList());
            //    index += remainder;
            //}

            Func<List<int>, Task<bool>> UpDataId = async (fids) =>
            {
                var newGroup = new UserGroup()
                {
                    Name = "分组",
                    UId = curUid
                };
                newGroup.Id = await _userGroupRepository.AsInsertable(newGroup).ExecuteReturnIdentityAsync();
                newGroup.Name += $"{newGroup.Id}";
                await _userGroupRepository.AsUpdateable().SetColumns(s => s.Name == newGroup.Name).Where(s => s.Id == newGroup.Id).ExecuteCommandAsync();

                await _userWalletRepository.AsUpdateable()
                    .SetColumns(s => s.GId == newGroup.Id)
                    .SetColumns(s => s.GName == newGroup.Name)
                    .SetColumns(s => s.OrderIndex == s.Id)
                    .Where(s => fids.Contains(s.Id))
                    .ExecuteCommandAsync();

                return true;
            };

            foreach (var item in dict)
            {
                await UpDataId(item.Value);
            }

            if (input.isDelete == true)
            {
                await _userGroupRepository.AsDeleteable().Where(s => input.selectedGroups.Contains(s.Id)).ExecuteCommandAsync();
            }
        }
        //打散
        else if (input.operationType == 3)
        {
            var list = await _userWalletRepository.AsQueryable()
               .Where(it => it.UId == curUid && input.selectedGroups.Contains(it.GId)).OrderBy(it => it.OrderIndex).ToListAsync();
            if (list == null || list.Count <= 0) throw Oops.Bah("请选择有效的分组打散");

            var updList = list.OrderBy(s => Guid.NewGuid()).Select((s, idx) => new { idx = idx + 1, item = s }).ToList();
            updList.ForEach(s => s.item.OrderIndex = s.idx);
            var execList = updList.Select(s => s.item).ToList();
            await _userWalletRepository.AsUpdateable(execList)
                .UpdateColumns(s => new { s.OrderIndex })
                .ExecuteCommandAsync();

        }

        return true;
    }

    /// <summary>
    /// 导入
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("/user/importuserwallet")]
    public async Task<bool> ImportUserWallet(ImportUserWalletInput input)
    {
        var curUid = (int)_userManager.UserId;
        if (string.IsNullOrWhiteSpace(input.Wallets)) throw Oops.Bah("请输入要导入的内容");
        var Wallets = input.Wallets.Split(",").ToList();
        if (Wallets.Count <= 0) throw Oops.Bah("请输入要导入的内容");
        //if (input.TId == 0 && input.GId == 0) throw Oops.Bah("请选择标签或者分组");

        var group = await _userGroupRepository.AsQueryable().Where(it => it.UId == curUid && it.Id == input.GId)
            .FirstAsync();
        //if (input.GId > 0 && group == null)
        //{
        //    if (group == null) throw Oops.Bah("分组不存在");
        //}

        //var tag = await _userTagRepository.AsQueryable().Where(it => it.UId == curUid && it.Id == input.TId)
        //    .FirstAsync();

        //if (input.TId > 0 && tag == null)
        //{
        //    if (tag == null) throw Oops.Bah("标签不存在");
        //}


        var sourceList = Wallets.Distinct().ToList();
        var list = new List<UserWallet>();
        var distList = await _userWalletRepository.AsQueryable()
            .Where(it => it.UId == curUid && sourceList.Contains(it.SecretKey))
            .OrderByDescending(it => it.OrderIndex)
            .Select(it => it.SecretKey)
            .ToListAsync();

        // 过滤掉已存在的私钥
        var newKeys = sourceList.Except(distList).ToList();

        for (int i = 0; i < newKeys.Count; i++)
        {
            var item = newKeys[i];
            var account = Solnet.Wallet.Account.FromSecretKey(item);
            list.Add(new UserWallet()
            {
                GId = group == null ? 0 : group.Id,
                GName = group == null ? "" : group.Name,
                //TId = new List<int>() { tag == null ? 0 : tag.Id },
                //TName = new List<string>() { tag == null ? "" : tag.Name },
                PubKey = account.PublicKey,
                SecretKey = account.PrivateKey,
                CreateTime = DateTime.Now,
                TokenNum = 0,
                UId = curUid
            });
        }

        if (list.Count > 0)
            await _userWalletRepository.AsInsertable(list).ExecuteCommandAsync();
        else
        {
            throw Oops.Bah("钱包已存在");
        }

        return list.Count > 0;
    }

    /// <summary>
    /// 导出钱包
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [NoApiResult]
    [Route("/user/exportwallet")]
    public async Task<FileContentResult> ExportUserWallet(ExportWalletInput input)
    {
        var curUid = (int)_userManager.UserId;
        //if (input.TId == 0 && input.GId == 0) throw Oops.Bah("请选择标签或者分组");

        var gId = (input.GId ?? 0);

        // 查询钱包列表
        var wallets = await _userWalletRepository.AsQueryable()
            .Where(it => it.UId == curUid && it.GId == gId)
            .OrderByDescending(it => it.OrderIndex)
            //.WhereIF(input.TId > 0, it => SqlFunc.JsonArrayAny(it.TId, input.TId))
            .Select(it => new { it.PubKey, it.SecretKey })
            .ToListAsync();

        if (!wallets.Any())
            throw Oops.Bah("未找到可导出的钱包");

        // 构建导出内容
        var sb = new StringBuilder();
        sb.AppendLine("# 钱包导出信息");
        sb.AppendLine($"# 导出时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"# 钱包数量：{wallets.Count}");
        sb.AppendLine("# 格式：公钥,私钥");
        sb.AppendLine("----------------------------------------");

        foreach (var wallet in wallets)
        {
            sb.AppendLine($"{wallet.PubKey}            {wallet.SecretKey}");
        }

        // 转换为字节数组
        byte[] fileBytes = Encoding.UTF8.GetBytes(sb.ToString());

        // 获取分组或标签名称用于文件命名
        string exportName = "wallets";
        // 生成文件名
        string fileName = $"{exportName}_{DateTime.Now:yyyyMMdd_HHmmss}.txt";

        // 返回文件
        return new FileContentResult(fileBytes, "application/octet-stream")
        {
            FileDownloadName = fileName,
            EnableRangeProcessing = true // 支持断点续传
        };
    }

    /// <summary>
    /// 更新钱包名称
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("/user/updatewalletnickname")]
    public async Task<bool> UpdateWalletNickName(WalletUpdateNickNameInput input)
    {
        var curUid = (int)_userManager.UserId;
        if (input.Id <= 0) throw Oops.Bah("钱包ID不能为空");
        //if (string.IsNullOrWhiteSpace(input.NickName)) throw Oops.Bah("钱包名称不能为空");

        var uw = await _userWalletRepository.AsQueryable().Where(it => it.Id == input.Id && it.UId == curUid)
            .FirstAsync();
        if (uw == null) throw Oops.Bah("钱包不存在");

        if (!string.IsNullOrEmpty(input.NickName))
        {
            uw = await _userWalletRepository.AsQueryable()
                .Where(it => it.Id != input.Id && it.UId == curUid && it.Name == input.NickName)
                .FirstAsync();
            if (uw != null) throw Oops.Bah("钱包名称已存在");
        }


        await _userWalletRepository.AsUpdateable()
            .SetColumns(it => it.Name == input.NickName)
            .Where(it => it.UId == curUid && it.Id == input.Id)
            .ExecuteCommandHasChangeAsync();
        return true;
    }


    /// <summary>
    /// 钱包持仓100%卖出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [Route("/user/wallethodersell")]
    public async Task<bool> WalletHoderSell(TokenBalanceInput input)
    {
        if (string.IsNullOrWhiteSpace(input.PubKey)) throw Oops.Bah("请输入公钥！");
        if (!Solnet.Wallet.PublicKey.IsValid(input.PubKey)) throw Oops.Bah("输入的公钥不正确！");
        var curId = (int)_userManager.UserId;


        if (input.Type == 1)
        {
            //var dexType = "";
            var mywallet = await _userMyWalletRepository.AsQueryable()
                .Where(s => s.UId == curId && s.PubKey == input.PubKey).FirstAsync();
            if (mywallet != null)
            {
                var account = Solnet.Wallet.Account.FromSecretKey(mywallet.SecretKey);
                var dexType = (await _solanaApi.GetMintDexType(input.Mint));
                var (successful, signature, errormsg) = await _solanaApi.Sell(dexType, account, input.Mint, 100, 0, 0);
                if (string.IsNullOrWhiteSpace(signature))
                {
                    throw Oops.Bah("交易失败，稍后再试！");
                }

                //跟新持仓数
                //await _userMyWalletRepository.AsUpdateable().SetColumns(s => s.TokenNum == s.TokenNum - 1).Where(s =>s.Id== mywallet.Id && s.TokenNum-1>=0).ExecuteCommandAsync();
                //百分百 卖出 时 标识所有未完成子任务为 失效 状态
                //获取代币信息
                //JupAgStreamResult.PoolData tokenInfo = null;
                //if (await _redisClient.HExistsAsync(RedisKey.SolanaTokenInfo, input.Mint))
                //{
                //    var tokens =
                //        await _redisClient.HGetAsync<Dictionary<string, JupAgStreamResult.PoolData>>(RedisKey.SolanaTokenInfo, input.Mint);
                //    if (ispump) tokenInfo = tokens[input.Mint];
                //    else
                //    {
                //        foreach (var data in tokens)
                //        {
                //            if (data.Value.Type == "raydium")
                //            {
                //                tokenInfo = data.Value;
                //                break;
                //            }
                //        }
                //    }
                //}

                var tokenInfo = await _solanaApi.GetMint(input.Mint);

                //写日志
                var logModel = new UserAllTraderLog()
                {
                    TraderType = TraderType.Trader_00,
                    UId = curId,
                    Chain = tokenInfo?.Chain ?? "",
                    TokenName = tokenInfo?.Name ?? "",
                    TokenSymbol = tokenInfo?.Symbol ?? "",
                    TokenAddress = input.Mint,
                    TokenIcon = tokenInfo?.Icon ?? "",
                    IsBuy = false,
                    SolAmount = 0,
                    TokenAmount = 0,
                    Gas = 0,
                    MyWalletAddress = mywallet.PubKey,
                    //MyWalletMark= mywallet.Name,
                    Status = "成功",
                    CreateTime = DateTime.Now,
                };
                // dexType = (await _solanaApi.GetMint(input.Mint))?.Type;
                logModel.Id = await _userAllTraderLogRepository.AsInsertable(logModel).ExecuteReturnIdentityAsync();
                await _eventPublisher.PublishAsync("QuickTradeUpdateAmountEvent",
                    new QuickTradeUpdateAmountEvent(logModel.Id, signature, input.Mint, dexType, false));
                await Task.Delay(500);
                var dict = new Dictionary<string, string>()
                {
                    ["uid"] = curId.ToString(),
                    ["wid"] = mywallet.Id.ToString(),
                    ["mint"] = input.Mint,
                    ["publickey"] = mywallet.PubKey
                };
                //发送消息
                await _redisClient.XAddAsync(StreamKey.RemoveTokenInventoryStream, dict);

                return true;
            }
        }
        else if (input.Type == 2)
        {
            var wallet = await _userWalletRepository.AsQueryable()
                .Where(s => s.UId == curId && s.PubKey == input.PubKey).FirstAsync();
            if (wallet != null)
            {
                var account = Solnet.Wallet.Account.FromSecretKey(wallet.SecretKey);
                var dexType = (await _solanaApi.GetMintDexType(input.Mint));
                var (successful, signature, errormsg) = await _solanaApi.Sell(dexType, account, input.Mint, 100, 0, 0);

                if (string.IsNullOrWhiteSpace(signature))
                {
                    throw Oops.Bah("交易失败，稍后再试！");
                }

                //跟新持仓数
                await _userWalletRepository.AsUpdateable().SetColumns(s => s.TokenNum == s.TokenNum - 1)
                    .Where(s => s.Id == wallet.Id && s.TokenNum - 1 >= 0).ExecuteCommandAsync();
                return true;
            }
        }


        return false;
    }


    /// <summary>
    /// 钱包持仓
    /// </summary>
    [HttpPost]
    [Route("/user/mytokens/list")]
    public async Task<List<UserMyTokensServices.Dto.MyUserTokenDto>> GetUserTokens(MyTokensInputDto input)
    {
        var curUid = (int)_userManager.UserId;

        var ownerList = new List<string>();
        if (input.WalletId != null)
        {
            var myUserTokens = await _userWalletRepository.AsQueryable()
                .Where(x => x.UId == curUid && x.Id == input.WalletId)
                .FirstAsync();
            if (myUserTokens == null) throw Oops.Bah("请选择钱包!");
            ownerList.Add(myUserTokens.PubKey);
        }
        else
        {
            if (input.GId < 0) throw Oops.Bah("请选择分组!");
            if (input.GId > 0 && await _userGroupRepository.AsQueryable()
                    .Where(s => s.UId == curUid && s.Id == input.GId).FirstAsync() == null) throw Oops.Bah("请选择分组!");
            var myUserTokens = await _userWalletRepository.AsQueryable()
                .Where(x => x.UId == curUid && x.GId == input.GId)
                .Select(s => s.PubKey).ToListAsync();
            if (myUserTokens.Count <= 0) throw Oops.Bah("请选择分组!");
            ownerList.AddRange(myUserTokens);
        }

        var tokenAccounts = new List<UserMyTokensServices.Dto.MyUserTokenDto>();

        var tokens = new List<Solnet.Rpc.Models.TokenAccount>();
        for (int i = 0; i < ownerList.Count; i++)
        {
            var token = await _solanaApi.GetTokenAccountByOwnerAsync(ownerList[i]);
            tokens.AddRange(token);
        }


        var queryTokens = tokens
            .Where(s => s.Account.Data.Parsed.Info.Mint != "So11111111111111111111111111111111111111112").WhereIF(
                input.IsHideZero != null && input.IsHideZero.Value,
                s => s.Account.Data.Parsed.Info.TokenAmount.AmountUlong > 0)
            .ToList();

        var mints = queryTokens.Select(s => s.Account.Data.Parsed.Info.Mint).Distinct().ToArray();
        if (mints.Length <= 0) return tokenAccounts;
        var solUsdtPrice = await _redisClient.GetAsync<decimal>(RedisKey.SolanaWSOLPrice);
        var tokenInfos = await _solanaApi.GetMints(mints);


        foreach (var item in queryTokens)
        {
            var tokenInfo = tokenInfos.FirstOrDefault(s => s.Mint == item.Account.Data.Parsed.Info.Mint);
            var TokenUsdtPrice = Convert.ToDecimal(tokenInfo?.Price ?? 0);
            var
                HoldTime = -1; //item.Account.Data.Parsed.Info.TokenAmount.AmountUlong>0? await _solanaApi.GetTokenAccountHoldTime(item.PublicKey):-1;
            var tokenAccountItem = tokenAccounts.Where(s => s.TokenAddress == item.Account.Data.Parsed.Info.Mint)
                .FirstOrDefault();
            if (tokenAccountItem == null)
            {
                tokenAccounts.Add(new UserMyTokensServices.Dto.MyUserTokenDto()
                {
                    TokenName = tokenInfo?.Name ?? "",
                    TokenSymbol = tokenInfo?.Symbol ?? "",
                    TokenAddress = item.Account.Data.Parsed.Info.Mint,
                    TokenIcon = tokenInfo?.Icon ?? "",
                    TokenAmount = item.Account.Data.Parsed.Info.TokenAmount.AmountDecimal,
                    TokenSolPrice = solUsdtPrice == 0 ? 0 : TokenUsdtPrice / solUsdtPrice,
                    TokenUsdtPrice = TokenUsdtPrice,
                    UnrealizedProfit = 0,
                    RealizedProfit = 0,
                    TokenAmountBuy = 0,
                    TokenAmountSell = 0,
                    HoldTime = HoldTime,
                    Wallets = new List<UserMyTokensServices.Dto.WalletModel>()
                    {
                        new UserMyTokensServices.Dto.WalletModel()
                        {
                            WalletName = "", WalletAddress = item.Account.Data.Parsed.Info.Owner,
                            TokenAmount = item.Account.Data.Parsed.Info.TokenAmount.AmountDecimal
                        }
                    }
                });
            }
            else
            {
                tokenAccountItem.TokenAmount += item.Account.Data.Parsed.Info.TokenAmount.AmountDecimal;
                tokenAccountItem.Wallets.Add(new UserMyTokensServices.Dto.WalletModel()
                {
                    WalletName = "",
                    WalletAddress = item.Account.Data.Parsed.Info.Owner,
                    TokenAmount = item.Account.Data.Parsed.Info.TokenAmount.AmountDecimal
                });
            }
        }

        var MyWalletAddress = tokenAccounts.Select(s => s.Wallets.SelectMany(x => x.WalletAddress)).ToList().Distinct()
            .ToArray();
        var myWallet = await _userWalletRepository.AsQueryable()
            .Where(s => s.UId == curUid && MyWalletAddress.Contains(s.PubKey)).ToListAsync();

        tokenAccounts.ForEach(it =>
        {
            it.Wallets.ForEach(subit =>
            {
                subit.WalletName =
                    myWallet.Where(s => s.PubKey == subit.WalletAddress).FirstOrDefault()?.Name ?? "";
            });
        });


        var pageData = tokenAccounts
            .WhereIF(input.IsHidenMin != null && input.IsHidenMin.Value,
                s => s.TokenUsdtPrice != 0 && s.TokenUsdtPrice * s.TokenAmount > 10) //隐藏小额资产暂定隐藏小于10U
            .WhereIF(input.IsHidenSmall != null && input.IsHidenSmall.Value,
                s => s.Liquidity != 0 && s.Liquidity > 10) //隐藏小额资产暂定隐藏小于10SOL
            .ToList();
        return pageData;
    }

    /// <summary>
    /// 交易
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("/user/mytokens/trade")]
    public async Task<bool> TokenTrade(MyTokenTradeInputDto input)
    {
        var curUid = (int)_userManager.UserId;

        if (input.type == "sell" && input.percentage <= 1 || input.percentage > 100) throw Oops.Bah("请输入卖出的百分比");
        if (input.type == "buy" && input.amount <= 0) throw Oops.Bah("请输入买入金额");

        var Wallets = new List<UserWallet>();
        if (input.walletId != null)
        {
            var wallet = await _userWalletRepository.AsQueryable()
                .Where(s => s.UId == curUid && s.Id == input.walletId)
                .FirstAsync();
            if (wallet == null) throw Oops.Bah("请选择钱包");
            Wallets.Add(wallet);
        }
        else
        {
            if (input.GId < 0) throw Oops.Bah("请选择分组!");
            if (input.GId > 0 && await _userGroupRepository.AsQueryable()
                    .Where(s => s.UId == curUid && s.Id == input.GId).FirstAsync() == null) throw Oops.Bah("请选择分组!");

            var wallet = await _userWalletRepository.AsQueryable().Where(s => s.UId == curUid && s.GId == input.GId)
                .OrderByDescending(it => it.OrderIndex)
                .ToListAsync();
            Wallets.AddRange(wallet);
        }

        foreach (var item in input.mint)
        {
            var tokenInfo = await _solanaApi.GetMint(item);
            var Model = new UserBatchTrade()
            {
                Chain = "Solana",
                UId = curUid,
                TokenAddress = item,
                TokenName = tokenInfo?.Name ?? "-",
                TokenSymbol = tokenInfo?.Symbol ?? "-",
                TokenIcon = tokenInfo?.Icon ?? "-",
                Slippage = input.slippage,
                TradeModel = 3,
                Gas = input.gas,
                Mev = input.mev,
                Status = 1,
                GroupId = Wallets.FirstOrDefault().GId,
                Wallets = Wallets.Select(s => new Domain.WalletModel()
                { WalletId = s.Id, Name = s.Name, PubKey = s.PubKey, SecretKey = s.SecretKey }).ToList(),
                CreateTime = DateTime.Now,
                IsBuy = input.type == "buy",
                ExpiredAuto = false,
                AmountData = new AmountModel
                {
                    Type = input.type == "buy" ? 3 : 2,
                    Amount = input.type == "buy" ? input.amount : null,
                    PercentRange = new List<decimal?>() { input.percentage, input.percentage }
                }
            };

            Model = await _userBatchTradeRepository.AsInsertable(Model).ExecuteReturnEntityAsync();
            if (Model.Id < 0) throw Oops.Bah("入库失败！");

            //TODO: 发送消息创建任务
            var dictPrice = new Dictionary<string, string>()
            {
                ["Id"] = $"{Model.Id}",
            };
            await _redisClient.XAddAsync(StreamKey.BatchTradeStream, dictPrice);
        }

        return true;
    }

    #endregion

    #region 我的钱包

    /// <summary>
    /// 获取我的钱包
    /// </summary>
    [HttpGet]
    [Route("/user/getmywallets")]
    public async Task<List<UserMyWalletOutput>> GetMyWallet()
    {
        var curUid = (int)_userManager.UserId;
        var myWallets = await _userMyWalletRepository.AsQueryable().Where(it => it.UId == curUid).ToListAsync();
        var list = myWallets.Adapt<List<UserMyWalletOutput>>();

        var address = list.Select(it => it.PubKey).ToList();

        var total = address.Count / 100 + (address.Count % 100 > 0 ? 1 : 0);
        var dict = new Dictionary<string, double>();
        for (int ii = 0; ii < total; ii++)
        {
            var userAddress = address.Skip(ii * 100).Take(100).ToList();
            var balances = await _solanaApi.GetMultipleAccountsBalanceAsync(userAddress);

            foreach (var balance in balances)
            {
                dict[balance.Key] = balance.Value;
            }
        }


        for (int i = 0; i < list.Count; i++)
        {
            var item = list[i];
            if (dict.ContainsKey(item.PubKey))
            {
                item.Sol = Math.Floor(Convert.ToDecimal((dict[item.PubKey] * 100).ToString())) / 100M;
            }
        }

        return list;
    }


    /// <summary>
    /// 更新钱包名称
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("/user/updatemywalletname")]
    public async Task<bool> UpdateMyWalletName(WalletUpdateNickNameInput input)
    {
        var curUid = (int)_userManager.UserId;
        if (input.Id <= 0) throw Oops.Bah("钱包ID不能为空");
        //if (string.IsNullOrWhiteSpace(input.NickName)) throw Oops.Bah("钱包名称不能为空");

        var uw = await _userMyWalletRepository.AsQueryable().Where(it => it.Id == input.Id && it.UId == curUid)
            .FirstAsync();
        if (uw == null) throw Oops.Bah("钱包不存在");

        if (!string.IsNullOrEmpty(input.NickName))
        {
            uw = await _userMyWalletRepository.AsQueryable()
                .Where(it => it.Id != input.Id && it.UId == curUid && it.Name == input.NickName)
                .FirstAsync();
            if (uw != null) throw Oops.Bah("钱包名称已存在");
        }


        await _userMyWalletRepository.AsUpdateable()
            .SetColumns(it => it.Name == input.NickName)
            .Where(it => it.UId == curUid && it.Id == input.Id)
            .ExecuteCommandHasChangeAsync();
        return true;
    }

    /// <summary>
    /// 生成一个我的钱包
    /// </summary>
    [HttpGet]
    [Route("/user/mywalletcreate")]
    public async Task<UserMyWalletOutput> CreateMyWallet()
    {
        var curUid = (int)_userManager.UserId;
        var myWalletsCount = await _userMyWalletRepository.AsQueryable().Where(it => it.UId == curUid).CountAsync();
        if (myWalletsCount > 10) //最多创建10个 个人钱包
            throw Oops.Bah("个人钱包只能创建10个");
        var newWallet = new Solnet.Wallet.Account();
        var myWallet = new UserMyWallet()
        {
            UId = curUid,
            PubKey = newWallet.PublicKey,
            SecretKey = newWallet.PrivateKey,
            Name = $"钱包{(myWalletsCount + 1)}",
            IsDefault = false,
            TokenNum = 0,
            CreateTime = DateTime.Now
        };
        myWallet.Id = await _userMyWalletRepository.AsInsertable(myWallet).ExecuteReturnIdentityAsync();
        var res = myWallet.Adapt<UserMyWalletOutput>();
        return res;
    }

    /// <summary>
    /// 查看私钥
    /// </summary>
    /// <param name="wid"></param>
    /// <returns></returns>
    [HttpGet]
    [Route("/user/getmywalletsecret/{wid}")]
    public async Task<string> GetMyWalletSecret(int wid)
    {
        var curUid = (int)_userManager.UserId;
        var myWallet = await _userMyWalletRepository.AsQueryable().Where(it => it.UId == curUid && it.Id == wid)
            .FirstAsync();
        if (myWallet == null) Oops.Bah("没有找到记录");
        return myWallet.SecretKey;
    }

    /// <summary>
    /// 导入我的钱包
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [Route("/user/importmyuserwallet")]
    public async Task<bool> ImportMyUserWallet(ImportMyUserWalletInput input)
    {
        var curUid = (int)_userManager.UserId;
        if (string.IsNullOrWhiteSpace(input.Wallets)) throw Oops.Bah("请输入要导入的内容");
        var Wallets = input.Wallets.Split(",").ToList();
        if (Wallets.Count <= 0) throw Oops.Bah("请输入要导入的内容");
        //过滤重复
        var sourceList = Wallets.Distinct().ToList();

        var list = new List<UserMyWallet>();
        var distList = await _userMyWalletRepository.AsQueryable()
            .Where(it => it.UId == curUid && sourceList.Contains(it.SecretKey))
            .Select(it => it.SecretKey).ToListAsync();


        // 过滤掉已存在的私钥
        var newKeys = sourceList.Except(distList).ToList();

        var count = await _userMyWalletRepository.AsQueryable()
            .Where(it => it.UId == curUid).CountAsync();
        for (int i = 0; i < newKeys.Count; i++)
        {
            var item = newKeys[i];
            var account = Solnet.Wallet.Account.FromSecretKey(item);
            var index = count + i + 1;
            list.Add(new UserMyWallet()
            {
                PubKey = account.PublicKey,
                SecretKey = account.PrivateKey,
                CreateTime = DateTime.Now,
                TokenNum = 0,
                Name = $"钱包{index}",
                IsDefault = false,
                UId = curUid
            });
            if (index > 10) break; //超过10 结束
        }


        if (list.Count > 0)
            await _userMyWalletRepository.AsInsertable(list).ExecuteCommandAsync();
        else
        {
            throw Oops.Bah("钱包已存在");
        }

        return list.Count > 0;
    }

    #endregion

    #region 分组

    [HttpGet]
    [Route("/user/getgroups")]
    public async Task<List<GroupDto>> GetUserGroupsAsync()
    {
        var curId = (int)_userManager.UserId;
        var list = await _userGroupRepository.AsQueryable().Where(it => it.UId == curId).ToListAsync();
        List<GroupDto> data = new();
        data = list.Adapt<List<GroupDto>>();

        var walletNum = await _userWalletRepository.AsQueryable().Where(s => s.UId == curId)
            .GroupBy(s => s.GId).Select(s => new { s.GId, Num = SqlFunc.AggregateCount(s.Id) }).ToListAsync();

        if (walletNum.FirstOrDefault(s => s.GId == 0) != null)
        {
            data.Insert(0, new GroupDto() { Id = 0, Name = "未分组", WalletNum = 0 });
        }

        data.ForEach(it => it.WalletNum = walletNum.FirstOrDefault(s => s.GId == it.Id)?.Num ?? 0);


        return data;
    }

    /// <summary>
    /// 写入分组
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [Route("/user/creategroups")]
    public async Task<UserGroup> CreateUserGroupsAsync(GroupInput input)
    {
        var curId = (int)_userManager.UserId;

        var group = await _userGroupRepository.AsQueryable().Where(it => it.Name == input.GroupName && it.UId == curId)
            .FirstAsync();
        if (group != null) throw Oops.Bah("分组名称已经存在！");
        group = new UserGroup() { UId = curId, Name = input.GroupName };
        group.Id = await _userGroupRepository.AsInsertable(group).ExecuteReturnIdentityAsync();
        return group;
    }


    /// <summary>
    /// 编辑分组
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [Route("/user/editgroups")]
    public async Task<UserGroup> EditUserGroupsAsync(GroupInput input)
    {
        var curId = (int)_userManager.UserId;

        var group = await _userGroupRepository.AsQueryable().Where(it => it.UId == curId && it.Id == input.Id)
            .FirstAsync();
        if (group == null) throw Oops.Bah("分组不存在！");
        var groupName = await _userGroupRepository.AsQueryable()
            .Where(it => it.UId == curId && it.Id != input.Id && it.Name == input.GroupName).FirstAsync();
        if (groupName != null) throw Oops.Bah("分组名称已存在！");
        group.Name = input.GroupName;
        await _userGroupRepository.AsUpdateable().SetColumns(s => s.Name == group.Name).Where(s => s.Id == group.Id)
            .ExecuteCommandAsync();
        return group;
    }

    /// <summary>
    /// 删除分组
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [Route("/user/delgroups")]
    public async Task<UserGroup> DeleteUserGroupsAsync(GroupInput input)
    {
        var curId = (int)_userManager.UserId;

        var group = await _userGroupRepository.AsQueryable().Where(it => it.UId == curId && it.Id == input.Id)
            .FirstAsync();
        if (group == null) throw Oops.Bah("分组不存在！");

        var walletNum = await _userWalletRepository.AsQueryable().Where(s => s.UId == curId && s.GId == group.Id)
            .CountAsync();
        if (walletNum > 0) throw Oops.Bah($"该分组下有{walletNum}个钱包,禁止删除！");

        await _userGroupRepository.DeleteAsync(group);
        return group;
    }

    #endregion

    #region 标签

    //[HttpGet]
    //[Route("/user/gettags")]
    //public async Task<List<TagDto>> GetUserTagsAsync()
    //{
    //    var curId = (int)_userManager.UserId;
    //    var list = await _userTagRepository.AsQueryable().Where(it => it.UId == curId || it.UId == 0).ToListAsync();

    //    List<TagDto> data = new();
    //    data = list.Adapt<List<TagDto>>();

    //    foreach (var tag in data)
    //    {
    //        tag.WalletNum = await _userWalletRepository.AsQueryable().Where(s => SqlFunc.JsonArrayAny(s.TId
    //            , tag.Id)).CountAsync();
    //    }

    //    return data;
    //}

    //[HttpPost]
    //[Route("/user/createtags")]
    //public async Task<UserTag> CreateUserTagsAsync(TagInput input)
    //{
    //    var curId = (int)_userManager.UserId;
    //    var tag = await _userTagRepository.AsQueryable().Where(it => it.UId == curId && it.Name == input.TagName)
    //        .FirstAsync();
    //    if (tag != null) throw Oops.Bah("标签名称已经存在！");
    //    tag = new UserTag() { UId = curId, Name = input.TagName };
    //    tag.Id = await _userTagRepository.AsInsertable(tag).ExecuteReturnIdentityAsync();
    //    return tag;
    //}

    ///// <summary>
    ///// 编辑标签
    ///// </summary>
    ///// <param name="input"></param>
    ///// <returns></returns>
    ///// <exception cref="BusinessException"></exception>
    //[HttpPost]
    //[Route("/user/edittags")]
    //public async Task<UserTag> EditUserTagsAsync(TagInput input)
    //{
    //    var curId = (int)_userManager.UserId;

    //    var tag = await _userTagRepository.AsQueryable().Where(it => it.UId == curId && it.Id == input.Id)
    //        .FirstAsync();
    //    if (tag == null) throw Oops.Bah("标签不存在！");
    //    var tagName = await _userTagRepository.AsQueryable()
    //        .Where(it => it.UId == curId && it.Id != input.Id && it.Name == input.TagName).FirstAsync();
    //    if (tagName != null) throw Oops.Bah("标签名称已存在！");
    //    tag.Name = input.TagName;
    //    await _userTagRepository.AsUpdateable().SetColumns(s => s.Name == tag.Name).Where(s => s.Id == tag.Id)
    //        .ExecuteCommandAsync();
    //    return tag;
    //}

    ///// <summary>
    ///// 删除标签
    ///// </summary>
    ///// <param name="input"></param>
    ///// <returns></returns>
    ///// <exception cref="BusinessException"></exception>
    //[HttpPost]
    //[Route("/user/deltags")]
    //public async Task<UserTag> DeleteUserTagsAsync(TagInput input)
    //{
    //    var curId = (int)_userManager.UserId;

    //    var tag = await _userTagRepository.AsQueryable().Where(it => it.UId == curId && it.Id == input.Id)
    //        .FirstAsync();
    //    if (tag == null) throw Oops.Bah("标签不存在！");

    //    var walletNum = await _userWalletRepository.AsQueryable().Where(s => s.UId == curId && s.TId.Contains(tag.Id))
    //        .CountAsync();
    //    if (walletNum > 0) throw Oops.Bah($"该标签下有{walletNum}个钱包,禁止删除！");

    //    await _userTagRepository.DeleteAsync(tag);
    //    return tag;
    //}

    #endregion

    #region 转账

    /// <summary>
    /// 一 =》一
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [Route("/transfer/onetoone")]
    public async Task<string> TransferOneToOneAsync(TransferOneInput input)
    {
        var curUid = (int)_userManager.UserId;
        if (input.FrmAddressAddMode == "import" && string.IsNullOrWhiteSpace(input.FrmWalletAddress))
            throw Oops.Bah("转出私钥不能为空！");
        if (input.FrmAddressAddMode == "wallet" && input.FrmWalletId <= 0) throw Oops.Bah("请选择转出钱包！");
        if (input.ToAddressAddMode == "import" && string.IsNullOrWhiteSpace(input.ToWalletAddress) &&
            !Solnet.Wallet.PublicKey.IsValid(input.ToWalletAddress)) throw Oops.Bah("目标公钥不能为空！");
        if (input.ToAddressAddMode == "wallet" && input.ToWalletId <= 0) throw Oops.Bah("请选择转出钱包！");

        Account frmAccount;
        if (input.FrmAddressAddMode == "import")
        {
            frmAccount = Account.FromSecretKey(input.FrmWalletAddress);
        }
        else
        {
            var entity = await _userWalletRepository.AsQueryable().Where(s => s.Id == input.FrmWalletId).FirstAsync();
            if (entity == null) throw Oops.Bah("请选择转出钱包！");
            frmAccount = Account.FromSecretKey(entity.SecretKey);
        }

        string toAccount;
        if (input.ToAddressAddMode == "import")
        {
            toAccount = input.ToWalletAddress;
        }
        else
        {
            var entity = await _userWalletRepository.AsQueryable().Where(s => s.Id == input.ToWalletId).FirstAsync();
            if (entity == null) throw Oops.Bah("请选择转出钱包！");
            toAccount = entity.PubKey;
        }

        if (input.Amount <= 0) throw Oops.Bah("转账金额必须大于0！");

        var tranLog = new UserTransferLog()
        {
            UId = curUid,
            Chain = input.Chain,
            FrmSecretKey = frmAccount.PrivateKey.ToString(),
            FrmPubKey = frmAccount.PublicKey.ToString(),
            Amount = input.Amount,
            CreateTime = DateTime.Now,
            ToPubKey = toAccount,
            ToSecretKey = "",
            Status = TransferEnum.State_2,
            Signature = ""
        };

        var sign = await _solanaApi.Transaction(frmAccount, toAccount, input.Amount);
        if (string.IsNullOrEmpty(sign)) throw Oops.Bah("转账失败");

        tranLog.Signature = sign;
        tranLog.CompleteTime = DateTime.Now;
        tranLog.Id = await _userTransferLogRepository.AsInsertable(tranLog).ExecuteReturnIdentityAsync();

        //插 转账记录 结果  总线消息
        await _eventPublisher.PublishAsync("TransferToDb", new TransferToDbEvent(tranLog.Id, sign));
        return sign;
    }

    /// <summary>
    /// 一 =》多
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [Route("/transfer/onetomultiple")]
    public async Task<int> TransferOneToMultiple(OneToMultipleDtoInput input)
    {
        if (input.FrmAddressAddMode == "import" && string.IsNullOrWhiteSpace(input.FrmWalletAddress))
            throw Oops.Bah("转出私钥不能为空！");
        if (input.FrmAddressAddMode == "wallet" && input.FrmWalletId <= 0) throw Oops.Bah("请选择转出钱包！");
        if (input.AmountMin <= 0 || input.AmountMax <= 0 || input.AmountMin > input.AmountMax)
            throw Oops.Bah("金额输入错误！");
        if (input.TimeMin <= 0 || input.TimeMax <= 0 || input.TimeMax < input.TimeMin)
            throw Oops.Bah("时间间隔输入错误！");
        if (input.TranType < 0 || input.TranType > 2) throw Oops.Bah("请选择类型！");
        //导入模式
        if (input.TranType == 2 && string.IsNullOrWhiteSpace(input.TranData)) throw Oops.Bah("请导入目标钱包！");

        var arry = input.TranData.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).Distinct()
            .ToArray();
        if (arry.Length <= 0) throw Oops.Bah("请导入目标钱包！");

        Account frmAccount;
        if (input.FrmAddressAddMode == "import")
        {
            frmAccount = Account.FromSecretKey(input.FrmWalletAddress);
        }
        else
        {
            var entity = await _userWalletRepository.AsQueryable().Where(s => s.Id == input.FrmWalletId).FirstAsync();
            if (entity == null) throw Oops.Bah("请选择转出钱包！");
            frmAccount = Account.FromSecretKey(entity.SecretKey);
        }

        var curUid = (int)_userManager.UserId;
        var TaskParas = input.Adapt<OneToMultipleInput>();
        TaskParas.TranData = arry.Join(",");
        TaskParas.SecretKey = frmAccount.PrivateKey.ToString();
        var uttr = new UserTransferTask()
        {
            Chain = input.Chain,
            UId = curUid,
            CreateTime = DateTime.Now,
            Status = 0,
            TaskType = 1, //1=》多
            TaskParas = JsonSerializer.Serialize(TaskParas),
        };

        uttr.Id = await _userTransferTaskRepository.AsInsertable(uttr).ExecuteReturnIdentityAsync();
        var dict = new Dictionary<string, string>()
        {
            //用户ID-》任务 ID
            //[$"{curUid}"]=uttr.Id.ToString(),
            ["uid"] = curUid.ToString(),
            ["tid"] = uttr.Id.ToString()
        };
        //发送消息
        await _redisClient.XAddAsync(StreamKey.StreamTransfer, dict);
        return uttr.Id;
    }

    //多 => 一
    /// <summary>
    /// 多 => 一
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [Route("/transfer/multipletoone")]
    public async Task<int> TransferMultipleToOne(MultipleToOneDtoInput input)
    {
        if (input.TranType < 0 || input.TranType > 2) throw Oops.Bah("请选择类型！");
        if (input.TranType == 2 && string.IsNullOrWhiteSpace(input.TranData)) throw Oops.Bah("请导入转出钱包！");
        if (input.ToAddressAddMode == "import" && string.IsNullOrWhiteSpace(input.ToWalletAddress) &&
            !Solnet.Wallet.PublicKey.IsValid(input.ToWalletAddress)) throw Oops.Bah("目标公钥不能为空！");
        if (input.ToAddressAddMode == "wallet" && input.ToWalletId <= 0) throw Oops.Bah("请选择转出钱包！");
        var arry = input.TranData.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).Distinct()
            .ToArray();
        if (arry.Length <= 0) throw Oops.Bah("请导入转出钱包！");


        string toAccount;
        if (input.ToAddressAddMode == "import")
        {
            toAccount = input.ToWalletAddress;
        }
        else
        {
            var entity = await _userWalletRepository.AsQueryable().Where(s => s.Id == input.ToWalletId).FirstAsync();
            if (entity == null) throw Oops.Bah("请选择转出钱包！");
            toAccount = entity.PubKey;
        }

        var TaskParas = input.Adapt<MultipleToOneInput>();
        TaskParas.ToPublicKey = toAccount;
        TaskParas.TranData = arry.Join(",");
        var curUid = (int)_userManager.UserId;
        var uttr = new UserTransferTask()
        {
            Chain = input.Chain,
            UId = curUid,
            CreateTime = DateTime.Now,
            Status = 0,
            TaskType = 2, //多=>1
            TaskParas = JsonSerializer.Serialize(TaskParas),
        };
        uttr.Id = await _userTransferTaskRepository.AsInsertable(uttr).ExecuteReturnIdentityAsync();
        var dict = new Dictionary<string, string>()
        {
            //用户ID-》任务 ID
            //[$"{curUid}"]=uttr.Id.ToString(),
            ["uid"] = curUid.ToString(),
            ["tid"] = uttr.Id.ToString()
        };
        //发送消息
        await _redisClient.XAddAsync(StreamKey.StreamTransfer, dict);
        return uttr.Id;
    }

    //多 =》多
    /// <summary>
    /// 多 =》多
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [Route("/transfer/multipletomultiple")]
    public async Task<int> TransferMultipleToMultiple(MultipleToMultipleInput input)
    {
        if (input.TimeMin <= 0 || input.TimeMax <= 0 || input.TimeMax < input.TimeMin)
            throw Oops.Bah("时间间隔输入错误！");
        if (input.FromTranType < 0 || input.FromTranType > 2) throw Oops.Bah("请选择转出类型！");
        //导入模式
        if (input.FromTranType == 2 && string.IsNullOrWhiteSpace(input.FromTranData)) throw Oops.Bah("请导入转出钱包！");
        var fromArry = input.FromTranData.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
        if (fromArry.Length <= 0) throw Oops.Bah("请导入转出钱包！");
        if (input.ToTranType < 0 || input.ToTranType > 2) throw Oops.Bah("请选择转入类型！");
        //导入模式
        if (input.ToTranType == 2 && string.IsNullOrWhiteSpace(input.ToTranData)) throw Oops.Bah("请导入转入钱包！");
        var toArry = input.FromTranData.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
        if (toArry.Length <= 0) throw Oops.Bah("请导入转入钱包！");
        if (fromArry.Length != toArry.Length) throw Oops.Bah("转入转出数量不相等！");
        var curUid = (int)_userManager.UserId;
        var uttr = new UserTransferTask()
        {
            Chain = input.Chain,
            UId = curUid,
            CreateTime = DateTime.Now,
            Status = 0,
            TaskType = 3, //多=>多
            TaskParas = JsonSerializer.Serialize(input),
        };
        uttr.Id = await _userTransferTaskRepository.AsInsertable(uttr).ExecuteReturnIdentityAsync();
        var dict = new Dictionary<string, string>()
        {
            //用户ID-》任务 ID
            //[$"{curUid}"]=uttr.Id.ToString(),
            ["uid"] = curUid.ToString(),
            ["tid"] = uttr.Id.ToString()
        };
        //发送消息
        await _redisClient.XAddAsync(StreamKey.StreamTransfer, dict);
        return uttr.Id;
    }

    /// <summary>
    /// 查询转账日志
    /// </summary>
    [HttpPost]
    [Route("/transfer/logs")]
    public async Task<SqlSugarPagedList<TransferLogDto>> GetTransferLogs(TransferLogQueryInput input)
    {
        var curUid = (int)_userManager.UserId;

        var state = (int)input.Status;
        var query = _userTransferLogRepository.AsQueryable()
            .Where(x => x.UId == curUid && x.Level == 0)
            .WhereIF(!string.IsNullOrWhiteSpace(input.FromAddress),
                x => x.FrmPubKey == input.FromAddress || x.ToPubKey == input.ToAddress)
            .WhereIF(input.StartTime.HasValue, x => x.CreateTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, x => x.CreateTime <= input.EndTime)
            .WhereIF(state > -2, x => x.Status == input.Status)
            .OrderByDescending(x => x.CreateTime)
            .Select<TransferLogDto>();

        var pageData = await query.ToPagedListAsync(input.PageIndex, input.PageSize);
        var walletAddress = pageData.Items.Select(s => s.FrmPubKey).ToList();
        walletAddress.AddRange(pageData.Items.Select(s => s.FrmPubKey).ToList());
        walletAddress = walletAddress.Distinct().ToList();

        var entityWallet = await _userWalletRepository.AsQueryable().Where(s => walletAddress.Contains(s.PubKey))
            .ToListAsync();
        pageData.Items.ForEach(s =>
        {
            s.frmWalletName = entityWallet.FirstOrDefault(x => x.PubKey == s.FrmPubKey)?.Name ?? "";
            s.ToWalletName = entityWallet.FirstOrDefault(x => x.PubKey == s.ToPubKey)?.Name ?? "";
        });

        return pageData;
    }

    #endregion

    #region 交易

    ///// <summary>
    ///// 获取用户代币列表
    ///// </summary>
    //[HttpPost]
    //[Route("/user/tokens")]
    //public async Task<List<UserTokenDto>> GetUserTokens()
    //{
    //    var curUid = (int)_userManager.UserId;

    //    var query = _userTokensRepository.AsQueryable()
    //        .Where(x => x.UId == curUid)
    //        .Select<UserTokenDto>();

    //    var pageData = await query.ToListAsync();

    //    foreach (var item in pageData)
    //    {
    //        //获取代币单价
    //        var price = 0M;

    //        var tokens = await _solanaApi.GetMint(item.TokenAddress);
    //        price = Convert.ToDecimal(tokens.Price);

    //        item.UnrealizedProfit = (price * item.TokenAmount).ToTruncate(4);
    //        item.RealizedProfit = (await _userUserTradeLogRepository.AsQueryable()
    //            .Where(s => s.UId == curUid && s.IsBuy == false && s.TokenAddress == item.TokenAddress)
    //            .Select(s => SqlFunc.AggregateSum(s.Sol * s.TokenPrice)).FirstAsync()).ToDecimal().ToTruncate(4);
    //    }

    //    return pageData;
    //}

    ///// <summary>
    ///// 购买 代币
    ///// </summary>
    ///// <returns></returns>
    //[HttpPost]
    //[Route("/trade/buy")]
    //public async Task<int> TokenBuy(TokenBuyInput input)
    //{
    //    if (string.IsNullOrWhiteSpace(input.Mint)) throw Oops.Bah("请输入代币地址!");
    //    if (input.AmountMin <= 0 || input.AmountMax <= 0 || input.AmountMin > input.AmountMax)
    //        throw Oops.Bah("金额输入错误！");

    //    var curUid = (int)_userManager.UserId;
    //    var uttr = new UserTransferTask() //写入任务
    //    {
    //        UId = curUid,
    //        CreateTime = DateTime.Now,
    //        Status = 0,
    //        TaskType = 4, //购买
    //        TaskParas = JsonSerializer.Serialize(input),
    //    };
    //    uttr.Id = await _userTransferTaskRepository.AsInsertable(uttr).ExecuteReturnIdentityAsync();
    //    var dict = new Dictionary<string, string>()
    //    {
    //        //用户ID-》任务 ID
    //        //[$"{curUid}"]=uttr.Id.ToString(),
    //        ["uid"] = curUid.ToString(),
    //        ["tid"] = uttr.Id.ToString()
    //    };
    //    //发送消息
    //    await _redisClient.XAddAsync(StreamKey.StreamTransfer, dict);
    //    return uttr.Id;

    //    return 0;
    //}

    ///// <summary>
    ///// 卖出
    ///// </summary>
    ///// <returns></returns>
    //[HttpPost]
    //[Route("/trade/sell")]
    //public async Task<int> TokenSell(TokenSellInput input)
    //{
    //    var curUid = (int)_userManager.UserId;
    //    if (input.Mint.Count <= 0 && input.isClear == false) throw Oops.Bah("请选择要卖出代币!");

    //    if (input.isClear == true)
    //    {
    //        input.Mint = await _userTokensRepository.AsQueryable().Where(s => s.UId == curUid)
    //            .Select(s => s.TokenAddress).ToListAsync();
    //    }


    //    var uttr = new UserTransferTask()
    //    {
    //        UId = curUid,
    //        CreateTime = DateTime.Now,
    //        Status = 0,
    //        TaskType = 5, //卖出
    //        TaskParas = JsonSerializer.Serialize(input),
    //    };
    //    //UserTask.Create(curUid, 5, JsonSerializer.Serialize(input));

    //    uttr.Id = await _userTransferTaskRepository.AsInsertable(uttr).ExecuteReturnIdentityAsync();
    //    var dict = new Dictionary<string, string>()
    //    {
    //        //用户ID-》任务 ID
    //        //[$"{curUid}"]=uttr.Id.ToString(),
    //        ["uid"] = curUid.ToString(),
    //        ["tid"] = uttr.Id.ToString()
    //    };
    //    //发送消息
    //    await _redisClient.XAddAsync(StreamKey.StreamTransfer, dict);
    //    return uttr.Id;
    //    return 0;
    //}

    ///// <summary>
    ///// 查询交易日志
    ///// </summary>
    //[HttpPost]
    //[Route("/trade/logs")]
    //public async Task<SqlSugarPagedList<TradeLogDto>> GetTradeLogs(TradeLogQueryInput input)
    //{
    //    var curUid = (int)_userManager.UserId;

    //    var state = (int)input.Status;
    //    var query = _userUserTradeLogRepository.AsQueryable()
    //        .Where(x => x.UId == curUid)
    //        .WhereIF(input.StartTime.HasValue,
    //            x => SqlFunc.ToDateShort(x.CreateTime) >= SqlFunc.ToDateShort(input.StartTime))
    //        .WhereIF(input.EndTime.HasValue,
    //            x => SqlFunc.ToDateShort(x.CreateTime) <= SqlFunc.ToDateShort(input.EndTime))
    //        .WhereIF(input.IsBuy != null, x => x.IsBuy == input.IsBuy)
    //        .WhereIF(state > -2, x => x.Status == input.Status)
    //        .OrderByDescending(x => x.CreateTime)
    //        .Select<TradeLogDto>();

    //    var pageData = await query.ToPagedListAsync(input.PageIndex, input.PageSize);
    //    return pageData;
    //}

    ///// <summary>
    ///// 获取钱包数量
    ///// </summary>
    //[HttpPost]
    //[Route("/trade/getwalletnum")]
    //public async Task<int> GetUserWalletNumAsync(UserWalletGetInput input)
    //{
    //    var list = new List<UserWalletOutput>();
    //    var uw = await _userWalletRepository.AsQueryable()
    //        .Where(it => it.UId == _userManager.UserId)
    //        .WhereIF(input.Gid > 0, it => it.GId == input.Gid)
    //        //.WhereIF(input.TId > 0, it => SqlFunc.JsonArrayAny(it.TId, input.TId))
    //        .CountAsync();

    //    return uw;
    //}

    #endregion

    #region 代币

    /// <summary>
    /// 发射代币
    /// </summary>
    [HttpPost]
    [Route("/user/createtoken")]
    public async Task<bool> TokenCreateAndBuy(TokenCreateInput input)
    {
        var curUid = (int)_userManager.UserId;
        if (string.IsNullOrWhiteSpace(input.Name) || string.IsNullOrWhiteSpace(input.Symbol)
                                                  || string.IsNullOrWhiteSpace(input.Description)
                                                  || string.IsNullOrWhiteSpace(input.Logo)
           ) throw Oops.Bah("必须参数不能空，请检查！");
        if (input.MainBuyer == null) throw Oops.Bah("建立者不能空！");
        if (input.MainBuyer.WalletId <= 0) throw Oops.Bah("建立者不能空，请检查！");


        var myWallet = await _userMyWalletRepository.AsQueryable()
            .FirstAsync(s => s.UId == curUid && s.Id == input.MainBuyer.WalletId);
        if (myWallet == null) throw Oops.Bah("建立者不存在！");

        input.MainBuyer.PrivateKey = myWallet.SecretKey;

        if (input.Logo.Contains(","))
        {
            input.Logo = input.Logo.Split(',')[1];
        }

        var list = new List<(string, decimal)>();
        var dictSol = new Dictionary<string, (string, decimal)>();
        if (input.MainBuyer.Amount > 0)
        {
            list.Add((input.MainBuyer.PrivateKey, input.MainBuyer.Amount));
        }

        var bindWallet = await _userWalletRepository.AsQueryable()
            .Where(s => s.GId == input.Bindowers.Group && s.UId == curUid).ToListAsync();
        if (bindWallet != null && list.Count == 0)
        {
            foreach (var item in bindWallet)
            {
                var solAmount = GetRandomAmount(input.Bindowers.MinAmount, input.Bindowers.MaxAmount);
                list.Add((item.SecretKey, solAmount));
                if (list.Count == 1) break;
            }
        }

        if (input.Followers.MaxAmount < input.Followers.MinAmount) throw Oops.Bah("购买数量错误！");


        var coins = await _userCoinsBaseRepository.AsQueryable().Where(it => it.IsUse == false)
            .OrderBy(it => SqlFunc.GetRandom())
            .FirstAsync();
        if (coins == null) throw Oops.Bah("库存不足！");
        //coins.SecretKey = new Solnet.Wallet.Account().PrivateKey;
        (string Sign, string Mint, string Name, string Symbol, string Icon,
            List<(string pubkey, ulong outAmount)> outList) = await _solanaApi.MintCreateAndBuy(
            input.MainBuyer.PrivateKey, input.Name,
            input.Symbol, input.Description, input.Logo, input.twitter, input.telegram, input.website, coins.SecretKey,
            list
        );

        if (string.IsNullOrWhiteSpace(Sign)) throw Oops.Bah("创建失败！");
        //更新状态
        await _userCoinsBaseRepository.AsUpdateable().SetColumns(it => it.IsUse == true).Where(it => it.Id == coins.Id)
            .ExecuteCommandAsync();
        var mainCreate = Solnet.Wallet.Account.FromSecretKey(input.MainBuyer.PrivateKey);
        //写入记录
        var uc = new UserCoins()
        {
            UId = curUid,
            TokenName = Name,
            TokenSymbol = Symbol,
            TokenIcon = Icon,
            TokenAddress = Mint,
            TokenSecretKey = coins.SecretKey,
            SecretKey = mainCreate.PrivateKey,
            PubKey = mainCreate.PublicKey,
            BuySol = input.MainBuyer.Amount,
            IsPump = true,
            CreateTime = DateTime.Now
        };
        uc.Id = await _userCoinsRepository.AsInsertable(uc).ExecuteReturnIdentityAsync();
        //todo:以下逻辑强制不执行 2025 01 24
        outList = new List<(string pubkey, ulong outAmount)>();
        //if (outList.Count > 0) //有持仓
        //{
        //    var sum = outList.Aggregate(0UL, (acc, item) => acc + item.outAmount);
        //    //userToken 持仓
        //    var userToken = new UserTokens()
        //    {
        //        UId = curUid,
        //        TokenName = Name,
        //        TokenSymbol = Symbol,
        //        TokenIcon = Icon,
        //        TokenAddress = Mint,
        //        TokenAmount = Convert.ToDecimal(sum / Math.Pow(10, 6)),
        //        TokenAmountBuy = sum,
        //        TokenWalletCount = outList.Count,
        //        CreateTime = DateTime.Now
        //    };
        //    userToken.Id = await _userTokensRepository.AsInsertable(userToken).ExecuteReturnIdentityAsync();
        //    //userTrade 交易记录
        //    var userTrades = new List<UserTradeLog>();
        //    //userInventory 用户持仓
        //    var userInventory = new List<UserTokenInventory>();
        //    for (int i = 0; i < outList.Count; i++)
        //    {
        //        var ut = outList[i];
        //        var sut = dictSol[ut.pubkey];

        //        var uts = new UserTradeLog()
        //        {
        //            UId = curUid,
        //            TokenName = Name,
        //            TokenSymbol = Symbol,
        //            TokenIcon = Icon,
        //            TokenAddress = Mint,
        //            IsBuy = true,
        //            Sol = sut.Item2,
        //            TokenAmount = Convert.ToDecimal(ut.Item2 / Math.Pow(10, 6)),
        //            PubKey = ut.pubkey,
        //            SecretKey = sut.Item1,
        //            Signature = Sign,
        //            Status = TransferEnum.State_3,
        //            CreateTime = DateTime.Now,
        //            CompleteTime = DateTime.Now
        //        };
        //        userTrades.Add(uts);

        //        var uti = new UserTokenInventory()
        //        {
        //            UId = curUid,
        //            Mint = Mint,
        //            PublicKey = ut.pubkey,
        //            PrivateKey = sut.Item1,
        //            TokenAmount = Convert.ToDecimal(ut.Item2 / Math.Pow(10, 6)),
        //            IsInventory = true,
        //            CreateTime = DateTime.Now
        //        };
        //        userInventory.Add(uti);
        //    }

        //    //userTrade 交易记录
        //    await _userUserTradeLogRepository.AsInsertable(userTrades).ExecuteCommandAsync();
        //    //userInventory 用户持仓
        //    await _userTokenInventoryRepository.AsInsertable(userInventory).ExecuteCommandAsync();
        //    //userWallet 更新持仓
        //    var uw = outList.Select(it => it.pubkey).ToList();
        //    await _userWalletRepository.AsUpdateable().SetColumns(it => it.TokenNum == it.TokenNum + 1)
        //        .Where(it => it.UId == curUid && uw.Contains(it.PubKey))
        //        .ExecuteCommandAsync();
        //}

        //阻击交易
        var Model = new UserBatchTrade();
        if (input.Followers != null && input.Followers.MinAmount > 0
                                    && input.Followers.MaxAmount > 0
                                    && input.Followers.MinAmount <= input.Followers.MaxAmount)
        {
            //发布任务 处理购买
            // var accounts = await _userWalletRepository.AsQueryable()
            //     .Where(it => it.UId == curId)
            //     .WhereIF(input.Followers.Group > 0, it => it.GId == input.Followers.Group)
            //     .WhereIF(input.Followers.Tags > 0, it => SqlFunc.JsonArrayAny(it.TId, input.Followers.Tags))
            //     .ToListAsync();
            // for (int i = 0; i < accounts.Count; i++)
            // {
            //     //随机 金额
            //     var amount = Core.RandomHelper.GetRandomDouble((double)input.Followers.MinAmount,
            //         (double)input.Followers.MaxAmount);
            //     list.Add((accounts[i].SecretKey, amount));
            //     dictSol[accounts[i].PubKey] = (accounts[i].SecretKey, amount);
            // }

            //写入代币信息
            //var tokenInfo = new Tron.Abp.Multiplex.Solana.Dto.TokenInfo(Name, Symbol, Mint, 6, 0, Icon);
            //await _redisClient.HSetAsync(RedisKey.TokenInfoKey, Mint, JsonSerializer.Serialize(tokenInfo));
            //写 购买任务  消息
            //var ut = UserTask.CreateDefaultBuy(curUid, Mint, input.Followers.Group, input.Followers.Tags, input.Followers.MinAmount, input.Followers.MaxAmount);

            //ut.Id = await _userTransferTaskRepository.AsInsertable(ut).ExecuteReturnIdentityAsync();

            if (input.Followers.Group > 0 && !await _userGroupRepository.AsQueryable()
                    .AnyAsync(s => s.UId == curUid && s.Id == input.Followers.Group)) throw Oops.Bah("钱包分组不存在！");

            var Wallets = await _userWalletRepository.AsQueryable()
                .Where(s => s.UId == curUid && s.GId == input.Followers.Group).ToListAsync();
            if (Wallets.Count <= 0) throw Oops.Bah("选择的分组钱包数不足！");


            Model.Chain = "solana";
            Model.UId = curUid;
            Model.TokenName = Name;
            Model.TokenSymbol = Symbol;
            Model.TokenAddress = Mint;
            Model.TokenIcon = Icon;
            Model.IsBuy = true;
            Model.GroupId = input.Followers.Group;
            Model.Wallets = Wallets.Select(s => new Domain.WalletModel()
            { WalletId = s.Id, Name = s.Name, PubKey = s.PubKey, SecretKey = s.SecretKey }).ToList();
            Model.Status = 1;
            Model.CreateTime = DateTime.Now;
            Model.Wallets = Wallets.Select(s => new Domain.WalletModel()
            { WalletId = s.Id, Name = s.Name, PubKey = s.PubKey, SecretKey = s.SecretKey }).ToList();
            Model.TradeModel = 1;
            Model.Gas = 0;
            Model.Mev = 0;
            Model.Slippage = 10;
            Model.ExpiredAuto = false;
            Model.AmountData = new AmountModel
            {
                Type = 1,
                AmountRange = new List<decimal?>() { input.Followers.MinAmount, input.Followers.MaxAmount, 3 }
            };
            Model.StoplimitData = new StopLimitModel() { LimitModel = 1 };
            Model.Id = await _userBatchTradeRepository.AsInsertable(Model).ExecuteReturnIdentityAsync();
            if (Model.Id < 0) throw Oops.Bah("入库失败！");
        }

        //捆绑交易
        string? bindData = null;
        if (bindWallet != null)
        {
            var buylist = new Dictionary<string, decimal>();
            foreach (var item in bindWallet.Where(s => !list.Select(s => s.Item1).Contains(s.SecretKey)).ToList())
            {
                var solAmount = GetRandomAmount(input.Bindowers.MinAmount, input.Bindowers.MaxAmount);
                buylist.Add(item.SecretKey, solAmount);
            }

            bindData = JsonSerializer.Serialize(buylist);
        }

        var dict = new Dictionary<string, string>()
        {
            //用户ID-》任务 ID
            //[$"{curUid}"]=uttr.Id.ToString(),
            ["uid"] = curUid.ToString(),
            ["Id"] = Model.Id > 0 ? Model.Id.ToString() : "0",
            ["bindData"] = bindData,
        };
        //发送消息
        //await _redisClient.XAddAsync(StreamKey.StreamTransfer, dict);
        //增加狙击任务
        await _redisClient.HSetAsync(RedisKey.SnipePumpKey, Mint, JsonSerializer.Serialize(dict));

        return !string.IsNullOrWhiteSpace(Sign);
    }

    private decimal GetRandomAmount(decimal min, decimal max, decimal step = 3)
    {
        var min_val = (int)Math.Truncate(min * (int)Math.Pow(10, (int)step));
        var max_val = (int)Math.Truncate(max * (int)Math.Pow(10, (int)step));
        var val = (decimal)(Volo.Abp.RandomHelper.GetRandom(min_val, max_val) / Math.Pow(10, (int)step));
        return val;
    }

    /// <summary>
    /// 购买代币
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("/trade/buy")]
    public async Task<bool> TokenBuy(TokenBuyInput input)
    {
        var curUid = (int)_userManager.UserId;
        if (string.IsNullOrWhiteSpace(input.Mint)) throw Oops.Bah("请输入代币地址!");
        if (input.AmountMin <= 0 || input.AmountMax <= 0 || input.AmountMin > input.AmountMax)
            throw Oops.Bah("金额输入错误！");

        var Wallets = new List<UserWallet>();
        if (input.GId < 0) throw Oops.Bah("请选择分组!");
        if (input.GId > 0 && await _userGroupRepository.AsQueryable().Where(s => s.UId == curUid && s.Id == input.GId)
                .FirstAsync() == null) throw Oops.Bah("请选择分组!");

        var wallet = await _userWalletRepository.AsQueryable().Where(s => s.UId == curUid && s.GId == input.GId)
            .ToListAsync();
        Wallets.AddRange(wallet);

        var tokenInfo = await _solanaApi.GetMint(input.Mint);
        var Model = new UserBatchTrade()
        {
            Chain = "Solana",
            UId = curUid,
            TokenAddress = input.Mint,
            TokenName = tokenInfo?.Name ?? "-",
            TokenSymbol = tokenInfo?.Symbol ?? "-",
            TokenIcon = tokenInfo?.Icon ?? "-",
            Slippage = Convert.ToInt32(input.Slippage),
            TradeModel = input.IsMEV ? 3 : 1,
            Gas = input.Gas,
            Mev = input.Mev,
            Status = 1,
            GroupId = input.GId,
            Wallets = Wallets.Select(s => new Domain.WalletModel()
            { WalletId = s.Id, Name = s.Name, PubKey = s.PubKey, SecretKey = s.SecretKey }).ToList(),
            StoplimitData = new StopLimitModel() { LimitModel = 1 },
            CreateTime = DateTime.Now,
            IsBuy = true,
            ExpiredAuto = false,
            AmountData = new AmountModel
            {
                Type = 1,
                AmountRange = new List<decimal?>() { input.AmountMin, input.AmountMax, 3 }
            }
        };

        Model = await _userBatchTradeRepository.AsInsertable(Model).ExecuteReturnEntityAsync();
        if (Model.Id < 0) throw Oops.Bah("入库失败！");

        //TODO: 发送消息创建任务
        var dictPrice = new Dictionary<string, string>()
        {
            ["Id"] = $"{Model.Id}",
        };
        await _redisClient.XAddAsync(StreamKey.BatchTradeStream, dictPrice);

        return true;
    }

    /// <summary>
    /// 获取发射代币列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Route("/user/gettokenlist")]
    public async Task<List<Dto.UserCoinsOutput>> GetTokenList()
    {
        var curId = (int)_userManager.UserId;

        var list = await _userCoinsRepository.AsQueryable().Where(it => it.UId == curId)
            .OrderByDescending(it => it.CreateTime).ToListAsync();
        var outList = list.Adapt<List<Dto.UserCoinsOutput>>();
        foreach (var output in outList)
        {
            var tokenInfo = await _solanaApi.GetMintPoolData(output.TokenAddress);
            if (tokenInfo != null)
            {
                output.Volume24h = tokenInfo.Volume24h ?? 0;
                output.HolderCount = tokenInfo.BaseAsset.HolderCount ?? 0;
                output.Mcap = tokenInfo.BaseAsset.Mcap ?? 0;
                output.UsdPrice = tokenInfo.BaseAsset.UsdPrice ?? 0;
            }
        }

        return outList;
    }

    /// <summary>
    /// 卖出代币 来自创建者
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("/user/tokensellbyplayer")]
    public async Task<bool> TokenSellByPlayer(TokenSellByPlayerInput input)
    {
        var curUid = (int)_userManager.UserId;

        if (input.Id <= 0) throw Oops.Bah("请选择要清仓的代币");
        var uc = await _userCoinsRepository.AsQueryable().Where(it => it.UId == curUid && it.Id == input.Id)
            .FirstAsync();
        if (uc == null) throw Oops.Bah("代币不存在");

        var ut = UserTask.CreateTokenSellByPlayer(curUid, uc.TokenAddress, uc.SecretKey);
        ut.Id = await _userTransferTaskRepository.AsInsertable(ut).ExecuteReturnIdentityAsync();
        var dict = new Dictionary<string, string>()
        {
            //用户ID-》任务 ID
            //[$"{curUid}"]=uttr.Id.ToString(),
            ["uid"] = curUid.ToString(),
            ["tid"] = ut.Id.ToString()
        };
        //发送消息
        await _redisClient.XAddAsync(StreamKey.StreamTransfer, dict);
        return true;
    }

    #endregion

    #region 跟单

    /// <summary>
    /// 获取跟单列表
    /// </summary>
    [HttpGet]
    [Route("/user/smarttrade/list")]
    public async Task<List<UserSmartDto>> GetSmartTradeList()
    {
        var curUid = (int)_userManager.UserId;
        var list = await _userSmartTradeRepository.AsQueryable()
            .Where(it => it.UId == curUid)
            .OrderByDescending(it => it.CreateTime)
            .ToListAsync();
        var data = list.Adapt<List<Dto.UserSmartDto>>();

        return data;
    }

    /// <summary>
    /// 添加跟单
    /// </summary>
    [HttpPost]
    [Route("/user/smarttrade/add")]
    public async Task<bool> AddSmartTrade(UserSmartInputDto input)
    {
        var curUid = (int)_userManager.UserId;

        // 验证
        if (string.IsNullOrWhiteSpace(input.Name)) throw Oops.Bah("跟单名称不能为空");
        if (string.IsNullOrWhiteSpace(input.SmartWallet)) throw Oops.Bah("聪明钱包地址不能为空");
        if (!Solnet.Wallet.PublicKey.IsValid(input.SmartWallet)) throw Oops.Bah("聪明钱包地址格式错误");
        if (input.WalletId <= 0) throw Oops.Bah("请选择跟单钱包");

        var myWallet = await _userMyWalletRepository.AsQueryable()
            .FirstAsync(s => s.Id == input.WalletId && s.UId == curUid);
        if (myWallet == null) throw Oops.Bah("跟单钱包不存在");


        // 检查名称是否重复
        var exists = await _userSmartTradeRepository.AsQueryable()
            .Where(it => it.UId == curUid && it.Name == input.Name)
            .AnyAsync();
        if (exists) throw Oops.Bah("跟单名称已存在");

        // 检查名称是否重复
        var existsAddr = await _userSmartTradeRepository.AsQueryable()
            .Where(it => it.UId == curUid && it.SmartWallet == input.SmartWallet)
            .AnyAsync();
        if (existsAddr) throw Oops.Bah("跟单钱包地址已存在");


        var data = input.Adapt<UserSmartTrade>();
        data.UId = curUid;
        data.PubKey = myWallet.PubKey;
        data.SecretKey = myWallet.SecretKey;
        data.Status = 0;
        data.CreateTime = DateTime.Now;

        await _userSmartTradeRepository.AsInsertable(data).ExecuteReturnIdentityAsync();
        return true;
    }

    /// <summary>
    /// 修改跟单
    /// </summary>
    [HttpPost]
    [Route("/user/smarttrade/update")]
    public async Task<bool> UpdateSmartTrade(UserSmartInputUpdateDto input)
    {
        var curUid = (int)_userManager.UserId;

        if (input.Id <= 0) throw Oops.Bah("跟单ID不能为空");
        if (string.IsNullOrWhiteSpace(input.Name)) throw Oops.Bah("跟单名称不能为空");
        if (string.IsNullOrWhiteSpace(input.SmartWallet)) throw Oops.Bah("聪明钱包地址不能为空");
        if (!Solnet.Wallet.PublicKey.IsValid(input.SmartWallet)) throw Oops.Bah("聪明钱包地址格式错误");
        if (input.WalletId <= 0) throw Oops.Bah("请选择跟单钱包");

        var myWallet = await _userMyWalletRepository.AsQueryable()
            .FirstAsync(s => s.Id == input.WalletId && s.UId == curUid);
        if (myWallet == null) throw Oops.Bah("跟单钱包不存在");

        // 检查是否存在
        var smartTrade = await _userSmartTradeRepository.AsQueryable()
            .Where(it => it.Id == input.Id && it.UId == curUid)
            .FirstAsync();
        if (smartTrade == null) throw Oops.Bah("跟单不存在");

        // 检查是否在运行中
        if (smartTrade.Status == 1) throw Oops.Bah("跟单正在运行中，请先停止");

        // 检查名称是否重复(排除自身)
        var exists = await _userSmartTradeRepository.AsQueryable()
            .Where(it => it.UId == curUid && it.Name == input.Name && it.Id != input.Id)
            .AnyAsync();
        if (exists) throw Oops.Bah("跟单名称已存在");

        // 只更新允许修改的字段
        var result = await _userSmartTradeRepository.AsUpdateable()
            .SetColumns(it => new UserSmartTrade
            {
                Name = input.Name,
                SmartWallet = input.SmartWallet,
                WalletId = input.WalletId,
                PubKey = myWallet.PubKey,
                SecretKey = myWallet.SecretKey,
                BuyOnce = input.BuyOnce,
                BuyType = input.BuyType,
                MaxBuy = input.MaxBuy,
                BuyValue = input.BuyValue ?? 0,
                CustomBuy = input.CustomBuy,
                SellType = input.SellType,
                SellValue = input.SellValue ?? 0,
                CustomSell = input.CustomSell
            })
            .Where(it => it.Id == input.Id && it.UId == curUid)
            .ExecuteCommandHasChangeAsync();

        return result;
    }

    /// <summary>
    /// 删除跟单
    /// </summary>
    [HttpPost]
    [Route("/user/smarttrade/delete")]
    public async Task<bool> DeleteSmartTrade(int id)
    {
        var curUid = (int)_userManager.UserId;

        if (id <= 0) throw Oops.Bah("跟单ID不能为空");

        // 检查是否存在
        var smartTrade = await _userSmartTradeRepository.AsQueryable()
            .Where(it => it.Id == id && it.UId == curUid)
            .FirstAsync();
        if (smartTrade == null) throw Oops.Bah("跟单不存在");

        // 检查是否在运行中
        if (smartTrade.Status == 1) throw Oops.Bah("跟单正在运行中，请先停止");

        var result = await _userSmartTradeRepository.DeleteAsync(it => it.Id == id && it.UId == curUid);
        return result;
    }

    /// <summary>
    /// 更新跟单状态
    /// </summary>
    [HttpPost]
    [Route("/user/smarttrade/updatestatus")]
    public async Task<bool> UpdateSmartTradeStatus(int id, int status)
    {
        var curUid = (int)_userManager.UserId;

        if (id <= 0) throw Oops.Bah("跟单ID不能为空");
        if (status != 0 && status != 1) throw Oops.Bah("状态值错误");

        // 检查是否存在
        var smartTrade = await _userSmartTradeRepository.AsQueryable()
            .Where(it => it.Id == id && it.UId == curUid)
            .FirstAsync();
        if (smartTrade == null) throw Oops.Bah("跟单不存在");

        var result = await _userSmartTradeRepository.AsUpdateable()
            .SetColumns(it => it.Status == status)
            .Where(it => it.Id == id && it.UId == curUid)
            .ExecuteCommandHasChangeAsync();

        var dict = new Dictionary<string, string>()
        {
            //用户ID-》任务 ID
            //[$"{curUid}"]=uttr.Id.ToString(),
            ["uid"] = curUid.ToString(),
            ["tid"] = smartTrade.Id.ToString(),
            ["status"] = status.ToString()
        };
        //发送消息
        await _redisClient.XAddAsync(StreamKey.SmartWalletStream, dict);

        return result;
    }

    /// <summary>
    /// 获取配置
    /// </summary>
    [HttpGet]
    [Route("/user/usersmart/get")]
    public async Task<UserConfig> GetUserSmart()
    {
        var curUid = (int)_userManager.UserId;
        var list = await _userConfigRepository.AsQueryable()
            .Where(it => it.UId == curUid)
            .FirstAsync();
        return list;
    }

    /// <summary>
    /// 设置配置
    /// </summary>
    [HttpPost]
    [Route("/user/usersmart/set")]
    public async Task<bool> SetUserSmart(UserSmartSetDto input)
    {
        var curUid = (int)_userManager.UserId;
        var list = await _userConfigRepository.AsQueryable()
            .Where(it => it.UId == curUid)
            .FirstAsync();

        if (list == null) throw Oops.Bah("配置不存在");

        await _userConfigRepository.AsUpdateable().SetColumns(s => s.Slippage == input.Slippage)
            .SetColumns(s => s.BuyGas == input.BuyGas)
            .SetColumns(s => s.SellGas == input.SellGas)
            .SetColumns(s => s.Mev == input.Mev)
            .Where(s => s.Id == list.Id)
            .ExecuteCommandAsync();

        return true;
    }

    /// <summary>
    ///获取代币黑名单
    /// </summary>
    [HttpPost]
    [Route("/user/get/tokenblack")]
    public async Task<List<string>> GetUserTokenBlacklist(UserSmartSetDto input)
    {
        var curUid = (int)_userManager.UserId;
        var list = await _userConfigRepository.AsQueryable()
            .Where(it => it.UId == curUid)
            .FirstAsync();
        if (list == null)
        {
            list = new UserConfig()
            {
                UId = curUid,
                Mev = 0.01M,
                BuyGas = 0.01M,
                SellGas = 0.01M,
                Slippage = 10M,
                TokenBlackList = new List<string>()
            };
            await _userConfigRepository.InsertAsync(list);
        }

        return list.TokenBlackList;
    }

    /// <summary>
    /// 设置代币黑名单
    /// </summary>
    [HttpPost]
    [Route("/user/set/tokenblack")]
    public async Task<bool> SetUserTokenBlacklist(UserSmartSetDto input)
    {
        var curUid = (int)_userManager.UserId;
        var list = await _userConfigRepository.AsQueryable()
            .Where(it => it.UId == curUid)
            .FirstAsync();

        if (list == null) throw Oops.Bah("配置不存在");


        await _userConfigRepository.AsUpdateable()
            .SetColumns(s => s.TokenBlackList == input.TokenBlacklist)
            .Where(s => s.Id == list.Id)
            .ExecuteCommandAsync();

        //写入redis 黑名单
        await _redisClient.HSetAsync(RedisKey.CopyTradeBlackListing, curUid.ToString(), input.TokenBlacklist);

        return true;
    }

    #endregion

    /// <summary>
    /// 测试捆绑交易
    /// </summary>
    [HttpGet]
    [Route("/token/bindtrade")]
    public async Task<bool> GetSmartTradeList(string mint, string key)
    {
        //if (key != "123.456") return false;
        //var bindDict = new Dictionary<string, string>()
        //{
        //    ["bindData"] = "{\"Group\":23,\"MinAmount\":0.001,\"MaxAmount\":0.001}",
        //    ["uid"] = "100001",
        //    ["mint"] = mint,
        //    ["DexType"] = DexType.Pumpfun,
        //};
        //await _redisClient.XAddAsync(StreamKey.BindTradeStream, bindDict);

        return true;
    }
}