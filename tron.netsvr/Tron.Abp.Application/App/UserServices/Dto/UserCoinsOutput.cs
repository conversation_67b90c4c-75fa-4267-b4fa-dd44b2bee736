namespace Tron.Abp.Application.App.UserServices.Dto;

public class UserCoinsOutput
{
    public int Id { get; set; }
    public int UId { get; set; }
    public string TokenName { get; set; }
    public string TokenSymbol { get; set; }
    public string TokenIcon { get; set; }
    public string TokenAddress { get; set; }
    public string PubKey { get; set; }
    public decimal BuySol { get; set; } = 0;
    public string DexType { get; set; } 
    public decimal Volume24h { get; set; }
    public int HolderCount { get; set; }
    public decimal Mcap { get; set; }
    public decimal UsdPrice { get; set; }
    public DateTime CreateTime { get; set; } 
}