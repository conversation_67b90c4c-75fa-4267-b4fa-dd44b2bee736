namespace Tron.Abp.Application.App.UserServices.Dto;

public class TokenCreateInput
{
    public string Name { get; set; } 
    public string Symbol { get; set; } 
    public string Description { get; set; } 
    public string Logo { get; set; }
    public string twitter { get; set; }
    public string telegram { get; set; }
    public string website { get; set; }

    // 主买入人信息
    public MainAccount MainBuyer { get; set; } 

    // 阻击买人信息
    public BuyAccount? Followers { get; set; }

    // 捆绑买人信息
    public BuyAccount? Bindowers { get; set; }


    public class MainAccount
    {
        public int WalletId { get; set; } = 0;
        public string? PrivateKey { get; set; }
        public decimal Amount { get; set; } = 0;
    }

    public class BuyAccount
    {
        public int Group { get; set; } = 0;
        public decimal MinAmount { get; set; } = 0;
        public decimal MaxAmount { get; set; } = 0;
    }
}