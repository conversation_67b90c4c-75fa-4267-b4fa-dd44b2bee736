namespace Tron.Abp.Application.App.UserServices.Dto;

public class MultipleToOneInput
{
    public string Chain { get; set; } = "solana";
    /// <summary>
    ///  类型 0分组 2导入
    /// </summary>
    public int TranType { get; set; } = 0;
    /// <summary>
    /// 类型对应值
    /// </summary>
    public string TranData { get; set; }
    
    public string ToPublicKey { get; set; }
    /// <summary>
    /// 百分比
    /// </summary>
    public decimal Percentage { get; set; }
}

public class MultipleToOneDtoInput
{
    public string Chain { get; set; } = "solana";
    /// <summary>
    ///  类型 0分组 2导入
    /// </summary>
    public int TranType { get; set; } = 0;
    /// <summary>
    /// 类型对应值
    /// </summary>
    public string TranData { get; set; }

    public string ToAddressAddMode { get; set; } = "wallet";
    public int? ToWalletId { get; set; }
    public string? ToWalletAddress { get; set; } = "";
    /// <summary>
    /// 百分比
    /// </summary>
    public decimal Percentage { get; set; }
}