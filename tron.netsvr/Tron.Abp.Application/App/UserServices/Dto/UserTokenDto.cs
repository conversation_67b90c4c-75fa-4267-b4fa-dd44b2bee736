using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Tron.Abp.Application.App.UserServices.Dto
{
    /// <summary>
    /// 用户代币DTO
    /// </summary>
    public class UserTokenDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int UId { get; set; }

        /// <summary>
        /// 代币名称
        /// </summary>
        public string TokenName { get; set; }
        /// <summary>
        /// 代币缩写
        /// </summary>
        public string TokenSymbol { get; set; }
        /// <summary>
        /// 代币地址
        /// </summary>
        public string TokenAddress { get; set; }
        /// <summary>
        /// 代币Icon
        /// </summary>
        public string TokenIcon { get; set; }

        public decimal UnrealizedProfit { get; set; } = 0;
        public decimal RealizedProfit { get; set; } = 0;
        public decimal TotalProfit => this.UnrealizedProfit + this.RealizedProfit;

        /// <summary>
        /// 代币余额
        /// </summary>
        [SugarColumn(ColumnDescription = "代币余额", ColumnDataType = "decimal(18,6)", IsNullable = true)]
        public decimal TokenAmount { get; set; }
        /// <summary>
        /// 代币总买入
        /// </summary>
        [SugarColumn(ColumnDescription = "代币总买入", ColumnDataType = "decimal(18,6)", IsNullable = true)]
        public decimal TokenAmountBuy { get; set; }
        /// <summary>
        /// 代币总卖出
        /// </summary>
        [SugarColumn(ColumnDescription = "代币总卖出", ColumnDataType = "decimal(18,6)", IsNullable = true)]
        public decimal TokenAmountSell { get; set; }
        /// <summary>
        /// 所属分组名称
        /// </summary>
        [SugarColumn(ColumnDescription = "所属分组名称", ColumnDataType = "text", IsNullable = true, IsJson = true)]
        public List<string> GName { get; set; }
        /// <summary>
        /// 所属标签名称
        /// </summary>
        [SugarColumn(ColumnDescription = "所属标签名称", ColumnDataType = "text", IsNullable = true, IsJson = true)]
        public List<string> TName { get; set; }
        /// <summary>
        /// 持仓钱包数量
        /// </summary>
        public int TokenWalletCount { get; set; } = 0;
    }
}
