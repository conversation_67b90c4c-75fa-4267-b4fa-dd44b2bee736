using Jaina;
using Microsoft.AspNetCore.Mvc.Diagnostics;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Tron.Abp.Application.AppEventHandlers.Dto;
using Tron.Abp.Domain;
using Tron.Abp.Domain.Contracts.Enum;
using Tron.Abp.Multiplex.Solana;
using Tron.Abp.SqlSugar;
using Volo.Abp.DependencyInjection;

namespace Tron.Abp.Application.AppEventHandlers;

public class TransferEventSubscriber : IEventSubscriber, ISingletonDependency
{
    private readonly SqlSugarRepository<UserTransferLog> _userTransferLogRepository;
    public ILogger<TransferEventSubscriber> Logger { get; set; }

    public TransferEventSubscriber(SqlSugarRepository<UserTransferLog> userTransferLogRepository)
    {
        _userTransferLogRepository = userTransferLogRepository;
        Logger = NullLogger<TransferEventSubscriber>.Instance;
    }


    [EventSubscribe("TransferToDb")]
    public async Task TransferToDbOnEventAsync(EventHandlerExecutingContext context)
    {
        var es = context.Source;
        var ev = (TransferToDbEvent)es.Payload;
        await _userTransferLogRepository.CopyNew().AsUpdateable()
            .SetColumns(it => it.CompleteTime == DateTime.Now)
            .SetColumns(it => it.Signature == ev.Sign)
            .SetColumns(it => it.Status == TransferEnum.State_3)
            .Where(it => it.Id == ev.Id)
            .ExecuteCommandAsync();
    }
}