<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire.Redis.StackExchange" Version="1.9.4" />
    <PackageReference Include="Volo.Abp.AutoMapper" Version="8.3.4" />
    <PackageReference Include="Volo.Abp.BackgroundWorkers.Hangfire" Version="8.3.4" />
    <PackageReference Include="Volo.Abp.Ddd.Application" Version="8.3.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Cryptography.Obfuscation\Cryptography.Obfuscation.csproj" />
    <ProjectReference Include="..\Jaina.EventBus\Jaina.EventBus.csproj" />
    <ProjectReference Include="..\Tron.Abp.Application.Contracts\Tron.Abp.Application.Contracts.csproj" />
    <ProjectReference Include="..\Tron.Abp.Domain\Tron.Abp.Domain.csproj" />
    <ProjectReference Include="..\Tron.Abp.Multiplex.Contracts\Tron.Abp.Multiplex.Contracts.csproj" />
    <ProjectReference Include="..\Tron.Abp.Multiplex\Tron.Abp.Multiplex.csproj" />
    <ProjectReference Include="..\Tron.Abp.Nats\Tron.Abp.Nats.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="SysEventHandlers\" />
  </ItemGroup>

</Project>
