using System.Collections.Specialized;
using System.Text;
using System.Text.Json;
using System.Threading.Channels;
using Solana_gRpcApp.Model;
using WatsonWebsocket;

namespace Solana_gRpcApp.BackgroundWorker;


public class JupAgWorker : BackgroundWorkerBase
{
   
    private IWebSocketClient _webSocketClient;
    private readonly WorkQueue<Model. PoolData> _workQueue;
    private readonly Channel<Model. PoolData> _channel; 
    public JupAgWorker()
    {

        _workQueue = new WorkQueue< PoolData>(Processor, 10);
        _channel = Channel.CreateUnbounded<Model. PoolData>(); 
    }
    /// <summary>
    /// websocket 消息处理 N=>1
    /// </summary>
    /// <param name="bytes"></param>
    private async Task OnMessageReceived(byte[] bytes)
    {
        var str = Encoding.UTF8.GetString(bytes);
        // 配置反序列化选项：忽略大小写，支持宽松解析
        var options = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true, // 忽略属性名大小写
            AllowTrailingCommas = true // 允许尾随逗号
        };
        var result = JsonSerializer.Deserialize<Model.JupAgStreamResult>(str,options);
        if (result == null || result.Data == null || result.Data.Count <= 0) return;
        for (int i = 0; i < result.Data.Count; i++)
        {
            await _channel.Writer.WriteAsync(result.Data[i].Pool); // 写入通道
        } 
    }
    /// <summary>
    /// Channel消息处理 1=>1
    /// </summary>
    private async Task ProcessMessages()  
    {  
        // 读取并处理通道中的消息  
        await foreach (var result in _channel.Reader.ReadAllAsync())  
        {  
            //装入队列
            _workQueue.Enqueue(result);
        }  
    } 
    /// <summary>
    /// WorkQueue 消息处理 1=>N
    /// </summary>
    /// <param name="pool"></param>
    private async Task Processor( PoolData pool)
    {
        Console.WriteLine($"{pool.Dex} {pool.BaseAsset.Id} {pool.Id} ");
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        NameValueCollection header = new NameValueCollection();
        // 使用 Add 方法添加键值对
        //header.Add("Content-Type", "application/json");
        header.Add("Origin", "https://jup.ag");
        header.Add("Connection","Upgrade");
        header.Add("Sec-Websocket-Version","13");
        header.Add("Sec-Websocket-Extensions","permessage-deflate;client_max_window_bits");
        header.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0");
        _webSocketClient =/*new WatsonWsClient(new Uri("wss://trench-stream.jup.ag/ws"));*/ new WebSocketClient("wss://trench-stream.jup.ag/ws",header);

        _webSocketClient.OnConnected += async () =>
        {
            //连接成功 发送订阅消息
            /*
             * {"type":"subscribe:recent"}
             * {"type":"subscribe:pool","pools":[]}
             * {"type":"subscribe:txns","assets":[]}
             */
            var recent = "{\"type\":\"subscribe:recent\"}";
            var pool = "{\"type\":\"subscribe:pool\",\"pools\":[]}";
            var txns = "{\"type\":\"subscribe:txns\",\"assets\":[]}";
            await _webSocketClient.SendStringAsync(recent);
            await Task.Delay(1, cancellationToken);
            await _webSocketClient.SendStringAsync(pool);
            await Task.Delay(1, cancellationToken);
            await _webSocketClient.SendStringAsync(txns);
        };
        _webSocketClient.OnDisconnected += (e) => { };
        _webSocketClient.OnMessageReceived += async (e) => await OnMessageReceived(e);
        await _webSocketClient.ConnectAsync();
        // _webSocketClient.ServerConnected += OnWebSocketClientOnServerConnected;
        // _webSocketClient.ServerDisconnected += OnWebSocketClientOnServerDisconnected;
        // _webSocketClient.MessageReceived += OnWebSocketClientOnMessageReceived;
        // 创建一个新的 NameValueCollection 实例
        
        //await _webSocketClient.StartAsync(header);
        // 启动处理任务  
        _ = ProcessMessages(); // 启动消费者
        await base.StartAsync(cancellationToken);
    }

    private async void OnWebSocketClientOnMessageReceived(object? sender, MessageReceivedEventArgs args)
    {
        
        var str = Encoding.UTF8.GetString(args.Data);
        var result = JsonSerializer.Deserialize<Model.JupAgStreamResult>(str);
        if (result == null || result.Data == null || result.Data.Count <= 0) return;
        for (int i = 0; i < result.Data.Count; i++)
        {
            await _channel.Writer.WriteAsync(result.Data[i].Pool); // 写入通道
        } 
    }

    private async void OnWebSocketClientOnServerDisconnected(object? sender, EventArgs args)
    {
    }

    private async void OnWebSocketClientOnServerConnected(object? sender, EventArgs args)
    {
        var recent = "{\"type\":\"subscribe:recent\"}";
        var pool = "{\"type\":\"subscribe:pool\",\"pools\":[]}";
        var txns = "{\"type\":\"subscribe:txns\",\"assets\":[]}";
        /*await _webSocketClient.SendAsync(recent);
        await Task.Delay(100);
        await _webSocketClient.SendAsync(pool);
        await Task.Delay(100);
        await _webSocketClient.SendAsync(txns);*/
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        _channel.Writer.Complete(); // 完成通道写入
        await _workQueue.Stop();
        //await _webSocketClient.StopAsync();
        _webSocketClient.Dispose();
        await base.StopAsync(cancellationToken);
    }
}