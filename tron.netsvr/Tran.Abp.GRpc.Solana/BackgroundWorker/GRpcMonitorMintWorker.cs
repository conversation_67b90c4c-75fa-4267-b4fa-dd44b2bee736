using Google.Protobuf;
using Solnet.Programs;
using Solnet.Wallet;

namespace Tran.Abp.GRpc.Solana.BackgroundWorker;

public class GRpcMonitorMintWorker : BackgroundWorkerBase
{
    public ILogger<GRpcMonitorMintWorker> Logger { get; set; }
    private readonly Geyser.GeyserClient client;
    private DateTime startTime;
    private AsyncDuplexStreamingCall<SubscribeRequest, SubscribeUpdate> stream;
    private IRedisClient _redisClient;
    private SubscribeStreamObject streamRedis;
    private SubscribeRequest request;

    public GRpcMonitorMintWorker(Geyser.GeyserClient client, IRedisClient redisClient)
    {
        this.client = client;
        _redisClient = redisClient;
        Logger = NullLogger<GRpcMonitorMintWorker>.Instance;
        startTime = DateTime.Now;
    }

    private async Task DoWorkAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        request = new SubscribeRequest()
        {
            Commitment = CommitmentLevel.Processed
        };

        //GRPC 订阅
        stream = client.Subscribe();
        Task.Run(() => OnSubscribe(cancellationToken), cancellationToken);

        //redis 订阅
        streamRedis =
            _redisClient.SubscribeStream(StreamKey.SolanaMonitorMintStream, onMessage: OnRedisSubscribeStreamMessage);

        if (await _redisClient.HLenAsync(RedisKey.SolanaMonitorMint) > 0)
        {
            var dict = await _redisClient.HGetAllAsync(RedisKey.SolanaMonitorMint);
            foreach (var mint in dict)
            {
                if (mint.Value == "1") await SendRequest(mint.Key, true);
            }
        }
    }

    private async Task SendRequest(string mint, bool isAdd = true)
    {
        if (isAdd && request.Transactions.ContainsKey(mint) == false)
        {
            Logger.LogDebug($"添加订阅交易{mint}");
            request.Transactions.Add(mint, new SubscribeRequestFilterTransactions()
            {
                Vote = false,
                Failed = false,
                AccountInclude = { mint },
                AccountExclude = { Solnet.AABot.AABotProgram.ID_MainNet }, //排除aabot交易
                AccountRequired = { },
            });
            await stream.RequestStream.WriteAsync(request);
        }
        else if (!isAdd && request.Transactions.ContainsKey(mint) == true)
        {
            Logger.LogDebug($"移除订阅交易{mint}");
            request.Transactions.Remove(mint);
            await stream.RequestStream.WriteAsync(request);
        }
    }

    private async Task SendUnSubscribeRequest()
    {
        request.Transactions.Clear();
        await stream.RequestStream.WriteAsync(request);
    }

    private async void OnRedisSubscribeStreamMessage(Dictionary<string, string> obj)
    {
        var mint = obj["mint"];
        var status = int.Parse(obj["status"]);
        Logger.LogDebug($"收到订阅消息 开始处理交易{mint} {status}");
        if (await _redisClient.HExistsAsync(RedisKey.SolanaMonitorMint, mint))
        {
            await _redisClient.HSetAsync(RedisKey.SolanaMonitorMint, mint, status);
        }

        await SendRequest(mint, status == 1);
    }

    private async Task OnSubscribe(CancellationToken cancellationToken)
    {
        while (await stream.ResponseStream.MoveNext(cancellationToken))
        {
            var data = stream.ResponseStream.Current;
            if (data.Transaction != null)
            {
                var mint = data.Filters[0];
                var sign = Base58.Encode(data.Transaction.Transaction.Signature.ToByteArray());
                //Console.WriteLine($"{mint}=>{sign}");
                var transaction = data.Transaction.Transaction;
                //所有账户
                var accountKeys = transaction.Transaction.Message.AccountKeys
                    .Select(k => Base58.Encode(k.ToByteArray()))
                    .ToList();
                if (transaction.Meta.LoadedWritableAddresses.Count > 0)
                {
                    accountKeys.AddRange(
                        transaction.Meta.LoadedWritableAddresses.Select(k => Base58.Encode(k.ToByteArray())));
                }

                if (transaction.Meta.LoadedReadonlyAddresses.Count > 0)
                {
                    accountKeys.AddRange(
                        transaction.Meta.LoadedReadonlyAddresses.Select(k => Base58.Encode(k.ToByteArray())));
                }

                var accountKeysArray = accountKeys.ToArray();
                try
                {
                    var (isBuy, solAmount, tokenAmount) = (false, 0m, 0m);
                    var transactionParser = new Parser.TransactionParser();
                    if (accountKeys.Contains(ProgramIds.LaunchpadProgramId))
                    {
                        var programIndex = Array.IndexOf(accountKeysArray, ProgramIds.LaunchpadProgramId);
                        if (programIndex > 0)
                        {
                            var tradeData = transaction.Transaction.Message.Instructions
                                .Where(it => it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 1)
                                .Select(it => it.Data.ToByteArray()).FirstOrDefault();
                            if (tradeData == null)
                            {
                                tradeData = transaction.Meta.InnerInstructions.SelectMany(it => it.Instructions)
                                    .Where(it =>
                                        it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 1)
                                    .Select(it => it.Data.ToByteArray()).FirstOrDefault();
                            }

                            if (tradeData != null)
                            {
                                var byteData = new ReadOnlySpan<byte>(tradeData);
                                var offset = 8 + 8;
                                var poolId = Base58.Encode(byteData.Slice(offset, 32).ToArray());
                                offset += 32;
                                offset += 8 * 7;
                                var amountIn = BinaryPrimitives.ReadUInt64LittleEndian(byteData.Slice(offset, 8));
                                offset += 8;
                                var amountOut = BinaryPrimitives.ReadUInt64LittleEndian(byteData.Slice(offset, 8));
                                offset += 8;
                                offset += 8 * 3;
                                isBuy = byteData[offset] == 0;
                                offset += 1;

                                var decimals = isBuy switch
                                {
                                    true => transaction.Meta.PostTokenBalances.FirstOrDefault(it => it.Mint == mint)
                                        ?.UiTokenAmount.Decimals ?? 0,
                                    false => transaction.Meta.PreTokenBalances.FirstOrDefault(it => it.Mint == mint)
                                        ?.UiTokenAmount.Decimals ?? 0,
                                };
                                solAmount = isBuy ? (decimal)amountIn / 1e9m : (decimal)amountOut / 1e9m;
                                tokenAmount = isBuy
                                    ? (decimal)amountOut / (decimal)(Math.Pow(10, decimals))
                                    : (decimal)amountIn / (decimal)(Math.Pow(10, decimals));
                                var price = solAmount / tokenAmount;
                                var user = accountKeys[0];
                                Logger.LogDebug(
                                    $"[launchpad]{mint}=>{user} {solAmount} {tokenAmount} {isBuy}  {price} {sign}");
                            }
                        }
                    }
                    else if (accountKeysArray.Contains(ProgramIds.PumpFunProgramId))
                    {
                        //解析pump fun 交易
                        var programIndex = Array.IndexOf(accountKeysArray, ProgramIds.PumpFunProgramId);
                        if (programIndex > 0)
                        {
                            var tradeData = transaction.Transaction.Message.Instructions
                                .Where(it => it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 1)
                                .Select(it => it.Data.ToByteArray()).FirstOrDefault();
                            if (tradeData == null)
                            {
                                tradeData = transaction.Meta.InnerInstructions.SelectMany(it => it.Instructions)
                                    .Where(it =>
                                        it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 1)
                                    .Select(it => it.Data.ToByteArray()).FirstOrDefault();
                            }

                            if (tradeData != null)
                            {
                                var byteData = new ReadOnlySpan<byte>(tradeData);
                                var offset = 16;
                                offset += 32;
                                var solAmountUl = BinaryPrimitives.ReadUInt64LittleEndian(byteData.Slice(offset, 8));
                                offset += 8;
                                var amountUl = BinaryPrimitives.ReadUInt64LittleEndian(byteData.Slice(offset, 8));
                                offset += 8;
                                isBuy = byteData[offset] == 1;
                                offset += 1;
                                solAmount = (decimal)solAmountUl / 1e9m;
                                tokenAmount = (decimal)amountUl / 1e6m;
                                var price = solAmount / tokenAmount;
                                var user = Base58.Encode(byteData.Slice(offset, 32).ToArray());
                                Logger.LogDebug(
                                    $"[pump fun]{mint}=>{user} {solAmount} {tokenAmount} {isBuy}  {price} {sign}");
                            }
                        }
                    }
                    else if (accountKeys.Contains(ProgramIds.MeteoraDBCProgramId))
                    {
                        var programIndex = Array.IndexOf(accountKeysArray, ProgramIds.MeteoraDBCProgramId);
                        if (programIndex > 0)
                        {
                            var tradeData = transaction.Transaction.Message.Instructions
                                .Where(it => it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 1)
                                .Select(it => it.Data.ToByteArray()).FirstOrDefault();
                            if (tradeData == null)
                            {
                                tradeData = transaction.Meta.InnerInstructions.SelectMany(it => it.Instructions)
                                    .Where(it =>
                                        it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 1)
                                    .Select(it => it.Data.ToByteArray()).FirstOrDefault();
                            }

                            if (tradeData != null)
                            {
                                var byteData = new ReadOnlySpan<byte>(tradeData);
                                var offset = 8 + 8;
                                var poolId = Base58.Encode(byteData.Slice(offset, 32).ToArray());
                                offset += 32;
                                offset += 32;
                                isBuy = byteData[offset] == 1;
                                offset += 1;
                                offset += 1;
                                var amountIn = BinaryPrimitives.ReadUInt64LittleEndian(byteData.Slice(offset, 8));
                                offset += 8;
                                offset += 8;
                                offset += 8;
                                var amountOut = BinaryPrimitives.ReadUInt64LittleEndian(byteData.Slice(offset, 8));
                                offset += 8;
                                var decimals = isBuy switch
                                {
                                    true => transaction.Meta.PostTokenBalances.FirstOrDefault(it => it.Mint == mint)
                                        ?.UiTokenAmount.Decimals ?? 0,
                                    false => transaction.Meta.PreTokenBalances.FirstOrDefault(it => it.Mint == mint)
                                        ?.UiTokenAmount.Decimals ?? 0,
                                };
                                solAmount = isBuy ? (decimal)amountIn / 1e9m : (decimal)amountOut / 1e9m;
                                tokenAmount = isBuy
                                    ? (decimal)amountOut / (decimal)(Math.Pow(10, decimals))
                                    : (decimal)amountIn / (decimal)(Math.Pow(10, decimals));
                                var price = solAmount / tokenAmount;
                                var user = accountKeys[0];
                                Logger.LogDebug(
                                    $"[meteora dbc]{mint}=>{user} {solAmount} {tokenAmount} {isBuy}  {price} {sign}");
                            }
                        }
                    }
                    else if (accountKeys.Contains(ProgramIds.PumpSwapProgramId))
                    {
                        var programIndex = Array.IndexOf(accountKeysArray, ProgramIds.PumpSwapProgramId);
                        if (programIndex > 0)
                        {
                            var pumpAmmTradeData = transaction.Transaction.Message.Instructions
                                .Where(it =>
                                    it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 19)
                                .Select(it => Tuple.Create(it.Accounts.ToByteArray(), it.Data.ToByteArray()))
                                .FirstOrDefault();
                            if (pumpAmmTradeData == null || pumpAmmTradeData.Item1.Length == 0 ||
                                pumpAmmTradeData.Item2.Length == 0)
                            {
                                pumpAmmTradeData = transaction.Meta.InnerInstructions.SelectMany(it => it.Instructions)
                                    .Where(it =>
                                        it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 19)
                                    .Select(it => Tuple.Create(it.Accounts.ToByteArray(), it.Data.ToByteArray()))
                                    .FirstOrDefault();
                            }

                            if (pumpAmmTradeData != null)
                            {
                                var user = accountKeys[pumpAmmTradeData.Item1[1]];

                                
                                var tokenAccount =
                                    AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(new PublicKey(user),
                                        new PublicKey(mint));
                                
                                //buy =>102,   6,  61,  18, 1, 218, 235, 234
                                //sell=> 51, 230, 133, 164, 1, 127, 131, 173
                                isBuy = pumpAmmTradeData.Item2.AsSpan(0, 8).SequenceEqual(new byte[8] { 102, 6, 61, 18, 1, 218, 235, 234 });
                                /*if(pumpAmmTradeData.Item2.Take(8).SequenceEqual(new byte[8]{51, 230, 133, 164, 1, 127, 131, 173}))
                                {
                                    isBuy=false;
                                }
                                else if(pumpAmmTradeData.Item2.Take(8).SequenceEqual(new byte[8]{102,   6,  61,  18, 1, 218, 235, 234}))
                                {
                                    isBuy=true;
                                }*/

                                var tradeData = transaction.Transaction.Message.Instructions
                                    .Where(it =>
                                        it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 1)
                                    .Select(it => it.Data.ToByteArray()).FirstOrDefault();
                                if (tradeData == null)
                                {
                                    tradeData = transaction.Meta.InnerInstructions.SelectMany(it => it.Instructions)
                                        .Where(it =>
                                            it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 1)
                                        .Select(it => it.Data.ToByteArray()).FirstOrDefault();
                                }

                                if (tradeData != null)
                                {
                                    var byteData = new ReadOnlySpan<byte>(tradeData);
                                    var decimals = isBuy switch
                                    {
                                        true => transaction.Meta.PostTokenBalances.FirstOrDefault(it => it.Mint == mint)
                                            ?.UiTokenAmount.Decimals ?? 0,
                                        false => transaction.Meta.PreTokenBalances.FirstOrDefault(it => it.Mint == mint)
                                            ?.UiTokenAmount.Decimals ?? 0,
                                    };
                                    solAmount = 0m;
                                    tokenAmount = 0m;
                                    if (isBuy)
                                    {
                                        var solAmountUl =
                                            BinaryPrimitives.ReadUInt64LittleEndian(byteData.Slice(14 * 8, 8));
                                        var tokenAmountUl =
                                            BinaryPrimitives.ReadUInt64LittleEndian(byteData.Slice(3 * 8, 8));
                                        solAmount = (decimal)solAmountUl / 1e9m;
                                        tokenAmount = (decimal)tokenAmountUl / (decimal)(Math.Pow(10, decimals));
                                    }
                                    else
                                    {
                                        var solAmountUl =
                                            BinaryPrimitives.ReadUInt64LittleEndian(byteData.Slice(15 * 8, 8));
                                        var tokenAmountUl =
                                            BinaryPrimitives.ReadUInt64LittleEndian(byteData.Slice(3 * 8, 8));
                                        solAmount = (decimal)solAmountUl / 1e9m;
                                        tokenAmount = (decimal)tokenAmountUl / (decimal)(Math.Pow(10, decimals));
                                    }

                                    var price = solAmount / tokenAmount;

                                    Logger.LogDebug(
                                        $"[pump swap]{mint}=>{user} {solAmount} {tokenAmount} {isBuy}  {price} {sign}");
                                }
                            }
                        }
                    }
                    else if (accountKeys.Contains(ProgramIds.MoonitProgramId))
                    {
                    }
                    else if (accountKeys.Contains(ProgramIds.RaydiumAmmProgramId))
                    {
                        var programIndex = Array.IndexOf(accountKeysArray, ProgramIds.RaydiumAmmProgramId);
                        if (programIndex > 0)
                        {
                            var raydiumAmmTradeData = transaction.Transaction.Message.Instructions
                                .Where(it =>
                                    it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length >= 17)
                                .Select(it => it.Accounts.ToByteArray())
                                .FirstOrDefault();
                            if (raydiumAmmTradeData == null || raydiumAmmTradeData.Length == 0)
                            {
                                raydiumAmmTradeData = transaction.Meta.InnerInstructions
                                    .SelectMany(it => it.Instructions)
                                    .Where(it =>
                                        it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length >= 17)
                                    .Select(it => it.Accounts.ToByteArray())
                                    .FirstOrDefault();
                            }


                            if (raydiumAmmTradeData != null)
                            {
                                var userIndex = raydiumAmmTradeData[^1];
                                var user = accountKeys[userIndex];
                                var destinationTokenAccountIndex = raydiumAmmTradeData[^2];
                                var destinationTokenAccount = accountKeys[destinationTokenAccountIndex];
                                var sourceTokenAccountIndex = raydiumAmmTradeData[^3];
                                var sourceTokenAccount = accountKeys[raydiumAmmTradeData[sourceTokenAccountIndex]];

                                var tokenAccount =
                                    AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(new PublicKey(user),
                                        new PublicKey(mint));
                                isBuy = destinationTokenAccount == tokenAccount.Key;

                                var tokenProgramIdIndex =
                                    Array.IndexOf(accountKeysArray, TokenProgram.ProgramIdKey.Key);
                                var wSolIndex = Array.IndexOf(accountKeysArray, ProgramIds.WSol);
                                var mintIndex = Array.IndexOf(accountKeysArray, mint);
                                var rayAuthIndex = Array.IndexOf(accountKeysArray,
                                    "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1");

                                byte[] sourceTradeData = null;

                                foreach (var instruction in transaction.Transaction.Message.Instructions)
                                {
                                    if (instruction.ProgramIdIndex != tokenProgramIdIndex ||
                                        instruction.Data.ToByteArray().Length != 9) continue;
                                    if (instruction.Accounts.ToByteArray().Length >= 3)
                                    {
                                        var dataAccounts = instruction.Accounts.ToByteArray();
                                        if (dataAccounts[0] == sourceTokenAccountIndex && dataAccounts[2] == userIndex)
                                        {
                                            sourceTradeData = instruction.Data.ToByteArray();
                                            break;
                                        }
                                    }
                                }

                                if (sourceTradeData == null)
                                {
                                    foreach (var innerInstruction in transaction.Meta.InnerInstructions)
                                    {
                                        foreach (var instruction in innerInstruction.Instructions)
                                        {
                                            if (instruction.ProgramIdIndex != tokenProgramIdIndex ||
                                                instruction.Data.ToByteArray().Length != 9) continue;
                                            if (instruction.Accounts.ToByteArray().Length >= 3)
                                            {
                                                var dataAccounts = instruction.Accounts.ToByteArray();
                                                if (dataAccounts[0] == sourceTokenAccountIndex &&
                                                    dataAccounts[2] == userIndex)
                                                {
                                                    sourceTradeData = instruction.Data.ToByteArray();
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }

                                byte[] destinationTradeData = null;
                                foreach (var instruction in transaction.Transaction.Message.Instructions)
                                {
                                    if (instruction.ProgramIdIndex != tokenProgramIdIndex ||
                                        instruction.Data.ToByteArray().Length != 9) continue;
                                    if (instruction.Accounts.ToByteArray().Length >= 3)
                                    {
                                        var dataAccounts = instruction.Accounts.ToByteArray();
                                        if (dataAccounts[1] == destinationTokenAccountIndex &&
                                            dataAccounts[2] == rayAuthIndex)
                                        {
                                            destinationTradeData = instruction.Data.ToByteArray();
                                            break;
                                        }
                                    }
                                }

                                if (destinationTradeData == null)
                                {
                                    foreach (var innerInstruction in transaction.Meta.InnerInstructions)
                                    {
                                        foreach (var instruction in innerInstruction.Instructions)
                                        {
                                            if (instruction.ProgramIdIndex != tokenProgramIdIndex ||
                                                instruction.Data.ToByteArray().Length != 9) continue;
                                            if (instruction.Accounts.ToByteArray().Length >= 3)
                                            {
                                                var dataAccounts = instruction.Accounts.ToByteArray();
                                                if (dataAccounts[1] == destinationTokenAccountIndex &&
                                                    dataAccounts[2] == rayAuthIndex)
                                                {
                                                    destinationTradeData = instruction.Data.ToByteArray();
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }

                                if (sourceTradeData != null && destinationTradeData != null)
                                {
                                    var solAmountUl = isBuy switch
                                    {
                                        true => BinaryPrimitives.ReadUInt64LittleEndian(sourceTradeData.AsSpan(1, 8)),
                                        false => BinaryPrimitives.ReadUInt64LittleEndian(
                                            destinationTradeData.AsSpan(1, 8)),
                                    };
                                    var tokenAmountUl = isBuy switch
                                    {
                                        true => BinaryPrimitives.ReadUInt64LittleEndian(
                                            destinationTradeData.AsSpan(1, 8)),
                                        false => BinaryPrimitives.ReadUInt64LittleEndian(sourceTradeData.AsSpan(1, 8)),
                                    };
                                    var decimals = isBuy switch
                                    {
                                        true => transaction.Meta.PostTokenBalances.FirstOrDefault(it => it.Mint == mint)
                                            ?.UiTokenAmount.Decimals ?? 0,
                                        false => transaction.Meta.PreTokenBalances.FirstOrDefault(it =>
                                            it.Mint == mint)?.UiTokenAmount.Decimals ?? 0,
                                    };
                                    solAmount = (decimal)solAmountUl / 1e9m;
                                    tokenAmount = (decimal)tokenAmountUl / (decimal)(Math.Pow(10, decimals));
                                    var price = solAmount / tokenAmount;
                                    Logger.LogDebug(
                                        $"[raydium amm]{mint}=>{user} {solAmount} {tokenAmount} {isBuy}  {price} {sign}");
                                }
                            }
                        }
                    }
                    else if (accountKeys.Contains(ProgramIds.RaydiumCpmmProgramId))
                    {
                        var programIndex = Array.IndexOf(accountKeysArray, ProgramIds.RaydiumCpmmProgramId);
                        if (programIndex > 0)
                        {
                            var raydiumCpmmTradeData = transaction.Transaction.Message.Instructions
                                .Where(it =>
                                    it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 13)
                                .Select(it => it.Accounts.ToByteArray())
                                .FirstOrDefault();
                            if (raydiumCpmmTradeData == null || raydiumCpmmTradeData.Length == 0)
                            {
                                raydiumCpmmTradeData = transaction.Meta.InnerInstructions
                                    .SelectMany(it => it.Instructions)
                                    .Where(it =>
                                        it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 13)
                                    .Select(it => it.Accounts.ToByteArray())
                                    .FirstOrDefault();
                            }

                            if (raydiumCpmmTradeData == null || raydiumCpmmTradeData.Length == 0) continue;
                            var userIndex = raydiumCpmmTradeData[0];
                            var user = accountKeys[userIndex];
                            var inputTokenAccountIndex = raydiumCpmmTradeData[4];
                            var inputTokenAccount = accountKeys[inputTokenAccountIndex];
                            var outputTokenAccountIndex = raydiumCpmmTradeData[5];
                            var outputTokenAccount = accountKeys[outputTokenAccountIndex];
                            var token2022ProgramIndex = Array.IndexOf(accountKeysArray, ProgramIds.Token2022Program);
                            var tokenProgramIdIndex = Array.IndexOf(accountKeysArray, TokenProgram.ProgramIdKey.Key);

                            var tokenAccount =
                                AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(new PublicKey(user),
                                    new PublicKey(mint));
                            isBuy = outputTokenAccount == tokenAccount.Key;

                            var wSolIndex = Array.IndexOf(accountKeysArray, ProgramIds.WSol);
                            var mintIndex = Array.IndexOf(accountKeysArray, mint);

                            var solTradeData = transactionParser.FindTransferInstructionData(transaction,
                                (byte)tokenProgramIdIndex,
                                d => d.Length == 10 && d[0] == 12,
                                u => u.Length == 4 && u[1] == wSolIndex
                            );
                            var tokenTradeProgramIndex = isBuy ? raydiumCpmmTradeData[9] : raydiumCpmmTradeData[8];
                            var tokenTradeData = transactionParser.FindTransferInstructionData(transaction,
                                (byte)tokenTradeProgramIndex,
                                d => d.Length == 10 && d[0] == 12,
                                u => u.Length == 4 && u[1] == mintIndex
                            );
                            if (solTradeData != null && tokenTradeData != null)
                            {
                                var solAmountUl =
                                    BinaryPrimitives.ReadUInt64LittleEndian(solTradeData.AsSpan(1, 8));
                                var tokenAmountUl =
                                    BinaryPrimitives.ReadUInt64LittleEndian(tokenTradeData.AsSpan(1, 8));
                                var decimals = (int)tokenTradeData[^1];
                                solAmount = (decimal)solAmountUl / 1e9m;
                                tokenAmount = (decimal)tokenAmountUl / (decimal)(Math.Pow(10, decimals));
                                var price = solAmount / tokenAmount;
                                Logger.LogDebug(
                                    $"[raydium cpmm]{mint}=>{user} {solAmount} {tokenAmount} {isBuy}  {price} {sign}");
                            }
                        }
                    }
                    else if (accountKeys.Contains(ProgramIds.RaydiumClmmProgramId))
                    {
                        var programIndex = Array.IndexOf(accountKeysArray, ProgramIds.RaydiumClmmProgramId);
                        if (programIndex > 0)
                        {
                            var raydiumClmmTradeData = transaction.Transaction.Message.Instructions
                                .Where(it =>
                                    it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length >= 13)
                                .Select(it => Tuple.Create(it.Accounts.ToByteArray(), it.Data.ToByteArray()))
                                .FirstOrDefault();
                            if (raydiumClmmTradeData == null || raydiumClmmTradeData.Item1.Length == 0 ||
                                raydiumClmmTradeData.Item2.Length == 0)
                            {
                                raydiumClmmTradeData = transaction.Meta.InnerInstructions
                                    .SelectMany(it => it.Instructions)
                                    .Where(it =>
                                        it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length >= 13)
                                    .Select(it => Tuple.Create(it.Accounts.ToByteArray(), it.Data.ToByteArray()))
                                    .FirstOrDefault();
                            }

                            if (raydiumClmmTradeData == null || raydiumClmmTradeData.Item1.Length == 0 ||
                                raydiumClmmTradeData.Item2.Length == 0) continue;

                            //2b 04 ed 0b 1a c9 1e 62       [43, 4, 237, 11, 26, 201, 30, 98]
                            //f8 c6 9e 91 e1 75 87 c8 swap  [248, 198, 158, 145, 225, 117, 135, 200]
                            var isSwapV2 = raydiumClmmTradeData.Item2.AsSpan(0, 8)
                                .SequenceEqual(new byte[8] { 43, 4, 237, 11, 26, 201, 30, 98 });
                            var userIndex = raydiumClmmTradeData.Item1[0];
                            var user = accountKeys[userIndex];
                            var poolIndex = raydiumClmmTradeData.Item1[2];
                            var pool = accountKeys[poolIndex];
                            var inputTokenAccountIndex = raydiumClmmTradeData.Item1[3];
                            var inputTokenAccount = accountKeys[inputTokenAccountIndex];
                            var outputTokenAccountIndex = raydiumClmmTradeData.Item1[4];
                            var outputTokenAccount = accountKeys[outputTokenAccountIndex];
                            var tokenProgramIdIndex = Array.IndexOf(accountKeysArray, TokenProgram.ProgramIdKey.Key);

                            var tokenAccount =
                                AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(new PublicKey(user),
                                    new PublicKey(mint));
                            isBuy = outputTokenAccount == tokenAccount.Key;
                            if (isSwapV2)
                            {
                                var wSolIndex = Array.IndexOf(accountKeysArray, ProgramIds.WSol);
                                var mintIndex = Array.IndexOf(accountKeysArray, mint);
                                var solTradeData = transactionParser.FindTransferInstructionData(transaction,
                                    (byte)tokenProgramIdIndex,
                                    d => d.Length == 10 && d[0] == 12,
                                    u => u.Length == 4 && u[1] == wSolIndex
                                );
                                var tokenTradeProgramIndex =
                                    isBuy ? raydiumClmmTradeData.Item1[9] : raydiumClmmTradeData.Item1[8];
                                var tokenTradeData = transactionParser.FindTransferInstructionData(transaction,
                                    (byte)tokenTradeProgramIndex,
                                    d => d.Length == 10 && d[0] == 12,
                                    u => u.Length == 4 && u[1] == mintIndex
                                );
                                if (solTradeData != null && tokenTradeData != null)
                                {
                                    var solAmountUl =
                                        BinaryPrimitives.ReadUInt64LittleEndian(solTradeData.AsSpan(1, 8));
                                    var tokenAmountUl =
                                        BinaryPrimitives.ReadUInt64LittleEndian(tokenTradeData.AsSpan(1, 8));
                                    var decimals = (int)tokenTradeData[^1];
                                    solAmount = (decimal)solAmountUl / 1e9m;
                                    tokenAmount = (decimal)tokenAmountUl / (decimal)(Math.Pow(10, decimals));
                                    var price = solAmount / tokenAmount;
                                    Logger.LogDebug(
                                        $"[raydium cpmm swapV2]{mint}=>{user} {solAmount} {tokenAmount} {isBuy}  {price} {sign}");
                                }
                            }
                            else
                            {
                                var sourceTradeData = transactionParser.FindTransferInstructionData(transaction,
                                    (byte)tokenProgramIdIndex,
                                    d => d.Length == 9 && d[0] == 3,
                                    u => u.Length == 3 && u[0] == inputTokenAccountIndex && u[2] == userIndex
                                );
                                var destinationTradeData = transactionParser.FindTransferInstructionData(transaction,
                                    (byte)tokenProgramIdIndex,
                                    d => d.Length == 9 && d[0] == 3,
                                    u => u.Length == 4 && u[1] == outputTokenAccountIndex && u[2] == poolIndex
                                );
                                if (sourceTradeData != null && destinationTradeData != null)
                                {
                                    var solAmountUl = isBuy switch
                                    {
                                        true => BinaryPrimitives.ReadUInt64LittleEndian(sourceTradeData.AsSpan(1, 8)),
                                        false => BinaryPrimitives.ReadUInt64LittleEndian(
                                            destinationTradeData.AsSpan(1, 8)),
                                    };
                                    var tokenAmountUl = isBuy switch
                                    {
                                        true => BinaryPrimitives.ReadUInt64LittleEndian(
                                            destinationTradeData.AsSpan(1, 8)),
                                        false => BinaryPrimitives.ReadUInt64LittleEndian(sourceTradeData.AsSpan(1, 8)),
                                    };
                                    var decimals = isBuy switch
                                    {
                                        true => transaction.Meta.PostTokenBalances.FirstOrDefault(it => it.Mint == mint)
                                            ?.UiTokenAmount.Decimals ?? 0,
                                        false => transaction.Meta.PreTokenBalances.FirstOrDefault(it =>
                                            it.Mint == mint)?.UiTokenAmount.Decimals ?? 0,
                                    };
                                    solAmount = (decimal)solAmountUl / 1e9m;
                                    tokenAmount = (decimal)tokenAmountUl / (decimal)(Math.Pow(10, decimals));
                                    var price = solAmount / tokenAmount;
                                    Logger.LogDebug(
                                        $"[raydium clmm swap]{mint}=>{user} {solAmount} {tokenAmount} {isBuy}  {price} {sign}");
                                }
                            }
                        }
                    }
                    else if (accountKeys.Contains(ProgramIds.MeteoraDynProgramId))
                    {
                        var programIndex = Array.IndexOf(accountKeysArray, ProgramIds.MeteoraDynProgramId);
                        if (programIndex > 0)
                        {
                            var meteoraDynTradeData = transaction.Transaction.Message.Instructions
                                .Where(it =>
                                    it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length >= 15)
                                .Select(it => it.Accounts.ToByteArray())
                                .FirstOrDefault();
                            if (meteoraDynTradeData == null || meteoraDynTradeData.Length == 0)
                            {
                                meteoraDynTradeData = transaction.Meta.InnerInstructions
                                    .SelectMany(it => it.Instructions)
                                    .Where(it =>
                                        it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length >= 15)
                                    .Select(it => it.Accounts.ToByteArray())
                                    .FirstOrDefault();
                            }

                            if (meteoraDynTradeData == null || meteoraDynTradeData.Length == 0) continue;
                            var userIndex = meteoraDynTradeData[12];
                            var poolIndex = meteoraDynTradeData[0];
                            var userSourceTokenIndex = meteoraDynTradeData[1];
                            var userDestinationTokenIndex = meteoraDynTradeData[2];
                            var aTokenVaultIndex = meteoraDynTradeData[5]; //token
                            var bTokenVaultIndex = meteoraDynTradeData[6]; //wsol
                            var user = accountKeys[userIndex];
                            var pool = accountKeys[poolIndex];
                            var sourceTokenAccount = accountKeys[userSourceTokenIndex];
                            var destinationTokenAccount = accountKeys[userDestinationTokenIndex];
                            var tokenProgramIdIndex = Array.IndexOf(accountKeysArray, TokenProgram.ProgramIdKey.Key);

                            var tokenAccount =
                                AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(new PublicKey(user),
                                    new PublicKey(mint));
                            isBuy = destinationTokenAccount == tokenAccount.Key;

                            var sourceTradeData = transactionParser.FindTransferInstructionData(transaction,
                                (byte)tokenProgramIdIndex,
                                d => d.Length == 9 && d[0] == 3,
                                u => u.Length == 3 && u[0] == userSourceTokenIndex && u[2] == userIndex &&
                                     u[1] == (isBuy ? bTokenVaultIndex : aTokenVaultIndex)
                            );
                            var destinationTradeData = transactionParser.FindTransferInstructionData(transaction,
                                (byte)tokenProgramIdIndex,
                                d => d.Length == 9 && d[0] == 3,
                                u => u.Length == 3 && u[1] == userDestinationTokenIndex
                            );
                            if (sourceTradeData != null && destinationTradeData != null)
                            {
                                var solAmountUl = isBuy switch
                                {
                                    true => BinaryPrimitives.ReadUInt64LittleEndian(sourceTradeData.AsSpan(1, 8)),
                                    false => BinaryPrimitives.ReadUInt64LittleEndian(
                                        destinationTradeData.AsSpan(1, 8)),
                                };
                                var tokenAmountUl = isBuy switch
                                {
                                    true => BinaryPrimitives.ReadUInt64LittleEndian(
                                        destinationTradeData.AsSpan(1, 8)),
                                    false => BinaryPrimitives.ReadUInt64LittleEndian(sourceTradeData.AsSpan(1, 8)),
                                };
                                var decimals = isBuy switch
                                {
                                    true => transaction.Meta.PostTokenBalances.FirstOrDefault(it => it.Mint == mint)
                                        ?.UiTokenAmount.Decimals ?? 0,
                                    false => transaction.Meta.PreTokenBalances.FirstOrDefault(it =>
                                        it.Mint == mint)?.UiTokenAmount.Decimals ?? 0,
                                };
                                solAmount = (decimal)solAmountUl / 1e9m;
                                tokenAmount = (decimal)tokenAmountUl / (decimal)(Math.Pow(10, decimals));
                                var price = solAmount / tokenAmount;
                                Logger.LogDebug(
                                    $"[meteora dyn]{mint}=>{user} {solAmount} {tokenAmount} {isBuy}  {price} {sign}");
                            }
                        }
                    }
                    else if (accountKeys.Contains(ProgramIds.MeteoraDlmmProgramId))
                    {
                        var programIndex = Array.IndexOf(accountKeysArray, ProgramIds.MeteoraDlmmProgramId);
                        if (programIndex > 0)
                        {
                            var meteoraDlmmTradeData = transaction.Transaction.Message.Instructions
                                .Where(it =>
                                    it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length >= 17)
                                .Select(it => it.Accounts.ToByteArray())
                                .FirstOrDefault();
                            if (meteoraDlmmTradeData == null || meteoraDlmmTradeData.Length == 0)
                            {
                                meteoraDlmmTradeData = transaction.Meta.InnerInstructions
                                    .SelectMany(it => it.Instructions)
                                    .Where(it =>
                                        it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length >= 17)
                                    .Select(it => it.Accounts.ToByteArray())
                                    .FirstOrDefault();
                            }

                            if (meteoraDlmmTradeData == null || meteoraDlmmTradeData.Length == 0) continue;
                            var tokenY = accountKeys[meteoraDlmmTradeData[7]];
                            var tokenX = accountKeys[meteoraDlmmTradeData[8]];
                            var user = accountKeys[meteoraDlmmTradeData[10]];
                            var outputTokenAccount = accountKeys[meteoraDlmmTradeData[5]];
                            if (tokenY != ProgramIds.WSol) continue;

                            var tradeData = transactionParser.FindTransferInstructionData(transaction,
                                (byte)programIndex,
                                d => d[0] == 228,
                                u => u.Length == 1
                            );
                            if (tradeData != null)
                            {
                                var byteData = new ReadOnlySpan<byte>(tradeData);
                                var offset = 8 + 8;
                                var poolId = Base58.Encode(byteData.Slice(offset, 32).ToArray());
                                offset += 32;
                                offset += 32;
                                offset += 8;
                                var amountIn = BinaryPrimitives.ReadUInt64LittleEndian(byteData.Slice(offset, 8));
                                offset += 8;
                                var amountOut = BinaryPrimitives.ReadUInt64LittleEndian(byteData.Slice(offset, 8));
                                offset += 8;
                                /*var tokenAccount =
                                    AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(new PublicKey(user),
                                        new PublicKey(mint));
                                var isBuy = outputTokenAccount == tokenAccount.Key;*/
                                isBuy = byteData[offset] == 0;
                                var decimals = isBuy switch
                                {
                                    true => transaction.Meta.PostTokenBalances.FirstOrDefault(it => it.Mint == mint)
                                        ?.UiTokenAmount.Decimals ?? 0,
                                    false => transaction.Meta.PreTokenBalances.FirstOrDefault(it => it.Mint == mint)
                                        ?.UiTokenAmount.Decimals ?? 0,
                                };
                                solAmount = isBuy ? (decimal)amountIn / 1e9m : (decimal)amountOut / 1e9m;
                                tokenAmount = isBuy
                                    ? (decimal)amountOut / (decimal)(Math.Pow(10, decimals))
                                    : (decimal)amountIn / (decimal)(Math.Pow(10, decimals));
                                var price = solAmount / tokenAmount;

                                Logger.LogDebug(
                                    $"[meteora dlmm]{mint}=>{user} {solAmount} {tokenAmount} {isBuy}  {price} {sign}");
                            }
                        }
                    }

                    await _redisClient.PublishAsync(RedisKey.SolanaMonitorMintSubscribe,
                        JsonSerializer.Serialize(new SolanaMonitorMintSubscribe(mint, solAmount, tokenAmount, isBuy)));
                }
                catch (Exception e)
                {
                    Logger.LogError(e.Message);
                }
            }
        }
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        Logger.LogDebug($"Token 交易监听 服务 启动");
        await DoWorkAsync(cancellationToken);


        await base.StartAsync(cancellationToken);
    }


    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        await SendUnSubscribeRequest();
        Logger.LogDebug($"Token 交易监听 服务 停止");
        streamRedis.Dispose();
        stream.Dispose();
        await base.StopAsync(cancellationToken);
    }
}