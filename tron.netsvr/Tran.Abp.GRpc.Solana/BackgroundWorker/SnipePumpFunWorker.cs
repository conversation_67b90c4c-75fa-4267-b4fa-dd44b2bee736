namespace Tran.Abp.GRpc.Solana.BackgroundWorker;

public class SnipePumpFunWorker : AsyncPeriodicBackgroundWorkerBase
{
    public ILogger<SnipePumpFunWorker> Logger { get; set; }
    private readonly Geyser.GeyserClient client;
    private DateTime startTime;
    private AsyncDuplexStreamingCall<SubscribeRequest, SubscribeUpdate> stream;
    private SubscribeRequest request;
    private DateTime _lastMessageTime; // 标记最后收到消息的时间
    private IRedisClient _redisClient;
    public SnipePumpFunWorker(AbpAsyncTimer timer, IServiceScopeFactory serviceScopeFactory, Geyser.GeyserClient client, IRedisClient redisClient)
        : base(timer, serviceScopeFactory)
    {
        Timer.Period = 5 * 60 * 1000; //5s 执行一下次
        this.client = client;
        _redisClient = redisClient;
        Logger = NullLogger<SnipePumpFunWorker>.Instance;
    }

    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {
        var timeSinceLastMessage = DateTime.Now - _lastMessageTime;
        Logger.LogWarning($"ws 断开 1s 后连接 {_lastMessageTime}=>{DateTime.Now}=>{timeSinceLastMessage.TotalSeconds}");
        if (timeSinceLastMessage.TotalSeconds >= 60)
        {
            Logger.LogWarning($"ws 断开 1s 后连接");
            await UnSubscribe(new CancellationToken());
            await Task.Delay(1000);
            await Subscribe(new CancellationToken());
        }
    }

    private async Task Subscribe(CancellationToken cancellationToken)
    {
        request.Transactions.Add("pumpfuncreate", new SubscribeRequestFilterTransactions()
        {
            Vote = false,
            Failed = false,
            AccountInclude = { "TSLvdd1pWpHVjahSpsvCXUbgwsL3JAcvokwaKt1eokM" },
            AccountExclude = { },
            AccountRequired = { }
        });


        await stream.RequestStream.WriteAsync(request, cancellationToken);
    }

    private async Task UnSubscribe(CancellationToken cancellationToken)
    {
        request.Transactions.Clear();
        await stream.RequestStream.WriteAsync(request, cancellationToken);
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        stream = client.Subscribe();
        Task.Run(() => OnSubscribe(cancellationToken), cancellationToken);
        request = new SubscribeRequest
        {
            Commitment = CommitmentLevel.Finalized,
        };
        await Subscribe(cancellationToken);
        Logger.LogDebug("snipe pumpfun  服务=>启动");
        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        stream.Dispose();
        Logger.LogDebug("snipe pumpfun  服务=>停止");
        await base.StopAsync(cancellationToken);
    }

    private async Task OnSubscribe(CancellationToken cancellationToken)
    {
        while (await stream.ResponseStream.MoveNext(cancellationToken))
        {
            var data = stream.ResponseStream.Current;
            if (data.Transaction != null)
            {
                _lastMessageTime = DateTime.Now;
                //解析消息
                var type = data.Filters[0];
                switch (type)
                {
                    case "pumpfuncreate":
                        await PumpFunCreateHandler(data.Transaction.Transaction);
                        break;
                }
            }
        }
    }

    private async Task PumpFunCreateHandler(SubscribeUpdateTransactionInfo transaction)
    {
        var signature = Base58.Encode(transaction.Signature.ToByteArray());
        //所有账户
        var accountKeys = transaction.Transaction.Message.AccountKeys
            .Select(k => Base58.Encode(k.ToByteArray()))
            .ToList();
        if (transaction.Meta.LoadedWritableAddresses.Count > 0)
        {
            accountKeys.AddRange(
                transaction.Meta.LoadedWritableAddresses.Select(k => Base58.Encode(k.ToByteArray())));
        }
        if (transaction.Meta.LoadedReadonlyAddresses.Count > 0)
        {
            accountKeys.AddRange(
                transaction.Meta.LoadedReadonlyAddresses.Select(k => Base58.Encode(k.ToByteArray())));
        }
        var accountKeysArray = accountKeys.ToArray();
        if (accountKeysArray.Contains(ProgramIds.PumpFunProgramId))
        {
            var programIndex = Array.IndexOf(accountKeysArray, ProgramIds.PumpFunProgramId);
            if (programIndex > 0)
            {
                var tradeAccounts = transaction.Transaction.Message.Instructions
                    .Where(it => it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 14)
                    .Select(it => it.Accounts.ToByteArray()).FirstOrDefault();
                if (tradeAccounts == null)
                {
                    tradeAccounts = transaction.Meta.InnerInstructions.SelectMany(it => it.Instructions)
                        .Where(it =>
                            it.ProgramIdIndex == programIndex && it.Accounts.ToByteArray().Length == 14)
                        .Select(it => it.Accounts.ToByteArray()).FirstOrDefault();
                }  
                if (tradeAccounts != null)
                {
                    var Mint=accountKeys[tradeAccounts[0]];
                    var Creator=accountKeys[tradeAccounts[7]];
                    var BondingCurve=accountKeys[tradeAccounts[2]];
                    Logger.LogWarning($"======================新代币======================{Environment.NewLine}" +
                                      $"{nameof(Mint),-12}:{Mint}{Environment.NewLine}" +
                                      $"{nameof(BondingCurve),-12}:{BondingCurve}{Environment.NewLine}" +
                                      $"{nameof(Creator),-12}:{Creator}{Environment.NewLine}" +
                                      $"https://solscan.io/tx/{signature}");
                }
            }
        }
        
        //Logger.LogWarning($"发射新代币=>代币:{mint} 所有者:{owner} https://solscan.io/tx/{signature}");
    }
    
    private async Task CheckSnipe(string mint)
    {
        if (_redisClient.HExists(RedisKey.SnipePumpKey, mint))
        {
            var taskJson = await _redisClient.HGetAsync(RedisKey.SnipePumpKey, mint);
            var dict = JsonConvert.DeserializeObject<Dictionary<string, string>>(taskJson);
            Logger.LogInformation($"狙击到本系统发币,发送狙击任务=>用户ID:{dict["uid"]} 任务ID:{dict["Id"]}");

            //判断阻击买入
            if (Convert.ToInt32(dict["Id"]) > 0)
            {
                //发送购买任务
                var batchdict = new Dictionary<string, string>()
                {
                    ["Id"] = dict["Id"],
                    ["DexType"] = DexType.Pumpfun,
                };
                await _redisClient.XAddAsync(StreamKey.BatchTradeStream, batchdict);
            }
            //判断捆绑买入
            if (dict["bindData"] != null)
            {
                var buyList = JsonSerializer.Deserialize<Dictionary<string,decimal>>(dict["bindData"]);
                if (buyList == null) return;
                for (int i = 0; i < buyList.Count;)
                {
                    var bindData = buyList.Skip(i).Take(2).ToDictionary();
                    var bindDict = new Dictionary<string, string>()
                    {
                        ["bindData"] = JsonSerializer.Serialize(bindData),
                        ["uid"] = dict["uid"],
                        ["mint"] = mint,
                        ["DexType"] = DexType.Pumpfun,
                    };
                    await _redisClient.XAddAsync(StreamKey.BindTradeStream, bindDict);
                    i += 2;
                }
            }

            _redisClient.HDel(RedisKey.SnipePumpKey, mint);
        }
    }
    
}