namespace Solnet.Launchpad.Curve;

public abstract class CurveBase
{
    public abstract decimal GetPoolInitPriceByPool(LaunchpadPoolInfo poolInfo, int decimalA, int decimalB);
    public abstract decimal GetPoolInitPriceByInit(decimal a, decimal b, int decimalA, int decimalB);
    public abstract decimal GetPoolPrice(LaunchpadPoolInfo poolInfo, int decimalA, int decimalB);

    public abstract decimal GetPoolEndPrice(decimal supply, decimal totalSell, decimal totalLockedAmount,
        decimal totalFundRaising, decimal migrateFee, int decimalA, int decimalB);

    public abstract decimal GetPoolEndPriceReal(LaunchpadPoolInfo poolInfo, int decimalA, int decimalB);

    public abstract (decimal a, decimal b, decimal c) GetInitParam(decimal supply, decimal totalFundRaising,
        decimal totalSell, decimal totalLockedAmount, decimal migrateFee);

    public abstract decimal BuyExactIn(LaunchpadPoolInfo poolInfo, decimal amount);
    public abstract decimal BuyExactOut(LaunchpadPoolInfo poolInfo, decimal amount);
    public abstract decimal SellExactIn(LaunchpadPoolInfo poolInfo, decimal amount);
    public abstract decimal SellExactOut(LaunchpadPoolInfo poolInfo, decimal amount);
}