namespace Solnet.Launchpad.Curve;

public class FixedPriceCurve: CurveBase
    {
        public override decimal GetPoolInitPriceByPool(LaunchpadPoolInfo poolInfo, int decimalA, int decimalB)
        {
            return (decimal)(poolInfo.VirtualB / poolInfo.VirtualA) * (decimal)Math.Pow(10, decimalA - decimalB);
        }

        public override decimal GetPoolInitPriceByInit(decimal a, decimal b, int decimalA, int decimalB)
        {
            return (decimal)(b / a) * (decimal)Math.Pow(10, decimalA - decimalB);
        }

        public override decimal GetPoolPrice(LaunchpadPoolInfo poolInfo, int decimalA, int decimalB)
        {
            return (decimal)(poolInfo.VirtualB / poolInfo.VirtualA) * (decimal)Math.Pow(10, decimalA - decimalB);
        }

        public override decimal GetPoolEndPrice(decimal supply, decimal totalSell, decimal totalLockedAmount, decimal totalFundRaising, decimal migrateFee, int decimalA, int decimalB)
        {
            var numerator = totalFundRaising - migrateFee;
            var denominator = supply - totalSell - totalLockedAmount;
            return (decimal)(numerator / denominator) * (decimal)Math.Pow(10, decimalA - decimalB);
        }

        public override decimal GetPoolEndPriceReal(LaunchpadPoolInfo poolInfo, int decimalA, int decimalB)
        {
            var allSellToken = poolInfo.TotalSellA - poolInfo.RealA;
            var buyAllTokenUseB = poolInfo.TotalFundRaisingB - poolInfo.RealB;
            var numerator = poolInfo.VirtualB + poolInfo.RealB + buyAllTokenUseB;
            var denominator = poolInfo.VirtualA - poolInfo.RealA + allSellToken;
            return (decimal)(numerator / denominator) * (decimal)Math.Pow(10, decimalA - decimalB);
        }

        public override (decimal a, decimal b, decimal c) GetInitParam(decimal supply, decimal totalFundRaising, decimal totalSell, decimal totalLockedAmount, decimal migrateFee)
        {
            var supplyMinusLocked = supply - totalLockedAmount;
            if (supplyMinusLocked <= 0) throw new ArgumentException("invalid input 1");

            var denominator = 2 * totalFundRaising - migrateFee;
            var numerator = totalFundRaising * supplyMinusLocked;
            var totalSellExpect = numerator / denominator;

            // 注释掉的验证逻辑，保留以供参考
            // if (totalSell != totalSellExpect) throw new ArgumentException("invalid input 2");

            return (totalSellExpect, totalFundRaising, totalSellExpect);
        }

        public override decimal BuyExactIn(LaunchpadPoolInfo poolInfo, decimal amount)
        {
            return GetAmountOut(amount, poolInfo.VirtualB, poolInfo.VirtualA);
        }

        public override decimal BuyExactOut(LaunchpadPoolInfo poolInfo, decimal amount)
        {
            return GetAmountIn(amount, poolInfo.VirtualB, poolInfo.VirtualA);
        }

        public override decimal SellExactIn(LaunchpadPoolInfo poolInfo, decimal amount)
        {
            return GetAmountOut(amount, poolInfo.VirtualA, poolInfo.VirtualB);
        }

        public override decimal SellExactOut(LaunchpadPoolInfo poolInfo, decimal amount)
        {
            return GetAmountIn(amount, poolInfo.VirtualA, poolInfo.VirtualB);
        }

        private static decimal GetAmountOut(decimal amountIn, decimal initInput, decimal initOutput)
        {
            var numerator = initOutput * amountIn;
            return numerator / initInput;
        }

        private static decimal GetAmountIn(decimal amountOut, decimal initInput, decimal initOutput)
        {
            var numerator = initInput * amountOut;
            return CeilDiv(numerator, initOutput);
        }

        /// <summary>
        /// 向上取整除法
        /// </summary>
        private static decimal CeilDiv(decimal numerator, decimal denominator)
        {
            return (numerator + denominator - 1) / denominator;
        }
    }