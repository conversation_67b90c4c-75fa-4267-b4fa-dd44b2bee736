using System.Linq;
using System.Text.Json.Nodes;
using Solnet.Pumpfun;
using Solnet.Wallet.Utilities;

namespace Tran.Abp.EnhancedWsServer.Core;

public class TransactionDataExtractor
{
    /// <summary>
    /// 从交易数据中提取与675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8相关的amount数据、是否买入标志、精度和mint值。
    /// </summary>
    /// <param name="json">交易数据的JSON字符串。</param>
    /// <returns>包含amounts、isBuy、decimals和mint的元组，如果未找到则返回默认值。</returns>
    public static (ulong[] amounts, bool isBuy, int decimals, string mint, string owner) ExtractRaydiumData(string json)
    {
        try
        {
            JsonNode? node = JsonNode.Parse(json);
            return ProcessRaydiumTransaction(node);
        }
        catch (JsonException ex)
        {
            Console.WriteLine($" Raydium JSON parsing error: {ex.Message}");
            return (new ulong[] { 0, 0 }, false, 0, "", "");
        }
    }

    private static (ulong[] amounts, bool isBuy, int decimals, string mint, string owner) ProcessRaydiumTransaction(
        JsonNode? node)
    {
        if (node == null) return (new ulong[] { 0, 0 }, false, 0, "", "");

        List<string> amountsList = new List<string>();

        JsonArray? innerInstructions = node["transaction"]?["meta"]?["innerInstructions"]?.AsArray();
        if (innerInstructions != null)
        {
            amountsList.AddRange(
                from inner in innerInstructions
                let innerInstrs = inner?["instructions"]?.AsArray()
                where innerInstrs != null
                from instr in innerInstrs
                where instr?["programId"]?.GetValue<string>() == "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
                let parsed = instr?["parsed"]
                where parsed != null
                let type = parsed["type"]?.GetValue<string>()
                where type == "transfer"
                let info = parsed["info"]?.AsObject()
                where info != null && info.ContainsKey("amount")
                select info["amount"]!.GetValue<string>()
            );
            /*foreach (var inner in innerInstructions)
            {
                JsonArray? innerInstrs = inner["instructions"]?.AsArray();
                if (innerInstrs != null)
                {
                    foreach (var instr in innerInstrs)
                    {
                        var programId = instr["programId"]?.GetValue<string>();
                        if (programId != null && programId == "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA")
                        {
                            var parsed = instr["parsed"];
                            if (parsed != null)
                            {
                                var type=parsed["type"]?.GetValue<string>();
                                if ( type != null && type == "transfer" && parsed["info"] is JsonObject info &&
                                    info["amount"] != null)
                                {
                                    string amount = info["amount"].GetValue<string>();
                                    amountsList.Add(amount);
                                }
                            }
                        }
                    }
                }
            }*/
        }


        if (amountsList.Count >= 2) // 确保有两条amount数据
        {
            int decimals = 0;
            string mint = null;
            // 将string转换为ulong
            ulong[] amounts = new ulong[2];
            amounts[0] = ulong.Parse(amountsList[0]);
            amounts[1] = ulong.Parse(amountsList[1]);

            // 从accountKeys获取第一个owner
            JsonArray? accountKeys = node["transaction"]?["transaction"]?["message"]?["accountKeys"]?.AsArray();
            string owner = accountKeys != null && accountKeys.Count > 0
                ? accountKeys[0]?["pubkey"]?.GetValue<string>()
                : null;

            bool isBuy = false;
            if (owner != null)
            {
                // 获取preTokenBalances和postTokenBalances
                JsonArray? preTokenBalances = node["transaction"]?["meta"]?["preTokenBalances"]?.AsArray();
                JsonArray? postTokenBalances = node["transaction"]?["meta"]?["postTokenBalances"]?.AsArray();

                // 查找匹配owner的记录
                JsonNode? preTokenBalance =
                    preTokenBalances?.FirstOrDefault(tb => tb["owner"]?.GetValue<string>() == owner
                                                           && tb["mint"]?.GetValue<string>() !=
                                                           "So11111111111111111111111111111111111111112");
                JsonNode? postTokenBalance =
                    postTokenBalances?.FirstOrDefault(tb => tb["owner"]?.GetValue<string>() == owner
                                                            && tb["mint"]?.GetValue<string>() !=
                                                            "So11111111111111111111111111111111111111112");

                // 计算amount变化
                ulong preAmount = preTokenBalance != null && preTokenBalance["uiTokenAmount"]?["amount"] != null
                    ? ulong.Parse(preTokenBalance["uiTokenAmount"]["amount"].GetValue<string>())
                    : 0;
                ulong postAmount = postTokenBalance != null &&
                                   postTokenBalance["uiTokenAmount"]?["amount"] != null
                    ? ulong.Parse(postTokenBalance["uiTokenAmount"]["amount"].GetValue<string>())
                    : 0;
                //Debug.WriteLine($"preTokenBalance:{JsonSerializer.Serialize(preTokenBalance)}");
                //Debug.WriteLine($"postTokenBalances:{JsonSerializer.Serialize(postTokenBalance)}");
                // 判断买入还是卖出
                if (preTokenBalances != null && postTokenBalances != null)
                {
                    isBuy = postAmount > preAmount; // amount增加，表示买入
                    if (isBuy)
                    {
                        mint = postTokenBalance["mint"]?.GetValue<string>();
                        decimals = postTokenBalance["uiTokenAmount"]?["decimals"]?.GetValue<int>() ?? 0;
                    }
                    else
                    {
                        mint = preTokenBalance["mint"]?.GetValue<string>();
                        decimals = preTokenBalance["uiTokenAmount"]?["decimals"]?.GetValue<int>() ?? 0;
                    }
                }
                else if (preTokenBalances == null && postTokenBalances != null)
                {
                    isBuy = true; // pre为空，post有值，表示买入
                    mint = postTokenBalance["mint"]?.GetValue<string>();
                    decimals = postTokenBalance["uiTokenAmount"]?["decimals"]?.GetValue<int>() ?? 0;
                }
                else if (preTokenBalances != null && postTokenBalances == null)
                {
                    isBuy = false; // pre有值，post为空，表示卖出
                    mint = preTokenBalance["mint"]?.GetValue<string>();
                    decimals = preTokenBalance["uiTokenAmount"]?["decimals"]?.GetValue<int>() ?? 0;
                }

                if (!isBuy) // 卖出，交换amounts[0]和amounts[1]
                {
                    ulong temp = amounts[0];
                    amounts[0] = amounts[1];
                    amounts[1] = temp;
                }

                return (amounts, isBuy, decimals, mint, owner);
            }
        }

        return (new ulong[] { 0, 0 }, false, 0, "", ""); // 如果amounts不满2条，返回空数组


    }

    public static (ulong[] amounts, bool isBuy, int decimals, string mint, string owner )
        ExtractJupiterAggregatorV6Data(string json)
    {
        try
        {
            JsonNode? node = JsonNode.Parse(json);
            return ProcessJupiterAggregatorV6Transaction(node);
        }
        catch (JsonException ex)
        {
            Console.WriteLine($" Jupiter Aggregator V6 JSON parsing error: {ex.Message}");
            return (new ulong[] { 0, 0 }, false, 0, "", "");
        }
    }

    private static (ulong[] amounts, bool isBuy, int decimals, string mint, string owner)
        ProcessJupiterAggregatorV6Transaction(
            JsonNode? node)
    {
        if (node == null) return (new ulong[] { 0, 0 }, false, 0, "", "");
        JsonArray? innerInstructions = node["transaction"]?["transaction"]?["meta"]?["innerInstructions"]?.AsArray();
        // 从accountKeys获取第一个owner
        JsonArray? accountKeys = node["transaction"]?["transaction"]?["message"]?["accountKeys"]?.AsArray();
        string owner = (accountKeys != null && accountKeys.Count > 0
            ? accountKeys[0]?["pubkey"]?.GetValue<string>()
            : null) ?? "";
        if (innerInstructions != null)
        {
            foreach (var inner in innerInstructions)
            {
                JsonArray? innerInstrs = inner["instructions"]?.AsArray();
                if (innerInstrs != null)
                {
                    foreach (var instr in innerInstrs)
                    {
                        string? innerProgramId = instr["programId"]?.GetValue<string>();
                        string? innerData = instr["data"]?.GetValue<string>();
                        if (innerProgramId == "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4" &&
                            innerData != null)
                        {
                            var data = new ReadOnlySpan<byte>(Base58.Decode(innerData));
                            var offset = 16;
                            var amm = Base58.Encode(data.Slice(offset, 32).ToArray());
                            offset += 32;
                            var inputMint = Base58.Encode(data.Slice(offset, 32).ToArray());
                            offset += 32;
                            var inputAmount = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                            offset += 8;
                            var outputMint = Base58.Encode(data.Slice(offset, 32).ToArray());
                            offset += 32;
                            var outputAmount = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                            var isBuy = inputMint == "So11111111111111111111111111111111111111112";
                            var mint = inputMint == "So11111111111111111111111111111111111111112"
                                ? outputMint
                                : inputMint;
                            ulong[] amounts = new ulong[2];
                            if (isBuy)
                            {
                                amounts[0] = inputAmount;
                                amounts[1] = outputAmount;
                            }
                            else
                            {
                                amounts[0] = outputAmount;
                                amounts[1] = inputAmount;
                            }

                            JsonArray? preTokenBalances = node["transaction"]?["meta"]?["preTokenBalances"]?.AsArray();
                            JsonArray? postTokenBalances =
                                node["transaction"]?["meta"]?["postTokenBalances"]?.AsArray();
                            var decimals = 0;
                            if (isBuy)
                            {
                                JsonNode? postTokenBalance =
                                    postTokenBalances?.FirstOrDefault(tb => tb["owner"]?.GetValue<string>() == owner
                                                                            && tb["mint"]?.GetValue<string>() == mint);
                                if (postTokenBalance != null)
                                {
                                    decimals = postTokenBalance["uiTokenAmount"]?["decimals"]?.GetValue<int>() ?? 0;
                                }
                            }
                            else
                            {
                                // 查找匹配owner的记录
                                JsonNode? preTokenBalance =
                                    preTokenBalances?.FirstOrDefault(tb => tb["owner"]?.GetValue<string>() == owner
                                                                           && tb["mint"]?.GetValue<string>() == mint);
                                if (preTokenBalances != null)
                                {
                                    decimals = preTokenBalance["uiTokenAmount"]?["decimals"]?.GetValue<int>() ?? 0;
                                }
                            }

                            return (amounts, isBuy, decimals, mint, owner);
                        }
                    }
                }
            }
        }

        return (new ulong[] { 0, 0 }, false, 0, "", "");
    }

    /// <summary>
    /// 从交易数据中提取与6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P相关的真实data。
    /// </summary>
    /// <param name="json">交易数据的JSON字符串。</param>
    /// <returns>真实data字符串，如果未找到则返回null。</returns>
    public static (ulong[] amounts, bool isBuy, int decimals, string mint, string owner) ExtractPumpFunData(string json)
    {
        try
        {
            JsonNode? node = JsonNode.Parse(json);
            return ProcessPumpFunTransaction(node);
        }
        catch (JsonException ex)
        {
            Console.WriteLine($"PumpFun JSON parsing error: {ex.Message}");
            return (new ulong[] { 0, 0 }, false, 0, "", "");
            ;
        }
    }

    private static (ulong[] amounts, bool isBuy, int decimals, string mint, string owner) ProcessPumpFunTransaction(
        JsonNode? node)
    {
        if (node == null) return (new ulong[] { 0, 0 }, false, 0, "", "");
        ;
// 从accountKeys获取第一个owner
        JsonArray? accountKeys = node["transaction"]?["transaction"]?["message"]?["accountKeys"]?.AsArray();
        string owner = accountKeys != null && accountKeys.Count > 0
            ? accountKeys[0]?["pubkey"]?.GetValue<string>()
            : null;
        JsonArray? instructions = node["transaction"]?["transaction"]?["message"]?["instructions"]?.AsArray();
        if (instructions == null)
        {
            instructions = node["transaction"]?["transaction"]?["instructions"]?.AsArray();
        }

        JsonNode? data = null;

        if (instructions != null)
        {
            data = instructions.Where(s =>
                s["programId"].GetValue<string>() == "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P" &&
                (s["accounts"] as JsonArray).Count() == 1).FirstOrDefault();
        }

        if (data == null)
        {
            var instructionsmeta = node["transaction"]?["meta"]?["innerInstructions"]?
                .AsArray().SelectMany(s => (s?["instructions"] as JsonNode).AsArray());
            data = instructionsmeta
                .Where(s => s["programId"].GetValue<string>() == "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"
                            && (s["accounts"] as JsonArray).Count() == 1).FirstOrDefault();
        }


        if (data != null)
        {
            //var data = GetRealDataFromInnerInstructions(node, instructionIndex);
            //if (!string.IsNullOrWhiteSpace(data))
            //{
            var i = Trade.Deserialize(Encoders.Base58.DecodeData(data["data"].ToString()), 16, out Trade trade);
            var amounts = new ulong[] { trade.Amount, trade.tokenAmount };
            var isBuy = trade.isBuy;
            var mint = trade.mint;
            return (amounts, isBuy, 6, mint, owner);
            //}
        }

        return (new ulong[] { 0, 0 }, false, 0, "", "");
    }


}