using Tran.Abp.JobServer.EventHandlers;
using Tran.Abp.JobServer.EventHandlers.Dto;

namespace Tran.Abp.JobServer.Job;

public class TransferMultipleToOneModel
{
    public string Chain { get; set; }
    public int TranType { get; set; }
    public string TranData { get; set; }
    public string ToPublicKey { get; set; }
    public decimal Percentage { get; set; }
    public decimal Amount { get; set; } = 0;
}

public class TransferMultipleToOneTask
{
    public TransferMultipleToOneTask(int uid, string fromPublicKey, string fromSecKey, string toPublicKey,
        decimal amount)
    {
        this.Uid = uid;
        this.FromPublicKey = fromPublicKey;
        this.FromSecKey = fromSecKey;
        this.ToPublicKey = toPublicKey;
        this.Amount = amount;
    }

    public string Chain { get; set; }
    public int Uid { get; }
    public string FromPublicKey { get; }
    public string FromSecKey { get; }
    public string ToPublicKey { get; }
    public decimal Amount { get; }
}

[TaskHandler(2)]
public class TransferMultipleToOneTaskHandler : ITaskHandler, IScopedDependency
{
    public ILogger<TransferMultipleToOneTaskHandler> Logger { get; set; }
    private SqlSugarRepository<UserWallet> _userWalletRepository;
    private IEventPublisher _eventPublisher;
    private ISolanaApi _solanaApi;
    private WorkQueue<TransferMultipleToOneTask> Queue;
    private int maxThread = 100;

    public TransferMultipleToOneTaskHandler(SqlSugarRepository<UserWallet> userWalletRepository,
        IEventPublisher eventPublisher, ISolanaApi solanaApi)
    {
        _userWalletRepository = userWalletRepository;
        _eventPublisher = eventPublisher;
        _solanaApi = solanaApi;
        Logger = NullLogger<TransferMultipleToOneTaskHandler>.Instance;
    }

    private async Task Send(TransferMultipleToOneTask item)
    {
        var fromAccount = Solnet.Wallet.Account.FromSecretKey(item.FromSecKey);
        var sign = await _solanaApi.Transaction(fromAccount, item.ToPublicKey, item.Amount);
        if (!string.IsNullOrWhiteSpace(sign))
        {
            //转账成功 发送消息
            await _eventPublisher.PublishAsync(JobEventConst.JobTransferTaskLogUpdata,
                new TransferTaskLogUpdataEvent(item.Uid, item.FromPublicKey, item.ToPublicKey, sign));
        }
    }

    public async Task<bool> HandleAsync(TaskModel task)
    {
        //{"TranType":0,"TranData":"9","ToPublicKey":"5kXzAtU58FKqUtZL7XBzXu26bhp5atBiywEo45AnPEgZ","Percentage":100}
        var para = task.Parameters;
        Logger.LogDebug($"开始处理 多对1转账=>{para}");
        var taskModel = JsonSerializer.Deserialize<TransferMultipleToOneModel>(para);
        //获取待转账号
        var targets = new List<Solnet.Wallet.Account>();

        if (taskModel.TranType == 2)
        {
            var arry = taskModel.TranData.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
            for (int i = 0; i < arry.Length; i++)
            {
                //导入
                var item = arry[i];
                if (item.Length < 86) continue;
                targets.Add(Solnet.Wallet.Account.FromSecretKey(item));
            }
        }
        else
        {
            var tid = taskModel.TranType == 1 ? Convert.ToInt32(taskModel.TranData) : 0;
            var list = await _userWalletRepository.AsQueryable()
                .Where(it => it.UId == task.UId)
                .WhereIF(taskModel.TranType == 0, it => it.GId == Convert.ToInt32(taskModel.TranData))
                //.WhereIF(taskModel.TranType == 1, it => SqlFunc.JsonArrayAny(it.TId, tid))
                .OrderByDescending(it=>it.OrderIndex)
                .ToListAsync();
            for (int i = 0; i < list.Count; i++)
            {
                var item = list[i].SecretKey;
                targets.Add(Solnet.Wallet.Account.FromSecretKey(item));
            }
        }

        //获取待转账号余额
        var address = targets.Select(it => it.PublicKey.Key).ToList();
        var total = address.Count / 100 + (address.Count % 100 > 0 ? 1 : 0);
        var dict = new Dictionary<string, decimal>();
        for (int ii = 0; ii < total; ii++)
        {
            var userAddress = address.Skip(ii * 100).Take(100).ToList();
            var balances = await _solanaApi.GetMultipleAccountsBalanceAsync(userAddress);

            foreach (var balance in balances)
            {
                //if (balance.Value > 0.000005)
                dict[balance.Key] = (decimal)balance.Value;
            }
        }

        //最大线程
        maxThread = targets.Count < maxThread ? targets.Count : 100;
        //任务 初始化 
        Queue = QueueManager.CreateQueue<TransferMultipleToOneTask>(Send,
            maxThread); //new WorkQueue<TransferMultipleToOneTask>(Send, maxThread);
        for (int i = 0; i < targets.Count; i++)
        {
            var item = targets[i];
            if (dict.TryGetValue(item.PublicKey.Key, out decimal realAmount))
            {
                //不足转账
                if (realAmount <= 0.000005m) continue;
                //真实转账 百分比
                var amount = realAmount * taskModel.Percentage / 100;
                //如果是百分百转 需要 扣除 最小 上链费用
                if (amount == realAmount) amount -= 0.000005m;
                //发送转账记录日志
                await _eventPublisher.PublishAsync(JobEventConst.JobTransferTaskLogAdd,
                    new TransferTaskLogEvent(
                        task.UId,
                        item.PrivateKey,
                        item.PublicKey,
                        taskModel.ToPublicKey,
                        "",
                        amount, task.Chain,0));
                //加入 线程
                Queue.Enqueue(new TransferMultipleToOneTask(task.UId, item.PublicKey.Key, item.PrivateKey.Key,
                    taskModel.ToPublicKey, amount));
            }
        }

        //结束任务
        await Queue.StopAsync();
        Logger.LogDebug($"处理完成 多对1转账");
        return true;
    }
}