using Solnet.Wallet;
using Tran.Abp.JobServer.BackgroundWorker;
using Tran.Abp.JobServer.EventHandlers;
using Tran.Abp.JobServer.EventHandlers.Dto;
using Tron.Abp.Core;

namespace Tran.Abp.JobServer.Job;

public class TransferOneToMultipleModel
{
    public string Chain { get; set; }
    public string SecretKey { get; set; }
    public int TranType { get; set; }
    public string TranData { get; set; }
    public double TimeMin { get; set; }
    public double TimeMax { get; set; }
    public double AmountMin { get; set; }
    public double AmountMax { get; set; }
    public int Level { get; set; }
}

public class TransferOneToMultipleTask
{
    public string Chain { get; set; }
    public int Id { get; }
    public int Uid { get; }
    public int Level { get; }
    public string FromPublicKey { get; }
    public string ToPublicKey { get; }
    public string FromSecKey { get; }
    public string ToSecKey { get; }
    public decimal Amount { get; }
    public int Time { get; }

    public TransferOneToMultipleTask(
        int id,
        int uid,
        int level,
        string fromPublicKey,
        string toPublicKey,
        string fromSecKey,
        string toSecKey,
        decimal amount,
        int time, string chain)
    {
        Id = id;
        Uid = uid;
        Level = level;
        FromPublicKey = fromPublicKey;
        ToPublicKey = toPublicKey;
        FromSecKey = fromSecKey;
        ToSecKey = toSecKey;
        Amount = amount;
        Time = time;
        Chain = chain;
    }
}

/// <summary>
/// 一对多转账
/// </summary>
[TaskHandler(1)]
public class TransferOneToMultipleTaskHandler : ITaskHandler, IScopedDependency
{
    public ILogger<TransferOneToMultipleTaskHandler> Logger { get; set; }
    private SqlSugarRepository<UserWallet> _userWalletRepository;
    private IEventPublisher _eventPublisher;
    private WorkQueue<List<TransferOneToMultipleTask>> Queue;
    private ISolanaApi _solanaApi;
    private int maxThread = 100;

    public TransferOneToMultipleTaskHandler(SqlSugarRepository<UserWallet> userWalletRepository, ISolanaApi solanaApi,
        IEventPublisher eventPublisher)
    {
        _userWalletRepository = userWalletRepository;
        _solanaApi = solanaApi;
        _eventPublisher = eventPublisher;

        Logger = NullLogger<TransferOneToMultipleTaskHandler>.Instance;
    }

    /// <summary>
    /// 执行转账任务
    /// </summary>
    /// <param name="key"></param>
    private async Task Send(List<TransferOneToMultipleTask> list)
    {
        // string secKey = key[0]; string pubKey = key[1];
        // var tranRanAmount = RandomHelper.GetRandomDouble((double)TranOutAmountMin,
        //     (double)TranOutAmountMax);
        // var fromAccount = Solnet.Wallet.Account.FromSecretKey(secKey);
        // var sign = await solanaApi.Transaction(fromAccount, pubKey, tranRanAmount);
        //Logger.LogDebug($"任务{key[2]}=>https://solscan.io/tx/");

        for (int i = 0; i < list.Count; i++)
        {
            var tryCount = 0;
            var item = list[i];
            var fromAccount = Solnet.Wallet.Account.FromSecretKey(item.FromSecKey);
            //最后一层等侍时间
            if (i == list.Count - 1)
            {
                await Task.Delay(item.Time);
            }
            do
            {
                //重试3次
                var sign = await _solanaApi.Transaction(fromAccount, item.ToPublicKey, item.Amount);
                Logger.LogDebug($"第{tryCount}次执行任务{item.Id}=》{i}=>https://solscan.io/tx/{sign}");
                if (!string.IsNullOrWhiteSpace(sign))
                {
                    //转账成功 发送消息
                    await _eventPublisher.PublishAsync(JobEventConst.JobTransferTaskLogUpdata,
                        new TransferTaskLogUpdataEvent(item.Uid, item.FromPublicKey, item.ToPublicKey, sign));
                    break;
                }
                else
                {
                    tryCount++;
                    await Task.Delay(200);
                }
            } while (tryCount < 3);

            if (tryCount >= 3) //重试过多，结束任务
            {
                //todo:此处 回退 转账金额
                for (int j = i - 1; j > 0; j--)
                {
                    var tmpAccount = Solnet.Wallet.Account.FromSecretKey(list[j].FromSecKey);
                    var sign = await _solanaApi.Transaction(tmpAccount, list[0].ToPublicKey, 0);
                    Logger.LogWarning($"转账退回{tmpAccount.PublicKey}=>{sign}");
                    await Task.Delay(500);
                }

                break;
            }

            var tranRanAmount = item.Amount - 0.000005m;
            while (true) //等待 目标账号到账，再进行下次转账
            {
                var balance = await _solanaApi.GetBalanceAsync(item.ToPublicKey);
                Logger.LogDebug($"任务{item.Id}=>{i}=>{fromAccount.PublicKey}=>{balance}=>{tranRanAmount}");
                if (balance > tranRanAmount)
                {
                    await Task.Delay(500);
                    break;
                }

                await Task.Delay(200);
            }
        }
    }

    public async Task<bool> HandleAsync(TaskModel task)
    {
        //{"SecretKey":"2feiGRz3ZFjMgAzath31LDsRuastxcX82B3KiNH9WPMJ5rBsoXJTr551DvzcMtR75ybYcFavaoKK3cGk5suMXq7X","TranType":0,"TranData":"1","TimeMin":0.1,"TimeMax":0.1,"AmountMin":0.1,"AmountMax":0.1,"Level":5}
        var para = task.Parameters;
        Logger.LogDebug($"开始处理 1对多转账=>{para}");
        var taskModel = JsonSerializer.Deserialize<TransferOneToMultipleModel>(para);

        var account = Solnet.Wallet.Account.FromSecretKey(taskModel.SecretKey);
        //最终目标账号
        var targets = new List<Solnet.Wallet.PublicKey>();
        if (taskModel.TranType == 2)
        {
            var arry = taskModel.TranData.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
            for (int i = 0; i < arry.Length; i++)
            {
                if (PublicKey.IsValid(arry[i]))
                    targets.Add(new PublicKey(arry[i]));
            }
        }
        else
        {
            //var tid = taskModel.TranType == 1 ? Convert.ToInt32(taskModel.TranData) : 0;
            //2025 05 14 16 37 分组 可多选
            var gids = taskModel.TranData.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries)
                .Select(it => int.Parse(it)).ToList();
            foreach (var gid in gids)
            {
                var list = await _userWalletRepository.AsQueryable()
                    .Where(it => it.UId == task.UId)
                    .WhereIF(taskModel.TranType == 0, it => it.GId == gid)
                    //.WhereIF(taskModel.TranType == 1, it => SqlFunc.JsonArrayAny(it.TId, tid))
                    .OrderByDescending(it=>it.OrderIndex)
                    .ToListAsync();
                for (int i = 0; i < list.Count; i++)
                {
                    if (PublicKey.IsValid(list[i].PubKey))
                        targets.Add(new PublicKey(list[i].PubKey));
                }
            }
        }

        //最大线程
        maxThread = 1; /*targets.Count < maxThread ? targets.Count : 100*/
        ;
        //任务 初始化 
        Queue = QueueManager.CreateQueue<List<TransferOneToMultipleTask>>(Send,
            maxThread); //new WorkQueue<List<TransferOneToMultipleTask>>(Send, maxThread);
        //生成 任务记录
        //var dict = new Dictionary<int, List<Solnet.Wallet.Account>>();
        for (int i = 0; i < targets.Count; i++)
        {
            //生成 临时账号
            var list = Enumerable.Range(0, taskModel.Level).Select(it =>
                {
                    var wallet = new Solnet.Wallet.Account();
                    return wallet;
                }
            ).ToList();
            //写入 钱包临时库
            await _eventPublisher.PublishAsync(JobEventConst.JobUserWalletTmpAdd,
                new UserWalletTempEvent(task.UId, list));
            var tmpAcc = targets[i];
            if (tmpAcc == account.PublicKey) continue;
            //随机 时间
            var time = RandomHelper.GetRandom((int)(taskModel.TimeMin * 1000), (int)(taskModel.TimeMax * 1000));
            //随机 金额
            var amount = RandomHelper.GetRandomDouble(taskModel.AmountMin, taskModel.AmountMax);
            //dict[i] = list;
            //生成 任务记录
            List<TransferOneToMultipleTask> sendList = new List<TransferOneToMultipleTask>();
            for (int j = 0; j <= taskModel.Level; j++)
            {
                amount = j > 0 ? amount - 0.000005m : amount;
                var from = j > 0 ? list[j - 1] : account;
                //var to = list[j];
                var toPubKey = ""; // to.PublicKey.Key;
                var toPrivateKey = ""; // to.PrivateKey.Key;
                if (j == taskModel.Level) //最后转给目标
                {
                    toPubKey = targets[i];
                    toPrivateKey = "";
                }
                else
                {
                    var to = list[j];
                    toPubKey = to.PublicKey.Key;
                    toPrivateKey = to.PrivateKey.Key;
                }

                Console.WriteLine($"{j}=>{from.PublicKey}=>{toPubKey}=>{amount}");
                //写入转账 任务日志
                await _eventPublisher.PublishAsync(JobEventConst.JobTransferTaskLogAdd,
                    new TransferTaskLogEvent(
                        task.UId,
                        from.PrivateKey,
                        from.PublicKey,
                        toPubKey,
                        toPrivateKey,
                        amount, task.Chain, taskModel.Level - j));

                sendList.Add(new TransferOneToMultipleTask(i, task.UId, j, from.PublicKey, toPubKey, from.PrivateKey,
                    toPrivateKey, amount, time, task.Chain));
            }

            Queue.Enqueue(sendList);
        }

        await Queue.StopAsync();
        Logger.LogDebug($"处理完成 1对多转账");
        return true;
    }
}