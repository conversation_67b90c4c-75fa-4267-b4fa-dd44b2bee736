namespace Tran.Abp.JobServer.EventHandlers.Dto;

public class TransferTaskLogEvent
{
    public TransferTaskLogEvent(int uid,string frmSecretKey,string frmPubKey,string toPubKey,string toSecretKey,decimal amount, string chain, int level)
    {
        this.Uid = uid;
        this.FrmSecretKey = frmSecretKey;
        this.FrmPubKey = frmPubKey;
        this.ToSecretKey = toSecretKey;
        this.ToPubKey = toPubKey;
        this.Amount = amount;
        Chain = chain;
        Level = level;
    }
    public int Uid { get; set; }
    public string FrmPubKey { get; set; }
    /// <summary>
    /// 发起私钥
    /// </summary>
    public string FrmSecretKey { get; set; }
    public decimal Amount { get; set; }
    /// <summary>
    /// 接受公钥
    /// </summary>
    public string ToPubKey { get; set; }
    /// <summary>
    /// 接受私钥
    /// </summary>
    public string ToSecretKey { get; set; }
    public string Chain { get; set; }
    
    public int Level { get; set; }
}

public class TransferTaskLogUpdataEvent
{
    public TransferTaskLogUpdataEvent(int uid,string fromPubKey,string toPubKey,string sign)
    {
        this.ToPubKey = toPubKey;
        this.FromPubKey = fromPubKey;
        this.Uid = uid;
        this.Sign = sign;
    }

    public int Uid { get; set; }
    public string FromPubKey { get; set; }
    public string ToPubKey { get; set; }
    public string Sign { get; set; }
}