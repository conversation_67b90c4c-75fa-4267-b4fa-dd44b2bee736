using FreeRedis;
using Mapster;
using Tran.Abp.JobServer.EventHandlers.Dto;
using Tron.Abp.Domain.Contracts.Enum;
using Tron.Abp.Multiplex.Contracts;

namespace Tran.Abp.JobServer.EventHandlers;
public record AllTradeUpdateAmountEvent(int Id, string Sign, string Mint, string DexType, bool IsBuy,string TradeType,string Owner,int Percentage);
public class JobEventSubscriber : IEventSubscriber, ISingletonDependency
{
    public ILogger<JobEventSubscriber> Logger { get; set; }
    private SqlSugarRepository<UserWalletTemp> _userWalletTempRepository;
    private SqlSugarRepository<UserTransferLog> _userTransferLogRepository;
    private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;
    private ISolanaApi _solanaApi;
    private IRedisClient _redisClient;
    public JobEventSubscriber(SqlSugarRepository<UserWalletTemp> userWalletTempRepository,
        SqlSugarRepository<UserTransferLog> userTransferLogRepository, SqlSugarRepository<UserAllTraderLog> userAllTraderLogRepository, ISolanaApi solanaApi, IRedisClient redisClient)
    {
        _userWalletTempRepository = userWalletTempRepository;
        _userTransferLogRepository = userTransferLogRepository;
        _userAllTraderLogRepository = userAllTraderLogRepository;
        _solanaApi = solanaApi;
        _redisClient = redisClient;
        Logger = NullLogger<JobEventSubscriber>.Instance;
    }

    /// <summary>
    /// 写入临时钱包
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(JobEventConst.JobUserWalletTmpAdd)]
    public async Task JobUserWalletTmpAddOnEventAsync(EventHandlerExecutingContext context)
    {
        var userWalletTemp = (Dto.UserWalletTempEvent)context.Source.Payload;
        if (userWalletTemp == null || userWalletTemp.Accounts.Count <= 0) return;
        var list = new List<UserWalletTemp>();
        foreach (var uwt in userWalletTemp.Accounts)
        {
            list.Add(new UserWalletTemp
            {
                UId = userWalletTemp.Uid,
                PubKey = uwt.PublicKey,
                SecretKey = uwt.PrivateKey,
                IsUser = true,
                CreateTime = DateTime.Now,
            });
        }

        await _userWalletTempRepository.CopyNew().AsInsertable(list).ExecuteCommandAsync();
        await Task.CompletedTask;
    }
    /// <summary>
    /// 写转账日志
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(JobEventConst.JobTransferTaskLogAdd)]
    public async Task JobTransferTaskLogAddOnEventAsync(EventHandlerExecutingContext context)
    {
        var taskLogs = (Dto.TransferTaskLogEvent)context.Source.Payload;

        var log = new UserTransferLog();
        log.Chain=taskLogs.Chain;
        log.UId = taskLogs.Uid;
        log.FrmPubKey = taskLogs.FrmPubKey;
        log.FrmSecretKey = taskLogs.FrmSecretKey;
        log.ToPubKey = taskLogs.ToPubKey;
        log.ToSecretKey = taskLogs.ToSecretKey;
        log.Amount = taskLogs.Amount;
        log.CreateTime = DateTime.Now;
        log.Status = TransferEnum.State_2;
        log.Signature = "";
        log.Level=taskLogs.Level;
        await _userTransferLogRepository.CopyNew().AsInsertable(log).ExecuteCommandAsync();
    }
    /// <summary>
    /// 更新转账日志
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(JobEventConst.JobTransferTaskLogUpdata)]
    public async Task JobTransferTaskLogUpdataOnEventAsync(EventHandlerExecutingContext context)
    {
        var taskLog = (Dto.TransferTaskLogUpdataEvent)context.Source.Payload;

        var log = await _userTransferLogRepository.CopyNew().AsQueryable()
            .Where(it => it.UId == taskLog.Uid && it.FrmPubKey == taskLog.FromPubKey && it.ToPubKey == taskLog.ToPubKey)
            .FirstAsync();
        if (log == null) return;

        await _userTransferLogRepository.CopyNew().AsUpdateable()
            .SetColumns(it => it.CompleteTime == DateTime.Now)
            .SetColumns(it => it.Status == TransferEnum.State_3)
            .SetColumns(it => it.Signature == taskLog.Sign)
            .Where(it => it.UId == taskLog.Uid && it.FrmPubKey == taskLog.FromPubKey && it.ToPubKey == taskLog.ToPubKey)
            .ExecuteCommandAsync();
    }
    [EventSubscribe(JobEventConst.JobTokenSellByPlayerUpDateSolAmount)]
    public async Task<bool> TokenSellByPlayerAsync(EventHandlerExecutingContext context)
    {
        var sw = (AllTradeUpdateAmountEvent)context.Source.Payload;
        
        var log =await _userAllTraderLogRepository.AsQueryable().Where(it=>it.Id==sw.Id).FirstAsync();
        if(log == null) return false;
        //查询交易
        var (solAmount, tokenAmount) = (log.SolAmount, log.TokenAmount);
        var i = 0;
        while (solAmount == 0 && tokenAmount == 0)
        {
            if (i > 10) break;
            await Task.Delay(1500);
            (solAmount, tokenAmount) = await _solanaApi.ParserTransaction(sw.Sign, sw.Mint, sw.DexType, sw.IsBuy);
            i++;
           
        }
        
        var wsolStr = await _redisClient.GetAsync(RedisKey.SolanaWSOLPrice) ?? "0";
        var wsol = Decimal.Parse(wsolStr);
        var isSuccessful = !(solAmount == 0 && tokenAmount == 0);
        var tokenPrice =isSuccessful?solAmount / tokenAmount:0m;
        var tokenUsdcPrice = isSuccessful?solAmount / tokenAmount*wsol:0m;
        var Status = isSuccessful ? $"{sw.TradeType}-成功" : $"{sw.TradeType}-失败";

        
        //更新记录
        await _userAllTraderLogRepository.CopyNew().AsUpdateable()
            .SetColumns(it => it.TokenAmount == tokenAmount)
            .SetColumns(it => it.SolAmount == solAmount)
            .SetColumns(it=>it.Signature==sw.Sign)
            .SetColumns(it=>it.CompleteTime==DateTime.Now)
            .SetColumns(it=>it.TokenSolPrice == tokenPrice)
            .SetColumns(it=>it.TokenUsdtPrice == tokenUsdcPrice)
            .SetColumns(it=>it.Status==Status)
            .Where(it => it.Id == log.Id)
            .ExecuteCommandAsync();
        
        return true;
    }

}