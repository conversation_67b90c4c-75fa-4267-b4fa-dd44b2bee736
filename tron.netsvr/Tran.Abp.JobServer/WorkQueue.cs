using System.Threading.Channels;
using System.Threading.Tasks.Dataflow;

namespace Tran.Abp.JobServer;


public static class QueueManager
{
    private static readonly SemaphoreSlim GlobalSemaphore = new SemaphoreSlim(200); // 全局最大 200 个线程

    public static WorkQueue<T> CreateQueue<T>(Func<T, Task> processor, int parallelism = 1)
    {
        return new WorkQueue<T>(processor, parallelism, GlobalSemaphore);
    }
}

public sealed class WorkQueue<T>
{
    private readonly Channel<T> channel;
    private readonly Func<T, Task> processor;
    private readonly Task[] workers;
    private readonly CancellationTokenSource[] cancellations;
    private volatile bool stopped;
    private readonly SemaphoreSlim globalSemaphore;

    public WorkQueue(Func<T, Task> processor, int parallelism, SemaphoreSlim globalSemaphore)
    {
        this.processor = processor ?? throw new ArgumentNullException(nameof(processor));
        this.globalSemaphore = globalSemaphore;
        channel = Channel.CreateUnbounded<T>();
        cancellations = Enumerable.Range(0, parallelism).Select(i => new CancellationTokenSource()).ToArray();
        workers = Enumerable.Range(0, parallelism)
            .Select(i => Task.Run(ProcessLoop, cancellations[i].Token)).ToArray();
    }

    public bool Enqueue(T item)
    {
        if (stopped)
            return false;
        return channel.Writer.TryWrite(item); // 返回是否成功入队
    }

    public async Task StopAsync(TimeSpan? timeout = null)
    {
        stopped = true;
        try
        {
            channel.Writer.Complete(); // 通知 Channel 完成

            if (timeout.HasValue)
                await Task.WhenAny(Task.WhenAll(workers), Task.Delay(timeout.Value));
            else
            {
                foreach (var source in cancellations)
                {
                    await source.CancelAsync();
                }

                await Task.WhenAll(workers);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }
    }

    private async Task ProcessLoop()
    {
        try
        {
            while (await channel.Reader.WaitToReadAsync())
            {
                while (channel.Reader.TryRead(out var item))
                {
                    await globalSemaphore.WaitAsync();
                    try
                    {
                        await processor(item);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"处理队列项时出错: {ex}");
                    }
                    finally
                    {
                        globalSemaphore.Release();
                    }
                }
            }
        }
        catch (ChannelClosedException exception)
        {
            // Channel 已关闭，正常退出
            Console.WriteLine($"已关闭，正常退出: {exception}");
        }

        Console.WriteLine("ProcessLoop 已退出");
    }

    public int Count => channel.Reader.Count;
    /*
    private readonly BufferBlock<T> queue = new BufferBlock<T>();
    private readonly Func<T, Task> processor;
    private volatile bool stopped;
    private readonly Task runLoopTask;

    public WorkQueue(Func<T, Task> processor, int parallelism = 1)
    {
        this.processor = processor ?? throw new ArgumentNullException(nameof(processor));
        runLoopTask = Task.WhenAll(Enumerable.Range(0, parallelism).Select(_ => Task.Run(ProcessLoop)));
    }

    public void Enqueue(T item)
    {
        if (!stopped)
            queue.Post(item);
    }

    public async Task StopAsync(TimeSpan? timeout = null)
    {
        stopped = true;
        queue.Complete(); // 通知 BufferBlock 完成

        if (timeout.HasValue)
        {
            await Task.WhenAny(runLoopTask, Task.Delay(timeout.Value)).ConfigureAwait(false);
            if (!runLoopTask.IsCompleted)
            {
                Console.WriteLine($"WorkQueue 停止超时，剩余队列项: {queue.Count}");
            }
        }
        else
        {
            await runLoopTask.ConfigureAwait(false);
        }
    }

    private async Task ProcessLoop()
    {
        while (!stopped || queue.Count > 0)
        {
            try
            {
                var item = await queue.ReceiveAsync().ConfigureAwait(false);
                await processor(item).ConfigureAwait(false);
            }
            catch (InvalidOperationException)
            {
                // BufferBlock 已完成，退出循环
                break;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理队列项时出错: {ex.Message}");
                // 继续处理下一项或退出（根据需求调整）
            }
        }
        Console.WriteLine("ProcessLoop 已退出");
    }

    public int Count => queue.Count;*/
}