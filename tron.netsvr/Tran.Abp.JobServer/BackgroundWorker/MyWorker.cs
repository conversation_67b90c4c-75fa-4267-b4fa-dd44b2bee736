using System.Reflection;
using System.Threading.Channels;
using FreeRedis;
using FreeRedis.Internal;
using Tron.Abp.Multiplex.Contracts;
using Volo.Abp.BackgroundWorkers;

namespace Tran.Abp.JobServer.BackgroundWorker;

public class MyWorker : BackgroundWorkerBase
{
    public ILogger<MyWorker> Logger { get; set; }
    private IRedisClient _redisClient;
    private SubscribeStreamObject _subscribeStreamObject;
    private readonly SqlSugarRepository<UserTransferTask> _userTransferTaskRepository;

    private readonly IServiceProvider _serviceProvider;
    private readonly Channel<TaskModel> _queue;
    private readonly Dictionary<int, ITaskHandler> _handlers;
    private readonly SemaphoreSlim _semaphore;

    public MyWorker(IRedisClient redisClient, SqlSugarRepository<UserTransferTask> userTransferTaskRepository,
        IServiceProvider serviceProvider)
    {
        _redisClient = redisClient;
        _userTransferTaskRepository = userTransferTaskRepository;
        _serviceProvider = serviceProvider;
        Logger = NullLogger<MyWorker>.Instance;

        // 创建无限容量的通道
        _queue = Channel.CreateUnbounded<TaskModel>();
        // 初始化处理器字典
        _handlers = InitializeHandlers();

        _semaphore = new SemaphoreSlim(5);
    }

    /// <summary>
    /// 初始化任务处理器
    /// </summary>
    private Dictionary<int, ITaskHandler> InitializeHandlers()
    {
        var handlers = new Dictionary<int, ITaskHandler>();

        var handlerTypes = _serviceProvider.GetServices<ITaskHandler>();

        foreach (var handler in handlerTypes)
        {
            var attribute = handler.GetType().GetCustomAttribute<TaskHandlerAttribute>();
            if (attribute != null)
            {
                handlers[attribute.TaskType] = handler;
            }
        }

        return handlers;
    }

    /// <summary>
    /// 添加任务到队列
    /// </summary>
    private async Task EnqueueAsync(TaskModel task)
    {
        await _queue.Writer.WriteAsync(task);
        Logger.LogInformation($"任务已加入队列: {task.Id} - {task.Name}");
    }

    /// <summary>
    /// 处理单个任务
    /// </summary>
    private async Task ProcessTaskAsync(TaskModel task)
    {
        try
        {
            if (_handlers.TryGetValue(task.TaskType, out var handler))
            {
                task.Status = TaskStatus.WaitingToRun;
                Logger.LogInformation($"开始处理任务: {task.Id} - {task.Name}");

                var success = await handler.HandleAsync(task);
                task.Status = success ? TaskStatus.RanToCompletion : TaskStatus.Faulted;

                Logger.LogInformation($"任务处理{(success ? "成功" : "失败")}: {task.Id}");
            }
            else
            {
                task.Status = TaskStatus.Faulted;
                Logger.LogInformation($"未找到任务类型 {task.TaskType} 的处理器");
            }
        }
        catch (Exception ex)
        {
            task.Status = TaskStatus.Faulted;
            Logger.LogInformation($"处理任务 {task.Id} 时发生异常: {ex.Message}{ex.StackTrace}");
            //throw;
        }
    }

    private async Task Monitor(CancellationToken cancellationToken)
    {
        //开启任务处理
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                // 从队列中读取任务
                var task = await _queue.Reader.ReadAsync(cancellationToken);

                // 等待信号量
                await _semaphore.WaitAsync(cancellationToken);

                // 异步处理任务
                _ = ProcessTaskAsync(task)
                    .ContinueWith(t =>
                    {
                        _semaphore.Release();
                        if (t.IsFaulted)
                        {
                            Logger.LogInformation($"处理任务 {task.Id} 时发生错误: {t.Exception.Message}{t.Exception.StackTrace}");
                        }
                    }, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                Logger.LogInformation($"处理队列时发生错误: {ex.Message}{ex.StackTrace}");
                await Task.Delay(1000, cancellationToken);
            }
        }
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        Logger.LogInformation($"开始订阅消息{StreamKey.StreamTransfer}");
        _subscribeStreamObject = _redisClient.SubscribeStream(StreamKey.StreamTransfer, onMessage: OnMessage);

        Task.Run(() => Monitor(cancellationToken), cancellationToken);

        await base.StartAsync(cancellationToken);
    }

    //处理
    private void OnMessage(Dictionary<string, string> stream)
    {
        Logger.LogInformation($"收到消息{StreamKey.StreamTransfer}=》{stream.Count}");

        if (stream.Count <= 0) return;

        var uid = stream["uid"].To<int>();
        var tid = stream["tid"].To<int>();
        //处理 各类 任务 分发任务
        var userTask = _userTransferTaskRepository.AsQueryable()
            .Where(it => it.UId == uid && it.Id == tid && it.Status == 0).First();

        if (userTask == null)
        {
            Logger.LogWarning($"用户{uid}=>任务{tid}=>没有找到");
            return;
        }

        _userTransferTaskRepository.AsUpdateable().SetColumns(it => it.Status == 1)
            .Where(it => it.UId == uid && it.Status == 0 && it.Id == tid).ExecuteCommand();
        var taskModel = new TaskModel()
        {
            Chain = userTask.Chain,
            Id = userTask.Id,
            UId = uid,
            Name = $"用户{uid}=>任务{tid}",
            TaskType = userTask.TaskType,
            Status = TaskStatus.WaitingForActivation,
            Parameters = userTask.TaskParas,
            CreateTime = userTask.CreateTime.To<DateTime>(),
        };
        EnqueueAsync(taskModel).Wait();
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        Logger.LogInformation($"取消订阅消息{StreamKey.StreamTransfer}");
        _subscribeStreamObject?.Dispose();
        await base.StopAsync(cancellationToken);
    }
}