using Grpc.Net.Client;
using Jaina.EventBus;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using NATS.Client.Core;
using NATS.Client.Serializers.Json;
using NATS.Net;
using Tran.Abp.MonitorServer.BackgroundWorker;
using Tran.Abp.MonitorServer.Options;
using Tron.Abp.Caching.FreeRedis;
using Tron.Abp.Multiplex;
using Volo.Abp;
using Volo.Abp.Autofac;
using Volo.Abp.Modularity;

namespace Tran.Abp.MonitorServer;

[DependsOn(typeof(AbpAutofacModule),
    typeof(AbpJainaEventBusModule),
    typeof(CachingFreeRedisModule),
    typeof(AbpDomainModule),
    typeof(AbpMultiplexModule),
    typeof(AbpBackgroundWorkersModule))]
public class MonitorServerModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        base.ConfigureServices(context);

        context.Services.AddEventBus();
        var configuration = context.Services.GetConfiguration();
        //grpc 配置
        /*var grpcConf = configuration.GetSection("Grpc").Get<GrpcOptions>();
        Configure<GrpcOptions>(options =>
        {
            options.Commitment = grpcConf.Commitment;
            options.Endpoint = grpcConf.Endpoint;
        });
        var channelOptions = new GrpcChannelOptions
        {
            MaxReceiveMessageSize = 128 * 1024 * 1024, // 128MB，匹配 Yellowstone 的需求
        };
        GrpcChannel channel = GrpcChannel.ForAddress(grpcConf.Endpoint, channelOptions);
        Geyser.GeyserClient client = new Geyser.GeyserClient(channel);
        context.Services.AddSingleton<Geyser.GeyserClient>(client);*/
        
        var natsOption = configuration.GetSection("Nats").Get<NatsOptions>();
        Configure<NatsOptions>(options =>
        {
            options.IsEnabled = natsOption.IsEnabled;
            options.Url = natsOption.Url;
            options.Ws = natsOption.Ws;
            options.UserName = natsOption.UserName;
            options.PassWord = natsOption.PassWord;
        });
        
        var natsClient=new NatsClient(NatsOpts.Default with
        {
            Url = natsOption.Url,
            SerializerRegistry = NatsJsonSerializerRegistry.Default,
            AuthOpts = new NatsAuthOpts()
            {
                Username = natsOption.UserName,
                Password = natsOption.PassWord
            }
        });
        context.Services.AddSingleton(natsClient);
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        base.OnApplicationInitialization(context);
        var _logger = context.ServiceProvider.GetRequiredService<ILogger<MonitorServerModule>>();
        var hostEnvironment = context.ServiceProvider.GetRequiredService<IHostEnvironment>();
        _logger.LogDebug($"MonitorServerModule 加载成功=>EnvironmentName => {hostEnvironment.EnvironmentName}");

        //导入任务 ping任务
        //context.AddBackgroundWorkerAsync<PingWorker>();
        //池子服务
        //context.AddBackgroundWorkerAsync<PoolWorker>();
        //实时价格服务
        //context.AddBackgroundWorkerAsync<PriceWorker>();
        //跟单钱包 
        //context.AddBackgroundWorkerAsync<CopyTradeWorker>();
        //快速交易 子任务服务
        context.AddBackgroundWorkerAsync<QuickTradeWorker>();
        //跟单钱包 子任务服务
        context.AddBackgroundWorkerAsync<CopyTradeTaskWorker>();
        //延时 跟单 购买任务 
        context.AddBackgroundWorkerAsync<CopyTradeDelayBuyTaskWorker>();
        //采集数据 来自 jup.ag
        //context.AddBackgroundWorkerAsync<JupAgWorker>();
        //代币持有 清理
        context.AddBackgroundWorkerAsync<RemoveTokenInventory>();
        //context.AddBackgroundWorkerAsync<TestWork>();
        //代币持有 卖出 百分百
        context.AddBackgroundWorkerAsync<UserMyTokensSellTaskWorker>();
    }
}