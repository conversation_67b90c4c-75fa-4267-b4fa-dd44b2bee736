using System.Collections.Concurrent;
using System.Diagnostics;
using FreeRedis.Internal;
using Solnet.Wallet;
using Tran.Abp.MonitorServer.BackgroundWorker.Dto;
using Tran.Abp.MonitorServer.EventHandlers;
using Tran.Abp.MonitorServer.EventHandlers.Dto;
using Tron.Abp.Core.Extensions;
using Tron.Abp.Multiplex.Solana;

namespace Tran.Abp.MonitorServer.BackgroundWorker;

/// <summary>
/// 快速买入止盈止损
/// </summary>
public class QuickTradeWorker : BackgroundWorkerBase
{
    public ILogger<QuickTradeWorker> Logger { get; set; }
    private IRedisClient _redisClient;
    private SubscribeStreamObject streamCopyTradeTask;
    private CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();
    private Dictionary<string, WorkQueue<CTTask>> dictionary;

    private ConcurrentDictionary<string, CancellationTokenSource> dictionaryLock =
        new ConcurrentDictionary<string, CancellationTokenSource>();

    private readonly SqlSugarRepository<UserQuickTradeTask> _userQuickTradeTaskRepository;
    private IEventPublisher _eventPublisher;
    private ISolanaApi _solanaApi;


    public QuickTradeWorker(IRedisClient redisClient,
        SqlSugarRepository<UserQuickTradeTask> userQuickTradeTaskRepository, IEventPublisher eventPublisher,
        ISolanaApi solanaApi)
    {
        _redisClient = redisClient;
        _userQuickTradeTaskRepository = userQuickTradeTaskRepository;
        _eventPublisher = eventPublisher;
        _solanaApi = solanaApi;
        Logger = NullLogger<QuickTradeWorker>.Instance;
        dictionary = new Dictionary<string, WorkQueue<CTTask>>();
    }

    private async Task<bool> IsInWorkQueue(string uidKey, string logID, string mint)
    {
        // 获取或初始化 mintDict
        var mintDict = await _redisClient.HExistsAsync(RedisKey.QuickTradeList, uidKey)
            ? await _redisClient.HGetAsync<Dictionary<string, List<string>>>(RedisKey.QuickTradeList, uidKey) ??
              new Dictionary<string, List<string>>()
            : new Dictionary<string, List<string>>();

        // 检查并添加 mint
        var mintList = mintDict.TryGetValue(logID, out var list) ? list : mintDict[logID] = new List<string>();
        if (mintList.Contains(mint))
        {
            return true; // 已存在，返回 true
        }

        mintList.Add(mint);
        await _redisClient.HSetAsync(RedisKey.QuickTradeList, uidKey, mintDict);
        return false; // 不存在，已添加，返回 false
    }

    private async Task PriceMonitor(CTTask item)
    {
        Debug.WriteLine($"{item.LogId}=>{Thread.CurrentThread.ManagedThreadId}");
        while (!(dictionaryLock[$"{item.UId}_{item.LogId}"].IsCancellationRequested))
        {
            //Debug.WriteLine($"{item.LogId}=>{Thread.CurrentThread.ManagedThreadId}");
            //卖出百分比
            var sellPercentage = 0;
            var taskId = 0;
            var logId = item.LogId;
            var runId = 0;
            //取任务 信息
            // StopLimitMode  自动=1 移动=2
            // StopLimitMode=1 AutoModel 1止盈 2止损
            /*var list = await _userQuickTradeTaskRepository.CopyNew().AsQueryable()
                .Where(it => it.LogId == item.LogId && it.Status == "active" && it.IsRuning)
                .ToListAsync();
            */
            var listItems = await _redisClient.HExistsAsync(RedisKey.QuickTradeSubTaskList,
                item.LogId)
                ? await _redisClient.HGetAsync<List<UserQuickTradeTask>>(RedisKey.QuickTradeSubTaskList,
                    item.LogId)
                : new List<UserQuickTradeTask>();
            var list = listItems.Where(it => it.Status == "active").ToList();
            var isExpired = false;
            var listItem = list.FirstOrDefault(it => DateTime.Now >= it.ExpiredTime /*it.ExpiredAuto == true*/);
            if (listItem != null /* && DateTime.Now >= listItem.ExpiredTime*/)
            {
                Logger.LogDebug($"快速交易 子任务 {item.LogId} Mint:{item.Mint} 过期 ");
                //过期处理
                //所有任务完成
                sellPercentage = listItem.ExpiredAuto ? 100 : 0;
                isExpired = true;

                await _userQuickTradeTaskRepository.CopyNew().AsUpdateable()
                    .SetColumns(it => it.CompleteTime == DateTime.Now)
                    .SetColumns(it => it.Status == "canceled")
                    .Where(it => it.LogId == item.LogId && it.Status == "active")
                    .ExecuteCommandAsync();

                goto Next;
            }

            //移动
            var moveData = list.FirstOrDefault(it => it.StopLimitMode == 2);
            //止盈
            var riseData = list.FirstOrDefault(it => it.StopLimitMode == 1 && it.AutoMode == 1);
            //止损
            var slumpData = list.FirstOrDefault(it => it.StopLimitMode == 1 && it.AutoMode == 2);
            //没有任务了
            if (moveData == null && slumpData == null && riseData == null) break;
            //取价格
            var tokenPrice = await _solanaApi.GetMintPrice(item.Mint, item.DexType, item.PoolId);
            ;

            Logger.LogDebug($"快速交易 子任务 {item.Mint} => {tokenPrice}");
            if (tokenPrice == 0)
            {
                goto Next;
                ;
            }

            //取小数后9位小数
            //tokenPrice = tokenPrice.ToTruncate(9);

            //移动 止损
            if (moveData != null && moveData.IsRuning)
            {
                /*if (DateTime.Now >= moveData.ExpiredTime)
                {
                    moveData.CompleteTime = DateTime.Now;
                    moveData.Status = "canceled";

                    taskId = moveData.Id;
                    //卖出百分比
                    sellPercentage = moveData.ExpiredAuto ? 100 : 0;
                }*/

                runId = moveData.Id;
                //todo: 初始时，网络因素取不取价格 固做判断 0401 取消判断
                //满足条件的最高价格
                var dbHighestPrice = moveData.BuyPrice * (1 + moveData.MoveData.PriceRatio / 100m);
                //if (moveData.HighestPrice >= dbHighestPrice)//0401 取消判断
                {
                    //回落价格
                    var declinePrice = moveData.HighestPrice * (1 - moveData.MoveData.DeclineRatio / 100m);
                    if (tokenPrice.ToTruncate(20) <= Convert.ToDecimal(declinePrice).ToTruncate(20) &&
                        Convert.ToDecimal(moveData.HighestPrice).ToTruncate(20) >= dbHighestPrice.ToTruncate(20))
                    {
                        //更新 本条状态
                        /*await _userQuickTradeTaskRepository.CopyNew().AsUpdateable()
                            .SetColumns(it => it.CompleteTime == DateTime.Now)
                            .SetColumns(it => it.Status == "completed")
                            .Where(it => it.Id == moveData.Id)
                            .ExecuteCommandAsync();
                        //更新 剩余 任务 的 buyPrice

                        await _userQuickTradeTaskRepository.CopyNew().AsUpdateable()
                            .SetColumns(it => it.BuyPrice == tokenPrice)
                            .SetColumns(it => it.HighestPrice == tokenPrice)
                            .Where(it => it.LogId == item.LogId && /*it.Id > moveData.Id &&#1# it.Status == "active")
                            .ExecuteCommandAsync();
                            */
                        moveData.CompleteTime = DateTime.Now;
                        moveData.Status = "completed";
                        list.ForEach(it =>
                        {
                            if (it.Status == "active")
                            {
                                it.BuyPrice = tokenPrice;
                                it.HighestPrice = tokenPrice;
                            }
                        });

                        //卖出百分比
                        taskId = moveData.Id;
                        sellPercentage = moveData.MoveData.SellRatio;
                    }
                }

                if (tokenPrice > moveData.HighestPrice)
                {
                    //更新最高价格
                    /*await _userQuickTradeTaskRepository.CopyNew().AsUpdateable()
                        .SetColumns(it => it.HighestPrice == tokenPrice)
                        .Where(it => it.Id == moveData.Id)
                        .ExecuteCommandAsync();*/
                    moveData.HighestPrice = tokenPrice;
                }
            }

            // 自动止盈
            if (riseData != null && riseData.IsRuning)
            {
                /*if (DateTime.Now >= riseData.ExpiredTime)
                {
                    riseData.CompleteTime = DateTime.Now;
                    riseData.Status = "canceled";

                    taskId = riseData.Id;
                    //卖出百分比
                    sellPercentage = riseData.ExpiredAuto ? 100 : 0;
                }*/

                runId = riseData.Id;
                var dbCurPrice = riseData.BuyPrice * (1 + riseData.AutoData.PriceRatio / 100m);
                if (tokenPrice.ToTruncate(20) >= dbCurPrice.ToTruncate(20))
                {
                    //更新 库状态
                    //更新 本条状态
                    /*await _userQuickTradeTaskRepository.CopyNew().AsUpdateable()
                        .SetColumns(it => it.CompleteTime == DateTime.Now)
                        .SetColumns(it => it.Status == "completed")
                        .Where(it => it.Id == riseData.Id)
                        .ExecuteCommandAsync();
                    //更新 剩余 任务 的  buyPrice
                    //var nextData = list.First(it => it.StopLimitMode == 1 && it.TradeMode == 1 && it.Id > riseData.Id);
                    //if (nextData != null)
                    await _userQuickTradeTaskRepository.CopyNew().AsUpdateable()
                        .SetColumns(it => it.BuyPrice == tokenPrice)
                        .SetColumns(it => it.HighestPrice == tokenPrice)
                        .Where(it => it.LogId == item.LogId && /*it.Id> riseData.Id &&#1# it.Status == "active")
                        .ExecuteCommandAsync();*/
                    riseData.CompleteTime = DateTime.Now;
                    riseData.Status = "completed";

                    list.ForEach(it =>
                    {
                        if (it.Status == "active")
                        {
                            it.BuyPrice = tokenPrice;
                            it.HighestPrice = tokenPrice;
                        }
                    });

                    taskId = riseData.Id;
                    //卖出百分比
                    sellPercentage = riseData.AutoData.SellRatio;
                }
            }

            //自动止损
            if (slumpData != null && slumpData.IsRuning)
            {
                /*if (DateTime.Now >= slumpData.ExpiredTime)
                {
                    slumpData.CompleteTime = DateTime.Now;
                    slumpData.Status = "canceled";

                    taskId = slumpData.Id;
                    //卖出百分比
                    sellPercentage = slumpData.ExpiredAuto ? 100 : 0;
                }*/

                runId = slumpData.Id;
                var dbCurPrice = slumpData.BuyPrice * (1 - slumpData.AutoData.PriceRatio / 100m);
                if (tokenPrice.ToTruncate(20) <= dbCurPrice.ToTruncate(20))
                {
                    //更新 库状态
                    //更新 本条状态
                    /*await _userQuickTradeTaskRepository.CopyNew().AsUpdateable()
                        .SetColumns(it => it.Status == "completed")
                        .SetColumns(it => it.CompleteTime == DateTime.Now)
                        .Where(it => it.Id == slumpData.Id)
                        .ExecuteCommandAsync();
                    //更新剩余 任务 的 buyPrice

                    await _userQuickTradeTaskRepository.CopyNew().AsUpdateable()
                        .SetColumns(it => it.BuyPrice == tokenPrice)
                        .SetColumns(it => it.HighestPrice == tokenPrice)
                        .Where(it => it.LogId == item.LogId && /*it.Id > slumpData.Id &&#1# it.Status == "active")
                        .ExecuteCommandAsync();*/

                    slumpData.CompleteTime = DateTime.Now;
                    slumpData.Status = "completed";
                    list.ForEach(it =>
                    {
                        if (it.Status == "active")
                        {
                            it.BuyPrice = tokenPrice;
                            it.HighestPrice = tokenPrice;
                        }
                    });

                    taskId = slumpData.Id;
                    //卖出百分比
                    sellPercentage = slumpData.AutoData.SellRatio;
                }
            }

            Next:
            if (sellPercentage > 0)
            {
                Logger.LogDebug($"快速交易 子任务:{taskId} 任务:{logId} {item.Mint} 百分比:{sellPercentage} {item.DexType}");
                //发送卖出 消息
                await _eventPublisher.PublishAsync(EventConsts.QuickTradeSubTaskSellEvent,
                    new QuickTradeSubTaskSellItem(taskId, logId, sellPercentage, item.DexType, item.PoolId));
                //更新Mysql信息需要把list信息更新到对应的mysql里
                await _userQuickTradeTaskRepository.AsUpdateable(list)
                    .UpdateColumns(s => new { s.BuyPrice, s.HighestPrice, s.Status, s.CompleteTime })
                    .ExecuteCommandAsync();
                //过期 买出 及删除任务
                if (sellPercentage == 100)
                {
                    await _redisClient.HDelAsync(RedisKey.QuickTradeSubTaskList, item.LogId);
                    //有百分百 卖出时 取消任务
                    await dictionaryLock[$"{item.UId}_{item.LogId}"].CancelAsync();
                    continue;
                }
            }

            if (isExpired)
            {
                list.ForEach(it =>
                {
                    if (DateTime.Now >= it.ExpiredTime)
                    {
                        it.CompleteTime = DateTime.Now;
                        it.Status = "canceled";
                    }
                });
            }

            var ids = list.Where(it => it.IsRuning).Select(it => it.Id.ToString()).ToList();
            Logger.LogDebug($"快速交易 子任务:{logId}=>{string.Join(",", ids)} 运行中...");
            await _redisClient.HSetAsync(RedisKey.QuickTradeSubTaskList, item.LogId, list);

            await Task.Delay(800);
        }
    }

    /*
     * 处理队列项时出错: SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
       at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
       at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
       at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()

     */
    private async void OnRedisSubscribeStreamMessage(Dictionary<string, string> obj)
    {
        var uid = int.Parse(obj["Uid"]);
        var logID = (obj["LogId"]);
        //var isPump = bool.Parse(obj["IsPump"]);
        var dexType = obj["DexType"];
        var mint = obj["Mint"];
        var poolId = obj["PoolId"];
        var status = 1;
        if (obj.TryGetValue("Status", out string statusStr) && int.TryParse(statusStr, out int parsedstatus))
            status = parsedstatus; //1 运行 2停止 3删除
        var bonding = Solnet.Pumpfun.PDALookup.FindBondingPDA(new PublicKey(mint));


        WorkQueue<CTTask> workQueue;
        if (dictionary.TryGetValue($"{uid}_{logID}", out workQueue))
        {
            Logger.LogDebug($"快速买入 子任务服务 {logID}=>{uid} 装入{mint} 运行任务数:{workQueue?.Count} 停止运行");
            await dictionaryLock[$"{uid}_{logID}"].CancelAsync();
            await workQueue.StopAsync();
            dictionary.Remove($"{uid}_{logID}");
        }

        if (status == 3)
        {
            await _redisClient.HDelAsync(RedisKey.QuickTradeSubTaskList, $"{logID}");
            Logger.LogDebug($"快速买入 子任务服务 {logID}=>{uid}=>{mint} 删除任务 ");
            //return;
        }

        //3s 后执行
        //await Task.Delay(3000);

        //获取代币当前价格
        var mintPrice = await _solanaApi.GetMintPrice(mint, dexType, poolId);
        //获取 价格，更新记录
        await _userQuickTradeTaskRepository.CopyNew().AsUpdateable()
            .SetColumns(it => it.BuyPrice == mintPrice)
            .Where(it => it.LogId == logID && it.Status == "active" && it.IsRuning)
            .ExecuteCommandAsync();
        List<UserQuickTradeTask> list = await _userQuickTradeTaskRepository.CopyNew().AsQueryable().Where(it =>
            it.LogId == logID && it.TokenAddress == mint && it.Status == "active" && it.IsRuning).ToListAsync();
        if (list == null || list.Count <= 0)
        {
            Logger.LogDebug($"快速买入 子任务服务 {logID}=>{uid}=>{mint} 没有可执行的子任务");
            return;
        }

        if (await _redisClient.HExistsAsync(RedisKey.QuickTradeSubTaskList, $"{logID}"))
        {
            var rList = await _redisClient.HGetAsync<List<UserQuickTradeTask>>(RedisKey.QuickTradeSubTaskList,
                $"{logID}");
            rList.ForEach(it =>
            {
                it.IsRuning = list.FirstOrDefault(iit => iit.Id == it.Id)?.IsRuning ?? false;
                it.ExpiredTime = list.FirstOrDefault(iit => iit.Id == it.Id)?.ExpiredTime ?? DateTime.Now;
            });
            await _redisClient.HSetAsync<List<UserQuickTradeTask>>(RedisKey.QuickTradeSubTaskList, $"{logID}", rList);
        }
        else
        {
            await _redisClient.HSetAsync<List<UserQuickTradeTask>>(RedisKey.QuickTradeSubTaskList, $"{logID}", list);
        }

        workQueue = QueueManager.CreateQueue<CTTask>(PriceMonitor, 1);
        dictionary[$"{uid}_{logID}"] = workQueue;
        var task = new CTTask(logID, uid, mint, dexType, bonding, poolId);
        dictionaryLock[$"{uid}_{logID}"] = new CancellationTokenSource();
        workQueue.Enqueue(task);
        Logger.LogDebug($"快速买入 子任务服务 {logID}=>{uid} 装入{mint} 运行任务数:{workQueue.Count}");
    }

    private async Task InitQuickTrade()
    {
        var list = await _userQuickTradeTaskRepository.AsQueryable().Where(it => it.Status == "active" && it.IsRuning)
            .ToListAsync();
        foreach (var tradeTask in list)
        {
            //发送 价格订阅
            await _redisClient.XAddAsync(StreamKey.SolanaPumpSwapPriceStream, new Dictionary<string, string>()
            {
                ["mint"] = tradeTask.TokenAddress,
            });


            var dict = new Dictionary<string, string>()
            {
                ["Uid"] = tradeTask.UId.ToString(),
                ["LogId"] = tradeTask.LogId.ToString(),
                ["Mint"] = tradeTask.TokenAddress
            };
            //var (isPump, _) = await _solanaApi.GetBoundingCurve(tradeTask.TokenAddress);
            dict["DexType"] = (await _solanaApi.GetMintDexType(tradeTask.TokenAddress));
            dict["PoolId"] = (await _solanaApi.GetMintPoolId(tradeTask.TokenAddress));
            OnRedisSubscribeStreamMessage(dict);
            Logger.LogDebug($"装入快速交易子任务:{tradeTask.LogId} {tradeTask.TokenAddress}");
            await Task.Delay(200);
        }
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        Logger.LogDebug($"快速买入止盈止损 子任务服务 启动");

        streamCopyTradeTask =
            _redisClient.SubscribeStream(StreamKey.QuickTraderTaskStream, onMessage: OnRedisSubscribeStreamMessage);

        await InitQuickTrade();

        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        //强制退出
        await cancellationTokenSource.CancelAsync();
        streamCopyTradeTask.Dispose();
        Logger.LogDebug($"快速买入止盈止损 子任务服务 停止");
        await base.StopAsync(cancellationToken);
    }
}