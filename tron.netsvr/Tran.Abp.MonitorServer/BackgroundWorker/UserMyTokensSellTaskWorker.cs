using FreeRedis.Internal;
using Tran.Abp.MonitorServer.EventHandlers;
using Tron.Abp.Multiplex.Solana;
using Tron.Abp.Nats;

namespace Tran.Abp.MonitorServer.BackgroundWorker;

public class TokenSellTaskItem
{
    public TokenSellTaskItem(int uid, string mint, string secretKey, decimal percentage, int slippage, decimal gas,
        decimal mev, string chain, string name, string symbol, string icon, int wId, string poolId, bool isBuy,
        decimal sol)
    {
        Slippage = slippage;
        Gas = gas;
        MEV = mev;
        Chain = chain;
        Name = name;
        Symbol = symbol;
        Icon = icon;
        WId = wId;
        PoolId = poolId;
        IsBuy = isBuy;
        Sol = sol;
        Uid = uid;
        Mint = mint;
        SecretKey = secretKey;
        Percentage = percentage;
    }

    public string Chain { get; set; }
    public string Name { get; set; }
    public string Symbol { get; set; }
    public string Icon { get; set; }
    public decimal Sol { get; set; }
    public int Slippage { get; set; }
    public decimal Gas { get; set; }
    public decimal MEV { get; set; }
    public int Uid { get; set; }
    public string Mint { get; set; }
    public int WId { get; set; }
    public string SecretKey { get; set; }
    public decimal Percentage { get; set; }
    public string PoolId { get; set; }
    public bool IsBuy { get; set; }
}

public class UserMyTokensSellTaskWorker : BackgroundWorkerBase
{
    public ILogger<UserMyTokensSellTaskWorker> Logger { get; set; }
    private readonly SqlSugarRepository<UserMyTokens> _userMyTokensRepository;
    private readonly SqlSugarRepository<UserMyWallet> _userMyWalletRepository;
    private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;
    private IRedisClient _redisClient;
    private ISolanaApi _solanaApi;
    private SubscribeStreamObject stream;
    WorkQueue<TokenSellTaskItem> queue;
    private IEventPublisher _eventPublisher;
    private IAllTradesSolAmountTokenAmount _allTradesSolAmountTokenAmount;
    private INats _nats;
    public UserMyTokensSellTaskWorker(IRedisClient redisClient, ISolanaApi solanaApi,
        SqlSugarRepository<UserMyTokens> userMyTokensRepository,
        SqlSugarRepository<UserMyWallet> userMyWalletRepository, IEventPublisher eventPublisher,
        SqlSugarRepository<UserAllTraderLog> userAllTraderLogRepository,
        IAllTradesSolAmountTokenAmount allTradesSolAmountTokenAmount, INats nats)
    {
        _redisClient = redisClient;
        _solanaApi = solanaApi;
        _userMyTokensRepository = userMyTokensRepository;
        _userMyWalletRepository = userMyWalletRepository;
        _eventPublisher = eventPublisher;
        _userAllTraderLogRepository = userAllTraderLogRepository;
        _allTradesSolAmountTokenAmount = allTradesSolAmountTokenAmount;
        _nats = nats;
        Logger = NullLogger<UserMyTokensSellTaskWorker>.Instance;
    }

    private async Task WorkQueueAction(TokenSellTaskItem item)
    {
        var account = Solnet.Wallet.Account.FromSecretKey(item.SecretKey);
        var dexType = (await _solanaApi.GetMintDexType(item.Mint));

        var (successful, sign, errormsg) =
            item.IsBuy
                ? await _solanaApi.Buy(dexType, account, item.Mint, item.Sol, item.Slippage, item.Gas, item.MEV,
                    item.PoolId)
                : await _solanaApi.Sell(dexType, account, item.Mint, item.Percentage, item.Gas, item.MEV, item.Slippage,
                    item.PoolId);
        if (string.IsNullOrWhiteSpace(sign))
        {
            Logger.LogDebug(
                $"持仓批量操作:{item.IsBuy} 代币:{item.Mint} sol:{item.Sol} 百分比:{item.Percentage} 操作失败=>{errormsg}");
            return;
        }

        //写日志
        //写log
        var logModel = new UserAllTraderLog()
        {
            TraderType = TraderType.Trader_00,
            UId = item.Uid,
            Chain = item.Chain,
            TokenName = item.Name,
            TokenSymbol = item.Symbol,
            TokenAddress = item.Mint,
            TokenIcon = item.Icon,
            IsBuy = false,
            SolAmount = 0,
            TokenAmount = 0,
            Gas = item.Gas,
            Mev = item.MEV,
            Status="持仓-成功",
            MyWalletAddress = account.PublicKey,
            CreateTime = DateTime.Now,
            Signature = sign
        };
        logModel.Id = await _userAllTraderLogRepository.CopyNew().AsInsertable(logModel).ExecuteReturnIdentityAsync();

        var atuae = new AllTradeUpdateAmountEvent(logModel.Id, sign, item.Mint, dexType, true, "持仓批量操作",
            logModel.FollowWalletAddress, 0);
        var (isSuccessful,solAmount,tokenAmount) = await _allTradesSolAmountTokenAmount.UpdateAllTrades_SolAmount_TokenAmount(atuae);
        if (!isSuccessful)
        {
            Logger.LogDebug($"持仓批量操作 {item.Mint} 获取交易记录 无法解析/交易失败 ");
            return;
        }

        //更新 子任务 百分百卖出 
        if (!item.IsBuy && item.Percentage == 100)
        {
            var dict = new Dictionary<string, string>()
            {
                ["uid"] = item.Uid.ToString(),
                ["wid"] = item.WId.ToString(),
                ["mint"] = item.Mint,
                ["publickey"] = account.PublicKey,
            };
            //发送消息
            await _redisClient.XAddAsync(StreamKey.RemoveTokenInventoryStream, dict);
        }
    }

    private async void OnRedisSubscribeStreamMessage(Dictionary<string, string> dict)
    {
        // 初始化默认值
        int uid = 0;
        string mints = "";
        decimal sol = 0m;
        int wid = 0;
        decimal gas = 0m;
        decimal mev = 0m;
        decimal slippage = 0m;
        decimal percentage = 0m;
        bool isBuy = false;
        if (dict.TryGetValue("uid", out string uidStr) && int.TryParse(uidStr, out int parsedUid))
            uid = parsedUid;
        if (dict.TryGetValue("mints", out string mintsStr))
            mints = mintsStr;
        if (dict.TryGetValue("sol", out string solStr) && decimal.TryParse(solStr, out decimal parsedSol))
            sol = parsedSol;
        if (dict.TryGetValue("wid", out string widStr) && int.TryParse(widStr, out int parsedWid))
            wid = parsedWid;
        if (dict.TryGetValue("gas", out string gasStr) && decimal.TryParse(gasStr, out decimal parsedGas))
            gas = parsedGas;
        if (dict.TryGetValue("mev", out string mevStr) && decimal.TryParse(mevStr, out decimal parsedMev))
            mev = parsedMev;
        if (dict.TryGetValue("slippage", out string slippageStr) && decimal.TryParse(slippageStr, out decimal parsedSlippage))
            slippage = parsedSlippage;
        if (dict.TryGetValue("percentage", out string percentageStr) && decimal.TryParse(percentageStr, out decimal parsedPercentage))
            percentage = parsedPercentage;
        if (dict.TryGetValue("isbuy", out string isBuyStr) && bool.TryParse(isBuyStr, out bool parsedIsBuy))
            isBuy = parsedIsBuy;
        
        Logger.LogDebug($"收到 持仓批量操作 {uid}=>{wid}=>{mints}");

        var mintsArry = mints.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).ToList();
        var myWallet = await _userMyWalletRepository.AsQueryable().Where(it => it.UId == uid && it.Id == wid)
            .FirstAsync();
        queue = QueueManager.CreateQueue<TokenSellTaskItem>(WorkQueueAction, 50);
        foreach (var mint in mintsArry)
        {
            var token = await _solanaApi.GetMint(mint);
            var poolId = await _solanaApi.GetMintPoolId(mint);
            var taskItem = new TokenSellTaskItem(uid, mint,
                myWallet.SecretKey, percentage, (int)slippage,
                gas, mev, token.Chain, token.Name, token.Symbol, token.Icon,
                wid, poolId, isBuy, sol);
            queue.Enqueue(taskItem);
        }
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        stream = _redisClient.SubscribeStream(StreamKey.UserMyTokensSellStream,
            onMessage: OnRedisSubscribeStreamMessage);
        Logger.LogDebug($"持有代币 持仓批量操作 服务 启动");
        //启动订阅消息
        Task.Run(async () => { await _nats.SubscribeAsync(NatsKeys.SysSubscribeTronMsgType); });
        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        if (queue != null) await queue.StopAsync();
        stream.Dispose();
        Logger.LogDebug($"持有代币 持仓批量操作 服务 停止");
        await base.StopAsync(cancellationToken);
    }
}