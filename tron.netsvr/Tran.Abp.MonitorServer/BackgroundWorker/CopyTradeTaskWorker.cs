using System.Collections.Concurrent;
using System.Text.Json;
using FreeRedis.Internal;
using Solnet.Wallet;
using Tran.Abp.MonitorServer.BackgroundWorker.Dto;
using Tran.Abp.MonitorServer.EventHandlers;
using Tran.Abp.MonitorServer.EventHandlers.Dto;
using Tron.Abp.Core.Extensions;
using Tron.Abp.Multiplex.Solana;

namespace Tran.Abp.MonitorServer.BackgroundWorker;

/// <summary>
/// 跟单交易 子任务服务
/// </summary>
public class CopyTradeTaskWorker : BackgroundWorkerBase
{
    public ILogger<CopyTradeTaskWorker> Logger { get; set; }
    private Dictionary<string, WorkQueue<CTTask>> dictionary;
    private IRedisClient _redisClient;
    private SubscribeStreamObject streamCopyTradeTask;
    private readonly SqlSugarRepository<UserCopyTradeTask> _userCopyTradeTaskRepository;
    private IEventPublisher _eventPublisher;
    private CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();
    private ISolanaApi _solanaApi;
    private ConcurrentDictionary<string, CancellationTokenSource> dictionaryLock;

    public CopyTradeTaskWorker(IRedisClient redisClient,
        SqlSugarRepository<UserCopyTradeTask> userCopyTradeTaskRepository, IEventPublisher eventPublisher,
        ISolanaApi solanaApi)
    {
        _redisClient = redisClient;
        _userCopyTradeTaskRepository = userCopyTradeTaskRepository;
        _eventPublisher = eventPublisher;
        _solanaApi = solanaApi;
        Logger = NullLogger<CopyTradeTaskWorker>.Instance;
        dictionary = new Dictionary<string, WorkQueue<CTTask>>();
        dictionaryLock =
            new ConcurrentDictionary<string, CancellationTokenSource>();
    }

    private async Task<bool> IsInWorkQueue(string uidKey, int logID, string mint)
    {
        // 获取或初始化 mintDict
        var mintDict = await _redisClient.HExistsAsync(RedisKey.CopyTradeList, uidKey)
            ? await _redisClient.HGetAsync<Dictionary<int, List<string>>>(RedisKey.CopyTradeList, uidKey) ??
              new Dictionary<int, List<string>>()
            : new Dictionary<int, List<string>>();

        // 检查并添加 mint
        var mintList = mintDict.TryGetValue(logID, out var list) ? list : mintDict[logID] = new List<string>();
        if (mintList.Contains(mint))
        {
            return true; // 已存在，返回 true
        }

        mintList.Add(mint);
        await _redisClient.HSetAsync(RedisKey.CopyTradeList, uidKey, mintDict);
        return false; // 不存在，已添加，返回 false
    }

    private async Task PriceMonitor(CTTask item)
    {
        var curDexType = item.DexType; //当前 dextype
        var curPoolId = item.PoolId; //当前poolid
        while (!(dictionaryLock[$"{item.UId}_{item.LogId}"].IsCancellationRequested))
        {
            //卖出百分比
            var sellPercentage = 0;
            var takid = 0;
            //var ctId = item.TaskId; //此ID为 跟单 钱包ID ==logId
            var logId = item.LogId;
            //取任务 信息
            // StopLimitMode  自动=1 移动=2
            // StopLimitMode=1 AutoModel 1止盈 2止损
            /*var list = await _userCopyTradeTaskRepository.CopyNew().AsQueryable()
                .Where(it => it.CtId == ctId && it.TokenAddress == item.Mint && it.Status == "active" && it.IsRuning)
                .ToListAsync();
            */
            var listItems = await _redisClient.HExistsAsync(RedisKey.CopyTradeSubTaskList, item.LogId)
                ? await _redisClient.HGetAsync<List<UserCopyTradeTask>>(RedisKey.CopyTradeSubTaskList, item.LogId)
                : new List<UserCopyTradeTask>();
            var list = listItems.Where(it => it.Status == "active").ToList();
            var listItem = list.FirstOrDefault(it => it.ExpiredAuto == true);
            if (listItem != null && DateTime.Now >= listItem.ExpiredTime)
            {
                sellPercentage = 100;
                //所有任务完成
                await _userCopyTradeTaskRepository.AsUpdateable()
                    .SetColumns(it => it.CompleteTime == DateTime.Now)
                    .SetColumns(it => it.Status == "canceled")
                    .Where(it => it.LogId == logId && it.TokenAddress == item.Mint && it.Status == "active")
                    .ExecuteCommandAsync();
                //跳至卖出
                goto Next;
            }

            //移动
            var moveData = list.FirstOrDefault(it => it.StopLimitMode == 2);
            //止盈
            var riseData = list.FirstOrDefault(it => it.StopLimitMode == 1 && it.AutoMode == 1);
            //止损
            var slumpData = list.FirstOrDefault(it => it.StopLimitMode == 1 && it.AutoMode == 2);
            //没有任务了
            if (moveData == null && slumpData == null && riseData == null) break;

            //取价格
            var tokenPrice = 0m;
            //0418 内盘 到 外盘  更新原有dex类型 及池子地址
            if (curDexType == DexType.Pumpfun||curDexType==DexType.Launchlab ||curDexType==DexType.MeteoraDBC)
            {
                var poolData = await _solanaApi.GetMint(item.Mint, "");
                if (poolData.Type != curDexType)
                {
                    curDexType = poolData.Type;
                    curPoolId = poolData.Id;
                    Logger.LogDebug($"跟单交易 子任务 {item.LogId} Mint:{item.Mint} 内盘到外盘 dex poolid 更新");
                }
            }

            tokenPrice = await _solanaApi.GetMintPrice(item.Mint, curDexType, curPoolId);


            Logger.LogDebug($"跟单交易 子任务 {item.LogId} 获取价格 Mint:{item.Mint} PoolId:{curPoolId} => {tokenPrice}");
            if (tokenPrice == 0)
            {
                goto Next;
            }

            //取小数后9位小数
            //tokenPrice = tokenPrice.ToTruncate(9);
            //移动 止损
            if (moveData != null && moveData.IsRuning)
            {
                //满足条件的最高价格 
                var dbHighestPrice = moveData.BuyPrice * (1 + moveData.MoveData.PriceRatio / 100m);
                //if (moveData.HighestPrice >= dbHighestPrice)
                {
                    //回落价格
                    var declinePrice = moveData.HighestPrice * (1 - moveData.MoveData.DeclineRatio / 100m);
                    if (tokenPrice.ToTruncate(20) <= Convert.ToDecimal(declinePrice).ToTruncate(20) &&
                        Convert.ToDecimal(declinePrice).ToTruncate(20) >= dbHighestPrice.ToTruncate(20))
                    {
                        //更新 本条状态
                        /*await _userCopyTradeTaskRepository.CopyNew().AsUpdateable()
                            .SetColumns(it => it.CompleteTime == DateTime.Now)
                            .SetColumns(it => it.Status == "completed")
                            .Where(it => it.Id == moveData.Id)
                            .ExecuteCommandAsync();
                        */

                        //更新 剩余 任务 的 buyPrice 
                        /*await _userCopyTradeTaskRepository.CopyNew().AsUpdateable()
                            .SetColumns(it => it.BuyPrice == tokenPrice)
                            .SetColumns(it => it.HighestPrice == tokenPrice)
                            .Where(it => it.CtId == ctId && it.TokenAddress == item.Mint && it.Status == "active")
                            .ExecuteCommandAsync();*/
                        moveData.CompleteTime = DateTime.Now;
                        moveData.Status = "completed";
                        list.ForEach(it =>
                        {
                            if (it.Status == "active")
                            {
                                it.BuyPrice = tokenPrice;
                                it.HighestPrice = tokenPrice;
                            }
                        });
                        //卖出百分比
                        takid = moveData.Id;
                        sellPercentage = moveData.MoveData.SellRatio;
                        goto Next;
                    }
                }

                if (tokenPrice > moveData.HighestPrice)
                {
                    /*
                    //更新最高价格
                    await _userCopyTradeTaskRepository.CopyNew().AsUpdateable()
                        .SetColumns(it => it.HighestPrice == tokenPrice)
                        .Where(it => it.Id == moveData.Id)
                        .ExecuteCommandAsync();
                    */
                    moveData.HighestPrice = tokenPrice;
                }
            }

            //止盈
            if (riseData != null && riseData.IsRuning)
            {
                var dbCurPrice = riseData.BuyPrice * (1 + riseData.AutoData.PriceRatio / 100m);
                if (tokenPrice.ToTruncate(20) >= dbCurPrice.ToTruncate(20)) //0401
                {
                    /*//更新 库状态
                    //更新 本条状态
                    await _userCopyTradeTaskRepository.CopyNew().AsUpdateable()
                        .SetColumns(it => it.CompleteTime == DateTime.Now)
                        .SetColumns(it => it.Status == "completed")
                        .Where(it => it.Id == riseData.Id)
                        .ExecuteCommandAsync();
                    //更新下一条 buyPrice
                    //var nextData = list.First(it => it.StopLimitMode == 1 && it.TradeMode == 1 && it.Id > riseData.Id);
                    //if (nextData != null)
                    await _userCopyTradeTaskRepository.CopyNew().AsUpdateable()
                        .SetColumns(it => it.BuyPrice == tokenPrice)
                        .SetColumns(it => it.HighestPrice == tokenPrice)
                        .Where(it => it.CtId == ctId && it.TokenAddress == item.Mint && it.Status == "active")
                        .ExecuteCommandAsync();
                    */
                    riseData.CompleteTime = DateTime.Now;
                    riseData.Status = "completed";

                    list.ForEach(it =>
                    {
                        if (it.Status == "active")
                        {
                            it.BuyPrice = tokenPrice;
                            it.HighestPrice = tokenPrice;
                        }
                    });

                    takid = riseData.Id;
                    //卖出百分比
                    sellPercentage = riseData.AutoData.SellRatio;
                }
            }

            //止损
            if (slumpData != null && slumpData.IsRuning)
            {
                var dbCurPrice = slumpData.BuyPrice * (1 - slumpData.AutoData.PriceRatio / 100m);
                if (tokenPrice.ToTruncate(20) <= dbCurPrice.ToTruncate(20)) //0401
                {
                    /*
                    //更新 库状态
                    //更新 本条状态
                    await _userCopyTradeTaskRepository.CopyNew().AsUpdateable()
                        .SetColumns(it => it.Status == "completed")
                        .SetColumns(it => it.CompleteTime == DateTime.Now)
                        .Where(it => it.Id == slumpData.Id)
                        .ExecuteCommandAsync();
                    //更新下一条 buyPrice
                    //var nextData = list.First(it => it.StopLimitMode == 1 && it.TradeMode == 2 && it.Id > slumpData.Id);
                    //if (nextData != null)
                    await _userCopyTradeTaskRepository.CopyNew().AsUpdateable()
                        .SetColumns(it => it.BuyPrice == tokenPrice)
                        .SetColumns(it => it.HighestPrice == tokenPrice)
                        .Where(it => it.CtId == ctId && it.TokenAddress == item.Mint && it.Status == "active")
                        .ExecuteCommandAsync();

                    */

                    slumpData.CompleteTime = DateTime.Now;
                    slumpData.Status = "completed";
                    list.ForEach(it =>
                    {
                        if (it.Status == "active")
                        {
                            it.BuyPrice = tokenPrice;
                            it.HighestPrice = tokenPrice;
                        }
                    });


                    takid = slumpData.Id;
                    //卖出百分比
                    sellPercentage = slumpData.AutoData.SellRatio;
                }
            }

            Next:
            if (sellPercentage > 0)
            {
                Logger.LogDebug($"跟单子任务:{takid} 跟单任务:{logId} {item.Mint} 卖出百分比:{sellPercentage} {curDexType}");
                //发送卖出 消息 todo:
                await _eventPublisher.PublishAsync(EventConsts.CopyTradeSubTaskSellEvent,
                    new CopyTradeSubTaskSellItem(takid, logId, sellPercentage, curDexType, item.Mint, curPoolId));
                //更新Mysql信息需要把list信息更新到对应的mysql里
                await _userCopyTradeTaskRepository.AsUpdateable(list)
                    .UpdateColumns(s => new { s.BuyPrice, s.HighestPrice, s.Status, s.CompleteTime })
                    .ExecuteCommandAsync();


                if (sellPercentage == 100)
                {
                    await _redisClient.HDelAsync(RedisKey.CopyTradeSubTaskList, item.LogId);
                    //有百分百 卖出时 取消任务
                    await dictionaryLock[$"{item.UId}_{item.LogId}"].CancelAsync();
                }
            }

            var ids = list.Where(it => it.Status == "active" && it.IsRuning).Select(it => it.Id.ToString()).ToList();
            Logger.LogDebug(
                $"跟单交易 子任务:{item.LogId}=>{string.Join(",", ids)} Mint:{item.Mint} PoolId:{curPoolId} 运行中...");
            await _redisClient.HSetAsync(RedisKey.CopyTradeSubTaskList, item.LogId, list);
            await Task.Delay(800);
        }
    }

    private async void OnRedisSubscribeStreamMessage(Dictionary<string, string> obj)
    {
        // Uid  LogId Mint
        var uid = int.Parse(obj["Uid"]);
        var logID = obj["LogId"];
        var dexType = obj["DexType"];
        var mint = obj["Mint"];
        var poolId = obj["PoolId"];
        var status = 1;
        if (obj.TryGetValue("Status", out string statusStr) && int.TryParse(statusStr, out int parsedstatus))
            status = parsedstatus; //1 运行 2停止 3删除
        var bonding = Solnet.Pumpfun.PDALookup.FindBondingPDA(new PublicKey(mint));
        WorkQueue<CTTask> workQueue;
        if (dictionary.TryGetValue($"{uid}_{logID}", out workQueue))
        {
            Logger.LogDebug($"跟单交易 子任务服务 {logID}=>{uid}=>{mint} 运行任务数:{workQueue.Count} 停止运行");
            await dictionaryLock[$"{uid}_{logID}"].CancelAsync();
            await workQueue.StopAsync();
            dictionary.Remove($"{uid}_{logID}");
        }

        if (status == 3)
        {
            await _redisClient.HDelAsync(RedisKey.CopyTradeSubTaskList, $"{logID}");
            Logger.LogDebug($"跟单交易 子任务服务 {logID}=>{uid}=>{mint} 删除任务 ");
        }


        //await Task.Delay(3000);
        Logger.LogDebug($"用户{uid}=>子任务{logID} =>{mint} 开始运行");
        //获取代币当前价格
        var mintPrice = await _solanaApi.GetMintPrice(mint, dexType, poolId);


        await _userCopyTradeTaskRepository.CopyNew().AsUpdateable()
            .SetColumns(it => it.BuyPrice == mintPrice)
            .Where(it => it.LogId == logID && it.TokenAddress == mint && it.Status == "active" && it.IsRuning)
            .ExecuteCommandAsync();

        List<UserCopyTradeTask> list = await _userCopyTradeTaskRepository.CopyNew().AsQueryable().Where(it =>
            it.LogId == logID && it.TokenAddress == mint && it.Status == "active" && it.IsRuning).ToListAsync();
        if (list == null || list.Count <= 0)
        {
            Logger.LogDebug($"跟单交易 子任务服务 {logID}=>{uid}=>{mint} 没有可执行的子任务");
            return;
        }

        if (await _redisClient.HExistsAsync(RedisKey.CopyTradeSubTaskList, $"{logID}"))
        {
            var rList = await _redisClient.HGetAsync<List<UserCopyTradeTask>>(RedisKey.CopyTradeSubTaskList,
                $"{logID}");
            rList.ForEach(it =>
            {
                it.IsRuning = list.FirstOrDefault(iit => iit.Id == it.Id)?.IsRuning ?? false;
                it.ExpiredTime = list.FirstOrDefault(iit => iit.Id == it.Id)?.ExpiredTime ?? DateTime.Now;
            });
            await _redisClient.HSetAsync<List<UserCopyTradeTask>>(RedisKey.CopyTradeSubTaskList, $"{logID}", rList);
        }
        else
        {
            await _redisClient.HSetAsync(RedisKey.CopyTradeSubTaskList, $"{logID}", list);
        }

        workQueue = QueueManager.CreateQueue<CTTask>(PriceMonitor, 1);
        dictionary[$"{uid}_{logID}"] = workQueue;
        var task = new CTTask(logID, uid, mint, dexType, bonding, poolId);
        dictionaryLock[$"{uid}_{logID}"] = new CancellationTokenSource();
        workQueue.Enqueue(task);
        Logger.LogDebug($"跟单交易 子任务服务 新增 {logID}=>{uid} 装入{mint} 运行任务数:{workQueue.Count}");
    }

    private async Task InitTrade()
    {
        var list = await _userCopyTradeTaskRepository.AsQueryable().Where(it => it.Status == "active" && it.IsRuning)
            .ToListAsync();
        foreach (var tradeTask in list)
        {
            //发送 价格订阅
            await _redisClient.XAddAsync(StreamKey.SolanaPumpSwapPriceStream, new Dictionary<string, string>()
            {
                ["mint"] = tradeTask.TokenAddress,
            });
            var dict = new Dictionary<string, string>()
            {
                ["Uid"] = tradeTask.UId.ToString(),
                ["LogId"] = tradeTask.LogId.ToString(),
                ["Mint"] = tradeTask.TokenAddress
            };
            //var (isPump, _) = await _solanaApi.GetBoundingCurve(tradeTask.TokenAddress);
            dict["DexType"] = (await _solanaApi.GetMintDexType(tradeTask.TokenAddress));
            dict["PoolId"] = (await _solanaApi.GetMintPoolId(tradeTask.TokenAddress));
            OnRedisSubscribeStreamMessage(dict);
            Logger.LogDebug($"装入 跟单交易 子任务:{tradeTask.LogId} {tradeTask.TokenAddress}");
            await Task.Delay(200);
        }
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        Logger.LogDebug($"跟单交易 子任务服务 启动");

        _redisClient.Subscribe(StreamKey.RedisSubscriberCopyTradeStream, (async (s, o) =>
        {
            if (o != null)
            {
                Logger.LogDebug($"收到订阅消息 开始处理交易{o}");
                if (o.ToString().IndexOf("ping", StringComparison.Ordinal) > -1) return;
                var sw = JsonSerializer.Deserialize<EventHandlers.Dto.CopyTradeBuySellEvent>(o.ToString(),
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });
                if (sw != null)
                {
                    //消息处理
                    await _eventPublisher.PublishAsync(EventConsts.CopyTradeBuySellEvent, sw);
                    //发送 价格订阅
                    await _redisClient.XAddAsync(StreamKey.SolanaPumpSwapPriceStream, new Dictionary<string, string>()
                    {
                        ["mint"] = sw.Mint,
                    });
                }
            }
        }));

        var dict = new Dictionary<string, string>() { ["ping"] = "1" };
        await _redisClient.PublishAsync(StreamKey.RedisSubscriberCopyTradeStream, JsonSerializer.Serialize(dict));

        streamCopyTradeTask =
            _redisClient.SubscribeStream(StreamKey.CopyTraderTaskStream, onMessage: OnRedisSubscribeStreamMessage);
        await InitTrade();
        await base.StartAsync(cancellationToken);
    }


    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        //强制退出
        await cancellationTokenSource.CancelAsync();
        streamCopyTradeTask.Dispose();
        if (dictionary.Any())
        {
            foreach (var workQueue in dictionary)
            {
                await workQueue.Value.StopAsync();
            }
        }

        Logger.LogDebug($"跟单交易 子任务服务 停止");
        await base.StopAsync(cancellationToken);
    }
}