using Tron.Abp.Multiplex.Solana;

namespace Tran.Abp.MonitorServer.BackgroundWorker;

public class TestWork : BackgroundWorkerBase
{
    public ILogger<TestWork> Logger { get; set; }
    private IRedisClient _redisClient;
    private ISolanaApi _solanaApi;
    WorkQueue<int> queue;

    public TestWork(IRedisClient redisClient, ISolanaApi solanaApi)
    {
        _redisClient = redisClient;
        _solanaApi = solanaApi;
        Logger = NullLogger<TestWork>.Instance;
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        await queue.StopAsync();
        await base.StopAsync(cancellationToken);
    }

    private async Task Processor(int i)
    {
        await _redisClient.SetAsync("test", i.ToString());
        Console.WriteLine($"{i}");
        await _redisClient.GetAsync("test");
        await Task.Delay(10000);
    }

    private async Task Init()
    {
   
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        queue = QueueManager.CreateQueue<int>(Processor, 100);
        await Init();
        await base.StartAsync(cancellationToken);
    }
}