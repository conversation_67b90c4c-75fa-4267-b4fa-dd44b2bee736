using System.Net.WebSockets;

namespace Tran.Abp.MonitorServer.Services;

public interface IWebSocketClient : IDisposable
{
    event Action OnConnected;
    event Action<byte[]> OnMessageReceived;
    event Action<string> OnDisconnected;

    WebSocketState State { get; }

    Task ConnectAsync();
    Task SendBytesAsync(byte[] data);
    Task SendStringAsync(string message);
    Task DisconnectAsync();
}