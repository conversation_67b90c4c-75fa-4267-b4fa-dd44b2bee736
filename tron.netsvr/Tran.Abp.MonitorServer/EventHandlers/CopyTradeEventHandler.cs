using DeviceDetectorNET.Class.Device;
using System.Text.Json;
using Tran.Abp.MonitorServer.BackgroundWorker;
using Tran.Abp.MonitorServer.Model;
using Tron.Abp.Multiplex.Solana;
using Tron.Abp.Nats;

namespace Tran.Abp.MonitorServer.EventHandlers;

public record TradeItem(int? PriceRatio, int? SellRatio, int? DeclineRatio);

public class CopyTradeEventSubscriber : IEventSubscriber, ISingletonDependency
{
    public ILogger<CopyTradeEventSubscriber> Logger { get; set; }
    private IRedisClient _redisClient;
    private ISolanaApi _solanaApi;
    private IAllTradesSolAmountTokenAmount _allTradesSolAmountTokenAmount;
    private IEventPublisher _eventPublisher;
    private readonly SqlSugarRepository<UserCopyTrade> _userCopyTradeRepository;
    private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;
    private readonly SqlSugarRepository<UserCopyTradeTask> _userCopyTradeTaskRepository;
    private readonly SqlSugarRepository<UserMyWallet> _userMyWalletRepository;
    private readonly SqlSugarRepository<UserCopyDelayTradeTask> _userCopyDelayTradeTask;
    private INats _nats;
    public CopyTradeEventSubscriber(IRedisClient redisClient, SqlSugarRepository<UserCopyTrade> userCopyTradeRepository,
        ISolanaApi solanaApi,
        SqlSugarRepository<UserCopyTradeTask> userCopyTradeTaskRepository, IEventPublisher eventPublisher,
        SqlSugarRepository<UserAllTraderLog> userAllTraderLogRepository,
        SqlSugarRepository<UserMyWallet> userMyWalletRepository,
        IAllTradesSolAmountTokenAmount allTradesSolAmountTokenAmount,
        SqlSugarRepository<UserCopyDelayTradeTask> userCopyDelayTradeTask, INats nats)
    {
        _redisClient = redisClient;
        _userCopyTradeRepository = userCopyTradeRepository;
        _solanaApi = solanaApi;
        _userCopyTradeTaskRepository = userCopyTradeTaskRepository;
        _eventPublisher = eventPublisher;
        _userAllTraderLogRepository = userAllTraderLogRepository;
        _userMyWalletRepository = userMyWalletRepository;
        _allTradesSolAmountTokenAmount = allTradesSolAmountTokenAmount;
        _userCopyDelayTradeTask = userCopyDelayTradeTask;
        _nats = nats;
        Logger = NullLogger<CopyTradeEventSubscriber>.Instance;
    }

    #region 私有方法

    bool CheckRange(decimal value, decimal? min, decimal? max)
    {
        if (min.HasValue && value < min.Value) return false; // 小于最小值
        if (max.HasValue && value >= max.Value) return false; // 大于等于最大值
        return true; // 其他情况通过
    }

    /// <summary>
    ///  判断当前小时是否在 时间区间 的范围内（考虑跨天情况）
    /// </summary>
    /// <param name="validTime"></param>
    /// <returns></returns>
    private bool IsHourInRange(List<int> validTime)
    {
        int start = validTime[0];
        int end = validTime[1];
        int currentHour = DateTime.Now.Hour;

        if (start == end) return true; // 相等表示全天有效
        return start < end
            ? currentHour >= start && currentHour <= end
            : currentHour >= start || currentHour <= end;
    }

    /// <summary>
    /// 相同代币值买入一次
    /// </summary>
    /// <param name="uid"></param>
    /// <param name="mint"></param>
    /// <returns></returns>
    private async Task<bool> IsSameTokenOnceBuy(int uid, int tid, string mint)
    {
        var isExists = await _redisClient.HExistsAsync(RedisKey.SameTokenOnceBuy, $"{uid}");
        if (!isExists) return false;
        var dictMints = await _redisClient.HGetAsync<Dictionary<int, string[]>>(RedisKey.SameTokenOnceBuy, $"{uid}");
        return dictMints.ContainsKey(tid) && dictMints[tid].Contains(mint);
    }

    /// <summary>
    /// 相同代币值买入一次 写入
    /// </summary>
    /// <param name="uid"></param>
    /// <param name="tid"></param>
    /// <param name="mint"></param>
    /// <returns></returns>
    private async Task<bool> SetSameTokenOnceBuy(int uid, int tid, string mint)
    {
        var isExists = await _redisClient.HExistsAsync(RedisKey.SameTokenOnceBuy, $"{uid}");
        if (!isExists)
        {
            var dict = new Dictionary<int, string[]>()
            {
                [tid] = new[] { mint }
            };
            await _redisClient.HSetAsync(RedisKey.SameTokenOnceBuy, $"{uid}", dict);
        }
        else
        {
            var dict = await _redisClient.HGetAsync<Dictionary<int, string[]>>(RedisKey.SameTokenOnceBuy, $"{uid}");
            //[1,["11","22"]]
            if (!dict.ContainsKey(tid))
            {
                dict[tid] = new[] { mint };
                await _redisClient.HSetAsync(RedisKey.SameTokenOnceBuy, $"{uid}", dict);
            }
            else
            {
                var arry = dict[tid];
                if (!Array.Exists(arry, x => x == mint))
                {
                    Array.Resize(ref arry, arry.Length + 1);
                    arry[arry.Length - 1] = mint;
                    dict[tid] = arry;
                    await _redisClient.HSetAsync(RedisKey.SameTokenOnceBuy, $"{uid}", dict);
                }
            }
        }

        return true;
    }

    /// <summary>
    /// 是否不买入已持有代币
    /// </summary>
    /// <param name="uid"></param>
    /// <param name="mint"></param>
    /// <returns></returns>
    private async Task<bool> IsTokenInventory(int uid, string pubkey, string mint)
    {
        var isExists = await _redisClient.HExistsAsync(RedisKey.TokenInventory, $"{uid}");
        if (!isExists) return false;
        var dictMints = await _redisClient.HGetAsync<Dictionary<string, string[]>>(RedisKey.TokenInventory, $"{uid}");
        return dictMints.ContainsKey(pubkey) && dictMints[pubkey].Contains(mint);
    }

    /// <summary>
    /// 写持仓
    /// </summary>
    /// <param name="uid"></param>
    /// <param name="pubkey"></param>
    /// <param name="mint"></param>
    /// <returns></returns>
    private async Task<bool> SetTokenInventory(int uid, string pubkey, string mint)
    {
        var isExists = await _redisClient.HExistsAsync(RedisKey.TokenInventory, $"{uid}");
        if (!isExists)
        {
            var dict = new Dictionary<string, string[]>()
            {
                [pubkey] = new[] { mint }
            };
            await _redisClient.HSetAsync(RedisKey.TokenInventory, $"{uid}", dict);
        }
        else
        {
            var dict = await _redisClient.HGetAsync<Dictionary<string, string[]>>(RedisKey.TokenInventory, $"{uid}");
            if (!dict.ContainsKey(pubkey))
            {
                dict[pubkey] = new[] { mint };
                await _redisClient.HSetAsync(RedisKey.TokenInventory, $"{uid}", dict);
            }
            else
            {
                var arry = dict[pubkey];
                if (!Array.Exists(arry, x => x == mint))
                {
                    Array.Resize(ref arry, arry.Length + 1);
                    arry[arry.Length - 1] = mint;
                    dict[pubkey] = arry;
                    await _redisClient.HSetAsync(RedisKey.TokenInventory, $"{uid}", dict);
                }
            }
        }

        return true;
    }

    /// <summary>
    /// 自动止盈  解析 
    /// </summary>
    /// <param name="autoData"></param>
    /// <param name="mintPrice"></param>
    /// <param name="ct"></param>
    /// <param name="logModel"></param>
    /// <param name="logId"></param>
    /// <param name="tasks"></param>
    private void ProcessRiseMode(List<RiseSlumpData> riseData, decimal mintPrice, UserCopyTrade ct,
        UserAllTraderLog logModel,
        string logId, List<UserCopyTradeTask> tasks)
    {
        // 止盈 (Rise)
        decimal risePrice = mintPrice;
        foreach (var (item, i) in riseData.DefaultIfEmpty().Select((item, i) => (item, i)))
        {
            if (item == null) break;
            var curPriceRatio = riseData[i == 0 ? 0 : i - 1]?.PriceRatio ?? 0;
            risePrice = mintPrice; //i == 0 ? mintPrice : risePrice * (1 + curPriceRatio / 100m);
            var task = CreateTradeTask(ct, logModel, logId, risePrice, 1, 1,
                new TradeItem(item.PriceRatio, item.SellRatio, 0));
            if (task != null)
                tasks.Add(task);
        }
    }

    /// <summary>
    /// 自动止损
    /// </summary>
    /// <param name="slumpData"></param>
    /// <param name="mintPrice"></param>
    /// <param name="ct"></param>
    /// <param name="logModel"></param>
    /// <param name="logId"></param>
    /// <param name="tasks"></param>
    private void ProcessSlumpMode(List<RiseSlumpData> slumpData, decimal mintPrice, UserCopyTrade ct,
        UserAllTraderLog logModel, string logId, List<UserCopyTradeTask> tasks)
    {
        // 止损 (Slump)
        decimal slumpPrice = mintPrice;
        foreach (var (item, i) in slumpData.DefaultIfEmpty().Select((item, i) => (item, i)))
        {
            if (item == null) break;
            var curPriceRatio = slumpData[i == 0 ? 0 : i - 1].PriceRatio ?? 0;
            slumpPrice = mintPrice; //i == 0 ? mintPrice : slumpPrice * (1 - curPriceRatio / 100m);
            var task = CreateTradeTask(ct, logModel, logId, slumpPrice, 1, 2,
                new TradeItem(item.PriceRatio, item.SellRatio, 0));
            if (task != null)
                tasks.Add(task);
        }
    }

    /// <summary>
    /// 追踪止盈止损  解析 
    /// </summary>
    /// <param name="moveData"></param>
    /// <param name="mintPrice"></param>
    /// <param name="ct"></param>
    /// <param name="logModel"></param>
    /// <param name="logId"></param>
    /// <param name="tasks"></param>
    private void ProcessMoveMode(List<MoveData> moveData, decimal mintPrice, UserCopyTrade ct,
        UserAllTraderLog logModel, string logId, List<UserCopyTradeTask> tasks)
    {
        decimal curPrice = mintPrice;
        foreach (var (item, i) in moveData.DefaultIfEmpty().Select((item, i) => (item, i)))
        {
            if (item == null) break;
            var curPriceRatio = moveData[i == 0 ? 0 : i - 1].PriceRatio ?? 0;
            curPrice = mintPrice; // i == 0 ? mintPrice : curPrice * (1 - curPriceRatio / 100m);
            var task = CreateTradeTask(ct, logModel, logId, curPrice, 2, 0,
                new TradeItem(item.PriceRatio, item.SellRatio, item.DeclineRatio));
            if (task != null)
                tasks.Add(task);
        }
    }

    /// <summary>
    /// 创建任务
    /// </summary>
    /// <param name="ct"></param>
    /// <param name="logModel"></param>
    /// <param name="logId"></param>
    /// <param name="price"></param>
    /// <param name="stopLimitMode"></param>
    /// <param name="autoMode"></param>
    /// <param name="item"></param>
    /// <returns></returns>
    private UserCopyTradeTask CreateTradeTask(UserCopyTrade ct, UserAllTraderLog logModel, string logId, decimal price,
        int stopLimitMode, int autoMode, TradeItem item)
    {
        if (stopLimitMode == 2 && item.PriceRatio == null && item.SellRatio == null &&
            item.DeclineRatio == null) return null;
        if (stopLimitMode == 1 && item.PriceRatio == null && item.SellRatio == null) return null;
        var autoData = stopLimitMode == 1
            ? new AutoModel { PriceRatio = item.PriceRatio!.Value, SellRatio = item.SellRatio!.Value }
            : null;
        var moveData = stopLimitMode == 2
            ? new MoveModel
            {
                PriceRatio = item.PriceRatio!.Value,
                SellRatio = item.SellRatio!.Value,
                DeclineRatio = item.DeclineRatio!.Value
            }
            : null;
        var task = new UserCopyTradeTask
        {
            UId = ct.UId,
            Chain = ct.Chain,
            TokenName = logModel.TokenName,
            TokenSymbol = logModel.TokenSymbol,
            TokenIcon = logModel.TokenIcon,
            TokenAddress = logModel.TokenAddress,
            LogId = logId,
            CtId = ct.Id,
            WalletId = ct.WalletId,
            PubKey = ct.PubKey,
            SecretKey = ct.SecretKey,
            FollowWalletAddress = logModel.FollowWalletAddress,
            FollowWalletMark = logModel.FollowWalletMark,
            TradeMode = ct.SellSettings.TradeModel,
            Gas = ct.SellSettings.Gas,
            Mev = ct.SellSettings.Mev,
            Slippage = ct.SellSettings.Slippage ?? 10,
            BuyPrice = price,
            HighestPrice = price,
            StopLimitMode = stopLimitMode,
            AutoMode = autoMode,
            AutoData = autoData,
            MoveData = moveData,
            Expired = ct.SellSettings.Expired ?? 120,
            ExpiredAuto = ct.SellSettings.ExpiredAuto,
            Status = "active",
            IsRuning = true,
            CreateTime = DateTime.Now,
            IsMev = ct.SellSettings.IsMev,
            MevType = ct.SellSettings.MevType,
        };
        task.ExpiredTime = DateTime.Now.AddHours(task.Expired);
        return task;
    }

    /// <summary>
    /// 计算条件买入 
    /// </summary>
    private decimal ProcessBuyCondition(List<BuyCondition> buyConditions, string mint, decimal smartBuySol,
        Tron.Abp.Multiplex.Contracts.JupAgStreamResult.PoolData token)
    {
        bool IsConditionMet(BuyConditionModel condition, decimal mcap, long holderCount, decimal smartBuySol)
        {
            return condition.Condition switch
            {
                1 => mcap > 0 && condition.Min <= mcap && mcap < condition.Max,
                2 => holderCount > 0 && condition.Min <= holderCount && holderCount < condition.Max,
                3 => condition.Min <= smartBuySol && smartBuySol < condition.Max,
                _ => false
            };
        }


        decimal CalculateBuyAmount(BuyCondition condition, decimal smartBuySol)
        {
            return condition.BuyType switch
            {
                1 => condition.BuyValue ?? 0, // 固定金额
                2 => smartBuySol * (condition.BuyValue ?? 0) / 100m, // 比例
                _ => 0m
            };
        }

        if (buyConditions == null || !buyConditions.Any() /*|| token == null*/)
            return 0m;
        var curSol = 0m;
        var mcap = token?.BaseAsset?.Mcap ?? 0;
        var holderCount = token?.BaseAsset?.HolderCount ?? 0;

        foreach (var item in buyConditions)
        {
            var isBuy = false;
            foreach (var buyConditionModel in item.ConditionData)
            {
                if (buyConditionModel.Min == null || buyConditionModel.Max == null) continue;
                isBuy = IsConditionMet(buyConditionModel, mcap, holderCount, smartBuySol);
                if (!isBuy) break;
            }

            if (isBuy)
                curSol += CalculateBuyAmount(item, smartBuySol);
        }

        return curSol;
    }

    /// <summary>
    /// 建立子任务
    /// </summary>
    /// <param name="stopLimit"></param>
    private async Task CreateStopLimit(string mint, string dexType, UserCopyTrade ct, UserAllTraderLog logModel,
        string poolId)
    {
        //0409 增加 如果是pump amm 交易 需要订阅 价格 
        if (dexType == DexType.PumpSwapAmm)
        {
            if (await _redisClient.ExistsAsync(RedisKey.SolanaSubscriberTokenPumpPool))
            {
                var mints = await _redisClient.GetAsync<Dictionary<string, int>>(RedisKey
                    .SolanaSubscriberTokenPumpPool);
                if (!mints.TryAdd(mint, 1))
                {
                    mints[mint] += 1;
                }

                await _redisClient.SetAsync(RedisKey.SolanaSubscriberTokenPumpPool, mints);
            }
            else
            {
                var mints = new Dictionary<string, int>()
                {
                    [mint] = 1
                };
                await _redisClient.SetAsync(RedisKey.SolanaSubscriberTokenPumpPool, mints);
            }
        }

        //获取代币当前价格
        var mintPrice = await _solanaApi.GetMintPrice(mint, dexType, poolId);
        //var mintPrice = priceData?.Data?.GetValueOrDefault(mint)?.Price ?? 0m;
        var logId = Cryptography.Obfuscation.Factory.ObfuscatorFactory.NewInstance.Obfuscate(logModel.Id);

        var tasks = new List<UserCopyTradeTask>();
        var stopLimit = ct.SellSettings.StopLimit;
        //Rise 
        if (stopLimit.LimitModel == 1)
            ProcessRiseMode(stopLimit.Rise, mintPrice, ct, logModel, logId, tasks);
        //Slump
        ProcessSlumpMode(stopLimit.Slump, mintPrice, ct, logModel, logId, tasks);
        //Move
        if (stopLimit.LimitModel == 2)
            ProcessMoveMode(stopLimit.Move, mintPrice, ct, logModel, logId, tasks);

        if (tasks.Any())
        {
            var ids = await _userCopyTradeTaskRepository.CopyNew()
                .AsInsertable(tasks)
                .ExecuteReturnPkListAsync<int>();
            var autoNum = 0; // stopLimit?.Autodata?.Rise?.Count + stopLimit?.Autodata?.Slump?.Count ?? 0;
            var moveNum = 0; // stopLimit?.Movedata?.Count ?? 0;
            if (stopLimit?.Rise != null)
            {
                autoNum += stopLimit?.Rise?.Where(it => it.PriceRatio > 0)?.Count() ?? 0;
            }

            if (stopLimit?.Slump != null)
            {
                autoNum += stopLimit?.Slump?.Where(it => it.PriceRatio > 0)?.Count() ?? 0;
            }

            if (stopLimit?.Move != null)
            {
                moveNum = stopLimit?.Move.Where(it => it.PriceRatio > 0)?.Count() ?? 0;
            }

            //更新 父级任务 记录数
            await _eventPublisher.PublishAsync(EventConsts.DBUpdata_UserCopyTrade_TaskNum,
                new UpdataUserCopyTradeTaskNumEventData(ct.Id, autoNum, moveNum));
            //以下进行发送任务处理
            //1.代币加入 获取 余额列表
            var priceDict = new Dictionary<string, string>()
            {
                ["mint"] = mint,
                ["type"] = "1"
            };
            var streamKey = dexType switch
            {
                DexType.Pumpfun => StreamKey.EnhancedWsPumpFunStream,
                DexType.RaydiumAmm => StreamKey.EnhancedWsRaydiumStream,
                DexType.PumpSwapAmm => StreamKey.EnhancedWsRaydiumStream,
            };
            //发送消息
            await _redisClient.XAddAsync(streamKey, priceDict);
            // Uid  LogId Mint
            //跟单交易 子任务服务 
            var dict = new Dictionary<string, string>()
            {
                ["Uid"] = ct.UId.ToString(),
                ["LogId"] = logId.ToString(),
                ["CtId"] = ct.Id.ToString(),
                ["DexType"] = dexType,
                ["Mint"] = mint,
                ["PoolId"] = poolId,
            };
            //发送消息
            await _redisClient.XAddAsync(StreamKey.CopyTraderTaskStream, dict);
        }
    }

    /// <summary>
    /// 跟单 消息处理
    /// </summary>
    /// <param name="sw"></param>
    private async Task BuySellAction(Dto.CopyTradeBuySellEvent sw)
    {
        Logger.LogDebug($"开始处理跟单任务{sw.Id}=>{sw.Mint}=>{sw.IsBuy}");

        var ct = await _userCopyTradeRepository.AsQueryable().Where(it => it.Id == sw.Id).FirstAsync();
        if (ct == null || ct.Status != 1)
        {
            Logger.LogDebug($"开始处理跟单任务{sw.Id}=>{sw.Mint}=>{sw.IsBuy} 跟单任务已停止");
            return;
        }
        
        var account = Solnet.Wallet.Account.FromSecretKey(ct.SecretKey);
        
        //黑名单 0409 更改为 redis存储
        //if (ct.TokenBlackList != null && ct.TokenBlackList.Contains(sw.Mint)) return;
        if (await _redisClient.HExistsAsync(RedisKey.CopyTradeBlackListing, $"{ct.UId}"))
        {
            var tokenBlackList =
                await _redisClient.HGetAsync<List<string>>(RedisKey.CopyTradeBlackListing, $"{ct.UId}");
            if (tokenBlackList.Contains(sw.Mint))
            {
                Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 {sw.Mint}在黑名单内");
                return;
            }
        }

        // ["pump","raydium-amm"]
        if (sw.DexType == DexType.Pumpfun && !ct.DexList.Contains("pump"))
        {
            Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 指定是pump DexList但没有包含");
            return;
        }

        if (sw.DexType == DexType.RaydiumAmm && !ct.DexList.Contains("raydium-amm"))
        {
            Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 指定是raydium DexList但没有包含");
            return;
        }

        if (sw.DexType == DexType.PumpSwapAmm && !ct.DexList.Contains("pump-amm"))
        {
            Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 指定是PumpSwapAmm DexList但没有包含");
            return;
        }

        if (sw.DexType == DexType.RaydiumClmm && !ct.DexList.Contains(DexType.RaydiumClmm.ToString()))
        {
            Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 指定是RaydiumClmm DexList但没有包含");
            return;
        }

        if (sw.DexType == DexType.RaydiumCpmm && !ct.DexList.Contains(DexType.RaydiumCpmm.ToString()))
        {
            Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 指定是RaydiumCpmm DexList但没有包含");
            return;
        }

        if (sw.DexType == DexType.Launchpad && !ct.DexList.Contains(DexType.Launchpad.ToString()))
        {
            Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消  Launchpad DexList但没有包含");
            return;
        }
        if (sw.DexType == DexType.MeteoraDYN && !ct.DexList.Contains(DexType.MeteoraDYN.ToString()))
        {
            Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消  MeteoraDYN DexList但没有包含");
            return;
        }
        if (sw.DexType == DexType.MeteoraDlmm && !ct.DexList.Contains(DexType.MeteoraDlmm.ToString()))
        {
            Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消  MeteoraDlmm DexList但没有包含");
            return;
        }
        if (sw.DexType == DexType.MeteoraDBC && !ct.DexList.Contains(DexType.MeteoraDBC.ToString()))
        {
            Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消  MeteoraDBC DexList但没有包含");
            return;
        }
        //防割 启用
        if (ct.AntiCutting.IsOpen)
        {
            var smartWallet = ct.FollowWallet.First(it => it.WalletAddress == sw.Signer);
            if (smartWallet != null && smartWallet.Status ==0)
            {
                Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 聪明钱包 {sw.Signer} 处于永久暂停状态");
                return;
            } else if (smartWallet != null && smartWallet.Status == 2)
            {
                //如果是 临时 暂停 且已经过期 恢复状态
                if (smartWallet.PauseEndTime.HasValue && smartWallet.PauseEndTime.Value < DateTime.Now)
                {
                    //更新钱包 状态
                    //满足 暂停 跟单 条件 更新记录
                    ct.FollowWallet.ForEach(it =>
                    {
                        if (it.WalletAddress ==sw.Signer)
                        {
                            it.Status = 1;
                            it.PauseEndTime = DateTime.Now;
                        }
                    });
                    await _userCopyTradeRepository.CopyNew().AsUpdateable()
                        .SetColumns(it=>it.FollowWallet==ct.FollowWallet)
                        .Where(s => s.Id == ct.Id)
                        .ExecuteCommandAsync();
                    
                    //统计 代币 盈亏 清空计
                    await _eventPublisher.PublishAsync(EventConsts.CopyTradeLossDataAnalysisRestoreData,
                        new CopyTradeLossDataAnalysisEvent(ct.UId, ct.Id, sw.Mint, true,
                            account.PublicKey, sw.Signer, sw.SolAmount, false)
                    );
                    
                }
                else
                {
                    Logger.LogDebug(
                        $"跟单任务{sw.Id} {sw.Mint} 取消 聪明钱包 {sw.Signer} 处于临时暂停状态 到期时间{smartWallet.PauseEndTime}");
                    return;
                }
            }
        }

        //聪明钱包持有不买入
        if (ct.BuySettings.FollowWalletHoldToken && sw.SmartWalletIsHold)
        {
            Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 聪明钱包 {sw.Signer} 已经持仓");
            return;
        }

        //聪明钱包非首次交互，不买入 开启的情况 判断该代币曾经购买过，不买入
        if (ct.BuySettings.FollowWalletFirstBuy && !string.IsNullOrEmpty(sw.Sign))
        {
            var flag = await _solanaApi.GetSignaturesForAddress(sw.Mint, sw.Signer, sw.Sign);
            if (flag)
            {
                Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 聪明钱包 {sw.Signer} 已经非首次买入");
                return;
            }
        }

        //代币创建时间
        if (ct.BuySettings.TokenCreateTime.Count == 2)
        {
            var tokenInfo = await _solanaApi.GetMintBaseAsset(sw.Mint);
            if (tokenInfo == null)
            {
                Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 代币创建时间  没有找到代币信息");
                return;
            }
            var localTime = tokenInfo.CreatedAt.ToLocalTime();
            var createTime = (int)(DateTime.Now - localTime).TotalMinutes;
            var min = ct.BuySettings.TokenCreateTime[0];
            var max = ct.BuySettings.TokenCreateTime[1];

            if (min.HasValue && createTime < min.Value)
            {
                Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 代币创建时间大于设置时间 {min.Value} 建立时间:{createTime}");
                return;
            }

            if (max.HasValue && createTime > max.Value)
            {
                Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 代币创建时间小于设置时间 {max.Value} 建立时间:{createTime}");
                return;
            }
        }

        var myWallet = await _userMyWalletRepository.AsQueryable()
            .Where(it => it.UId == ct.UId && it.Id == ct.WalletId).FirstAsync();
        //聪明钱包 解析
        var followWallet = ct.FollowWallet.First(it => it.WalletAddress == sw.Signer);
        //获取代币信息
        var token = await _solanaApi.GetMint(sw.Mint, sw.DexType);


        //买入
        if (sw.IsBuy)
        {
            var model = ct.BuySettings;
            if (!model.IsOpen) return;

            //时间区间
            if (!IsHourInRange(model.ValidTime))
            {
                Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 非指定时间区间交易");
                return;
            }

            //相同代币值买入一次 
            if (model.SameTokenOnceNotBuy && await IsSameTokenOnceBuy(ct.UId, ct.Id, sw.Mint))
            {
                Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 相同代币值买入一次");
                return;
            }

            //是否不买入已持有代币  
            if (!model.HoldTokenNotBuy && await IsTokenInventory(ct.UId, ct.PubKey, sw.Mint))
            {
                Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消 买入已持有代币 ");
                return;
            }


            var solBuy = sw.SolAmount;
            var operation = $"";

            switch (model.BuyModel)
            {
                case 1: //条件买入
                    solBuy = ProcessBuyCondition(model.BuyCondition, sw.Mint, sw.SolAmount, token);
                    operation = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss}|条件买入";
                    break;
                case 2: //固定买入
                    solBuy = model.BuyAmount ?? 0;
                    operation = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss}|固定买入";
                    break;
                case 3: //比例买入
                    solBuy = sw.SolAmount * model.BuyRatio.Value / 100;
                    var buyMaxAmount = model.BuyMaxAmount ?? 0;
                    solBuy = model.BuyMaxIsOpen && buyMaxAmount > 0 && solBuy > buyMaxAmount ? buyMaxAmount : solBuy;
                    operation = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss}|比例买入";
                    break;
            }


            if (solBuy == 0)
            {
                Logger.LogDebug($"跟单任务{sw.Id} {sw.Mint} 取消  sol为0 ");
                return;
            }

            //0409 条件买入 结构变更 新增
            if (model.BuyConditionData != null && model.BuyConditionData.Any())
            {
                var mcap = (token?.BaseAsset?.Mcap ?? 0) / 1000; //市值区间基础单位用K来表示 1K=1000
                var holderCount = token?.BaseAsset?.HolderCount ?? 0;
                var smartWalletBuy = sw.SolAmount;
                var isBuyFlag = model.BuyConditionData.All(condition => condition switch
                {
                    { Condition: 1 } => CheckRange(mcap, condition.Min, condition.Max),
                    { Condition: 2 } => CheckRange(holderCount, condition.Min, condition.Max),
                    { Condition: 3 } => CheckRange(smartWalletBuy, condition.Min, condition.Max),
                    _ => true // 未知条件默认通过
                });
                if (isBuyFlag == false)
                {
                    Logger.LogWarning(
                        $"跟单任务{sw.Id} {sw.Mint} 条件买入不符合 实际值:{mcap}：{holderCount}：{smartWalletBuy} 设置值:{JsonSerializer.Serialize(model.BuyConditionData)}");
                    return;
                }
            }

            //延时 购买
            if (model.DelayBuy.IsOpen)
            {
                var isSend =
                    await _redisClient.HExistsAsync(RedisKey.CopyTradeDelayBuyTokenSell,
                        $"{sw.Id}:{sw.Signer}:{sw.Mint}")
                        ? await _redisClient.HGetAsync(RedisKey.CopyTradeDelayBuyTokenSell,
                            $"{sw.Id}:{sw.Signer}:{sw.Mint}")
                        : "0";
                if (isSend == "1")
                {
                    Logger.LogDebug($"跟单任务{sw.Id}  {sw.Mint} 延时任务 聪明钱包存在买入且卖出行为，买入不处理");
                    return;
                }

                await _redisClient.HSetAsync(RedisKey.CopyTradeDelayBuyTokenSell, $"{sw.Id}:{sw.Signer}:{sw.Mint}",
                    "0");
                //var tokenPrice = (sw.SolAmount / sw.TokenAmount) * (1 - model.DelayBuy.PriceRatio / 100); 
                await Task.Delay(model.DelayBuy.Second ?? 10);

                var tokenPrice1 = await _solanaApi.GetMintPrice(sw.Mint, sw.DexType, sw.PoolId);
                var tokenPrice2 = (sw.SolAmount / sw.TokenAmount);
                Logger.LogDebug($"跟单任务{sw.Id}  {sw.Mint} 触发延时任务 当前价格:{tokenPrice1} => 聪明钱包交易价格:{tokenPrice2}");
                var tokenPrice = tokenPrice1;
                if (tokenPrice > 0)
                {
                    //写入延时买入记录 
                    var dtt = new UserCopyDelayTradeTask()
                    {
                        CtId = ct.Id,
                        UId = ct.UId,
                        TokenAddress = sw.Mint,
                        CreateTime = DateTime.Now,
                        Owner = sw.Signer,
                    };
                    dtt.Id = await _userCopyDelayTradeTask.CopyNew().AsInsertable(dtt).ExecuteReturnIdentityAsync();
                    var price = tokenPrice * ((decimal)(model?.DelayBuy?.PriceRatio ?? 0) / 100m);
                    // 聪明钱包  mint 购买sol数量  代币价格下降百分比 跟单任务ID 0331 更改为 可上涨可下跌
                    var delayBuyDict = new Dictionary<string, string>()
                    {
                        ["mint"] = sw.Mint,
                        ["sprice"] = (tokenPrice2).ToString(), //聪明钱包交易价格
                        ["cprice"] = (tokenPrice2).ToString(), //当前价格
                        ["priceratio"] = ((model?.DelayBuy?.PriceRatio ?? 0)).ToString(), //上涨可下跌百分比
                        ["owner"] = sw.Signer,
                        ["solBuy"] = solBuy.ToString(),
                        ["DexType"] = sw.DexType,
                        ["taskid"] = ct.Id.ToString(),
                        ["poolid"] = sw.PoolId,
                        ["dttId"] = dtt.Id.ToString()
                    };
                    //发送消息
                    await _redisClient.XAddAsync(StreamKey.CopyTradeDelayBuyTaskStream, delayBuyDict);
                    Logger.LogDebug($"跟单任务{sw.Id}  {sw.Mint} 发送延时任务  当前要买入价格:{price}=>聪明钱包交易价格:{tokenPrice2}");
                }
                else
                {
                    Logger.LogWarning($"跟单任务{sw.Id} {sw.Mint} 没有找到价格");
                }

                return;
            }


            var gas = 0m;
            var mev = 0m;
            switch (model.TradeModel)
            {
                case 1:
                    gas = model.Gas ?? 0.000005m;
                    mev = 0;
                    break;
                case 2:
                    gas = 0;
                    mev = model.Mev ?? 0;
                    break;
                case 3:
                    gas = model.Gas ?? 0.000005m;
                    mev = model.Mev ?? 0;
                    break;
                default:
                    break;
            }

            var isMev = model.IsMev;
            var mevType = model.MevType;
           
            Logger.LogDebug($"跟单任务 买入 发送交易 {sw.Id} =>{sw.Mint} =>{sw.PoolId}");
            var (successful, sign, errormsg) = await _solanaApi.Buy(sw.DexType, account, sw.Mint, solBuy,
                model.Slippage ?? 10m, gas, mev, sw.PoolId, isMev:isMev,mevList:mevType);
            Logger.LogDebug($"跟单任务 买入 交易返回 {sw.Id} =>{sw.Mint}=>{sign}=>{errormsg}");
            //如果用户 余额为0 或者 不足以购买代币 则 取消任务  2025 05 13 1441
            var userBlanece = await _solanaApi.GetBalanceAsync(ct.PubKey);
            if (userBlanece < solBuy)
            {
                Logger.LogDebug($"跟单任务:{sw.Id} {sw.Mint} 取消 用户 {ct.PubKey} 余额为0");
                //更新任务状态
                await _userCopyTradeRepository.AsUpdateable()
                    .SetColumns(it => it.Status == 0)
                    .Where(it => it.Id == ct.Id)
                    .ExecuteCommandAsync();
                var ids = new string[] { ct.Id.ToString() };
                var address = ct.FollowWallet.Select(it => it.WalletAddress).ToArray();
                //发送停止消息
                var dict = new Dictionary<string, string>()
                {
                    //用户ID-》任务 ID=》操作类型=》运行状态
                    ["uid"] = ct.UId.ToString(),
                    ["type"] = "2",//停止
                    ["tid"] = string.Join(",", ids),
                    ["address"] = string.Join(",", address),
                    ["status"] = "0"
                };
                //发送消息
                await _redisClient.XAddAsync(StreamKey.CopyTraderWsStream, dict);
                Logger.LogDebug($"跟单任务:{sw.Id} {sw.Mint} 取消 用户 {ct.PubKey} 发送取消订阅消息");
                return;
            }

            await Task.Delay(500);
            //解析交易
            var (solAmount, tokenAmount) = await _solanaApi.ParserTransaction(sign, sw.Mint, sw.DexType, true);

            //上链交易 写交易记录
            var logModel = new UserAllTraderLog()
            {
                TraderType = TraderType.Trader_02,
                TaskId = sw.Id,
                UId = ct.UId,
                Chain = ct.Chain,
                IsBuy = sw.IsBuy,
                SolAmount = solAmount,
                TokenAmount = tokenAmount,
                Gas = gas,
                Mev = mev,
                MyWalletAddress = myWallet.PubKey,
                //MyWalletMark = myWallet.Name,
                FollowWalletAddress = followWallet?.WalletAddress ?? "",
                FollowWalletMark = followWallet?.WalletName ?? "",
                TokenAddress = sw.Mint,
                Signature = sign,
                Status = string.IsNullOrWhiteSpace(sign) ? "跟单-失败" : "跟单-成功",
                CreateTime = DateTime.Now,
            };
            if (token != null)
            {
                logModel.TokenName = token.BaseAsset.Name;
                logModel.TokenSymbol = token.BaseAsset.Symbol;
                logModel.TokenIcon = token.BaseAsset.Icon;
            }
            else
            {
                //请求获取 todo:待定
                logModel.TokenName = "-";
                logModel.TokenSymbol = "-";
                logModel.TokenIcon = "-";
            }

            logModel.Id = await _userAllTraderLogRepository.CopyNew().AsInsertable(logModel)
                .ExecuteReturnIdentityAsync();

            //交易失败
            if (string.IsNullOrWhiteSpace(sign))
            {
                Logger.LogDebug($"跟单任务{sw.Id} 买入 {sw.Mint} 交易失败 上链失败 ");
                return;
            }

            var atuae = new AllTradeUpdateAmountEvent(logModel.Id, sign, sw.Mint, sw.DexType, true, "跟单",
                logModel.FollowWalletAddress, 0);
            var (isSuccessful, solBuyAmount, _) =
                await _allTradesSolAmountTokenAmount.UpdateAllTrades_SolAmount_TokenAmount(atuae);
            if (!isSuccessful)
            {
                Logger.LogDebug($"跟单任务{sw.Id} 买入 {sw.Mint} 获取交易记录 无法解析/交易失败 ");
                return;
            }

            //存 redis 只买一次
            await SetSameTokenOnceBuy(ct.UId, ct.Id, sw.Mint);
            //存 redis 持仓
            await SetTokenInventory(ct.UId, ct.PubKey, sw.Mint);

            //更新 最后操作
            await _eventPublisher.PublishAsync(EventConsts.DBUpdata_UserCopyTrade_Operation,
                new UpdataUserCopyTradeOperationEventData(ct.Id, operation));
            //更新 买入统计
            await _eventPublisher.PublishAsync(EventConsts.DBUpdata_UserCopyTrade_Statistical,
                new UpdataUserCopyTradeStatisticalEventData(ct.Id, sw.IsBuy, 0, solBuy)
            );
            //更新 代币持有
            await _eventPublisher.PublishAsync(EventConsts.DBUpdata_UserMyWallet_Held,
                new UpdataUserMyWalletHeldEventData(ct.WalletId));

            //统计 代币 盈亏
            await _eventPublisher.PublishAsync(EventConsts.CopyTradeLossDataAnalysisAddUpLog,
                new CopyTradeLossDataAnalysisEvent(ct.UId, ct.Id, sw.Mint, true,
                    account.PublicKey, sw.Signer, solBuy, false)
            );

            //建立 止盈止损 子任务 
            if (ct.SellSettings.SellModel == 3 || ct.SellSettings.SellModel == 1)
            {
                //建立子任务 发送消息
                await CreateStopLimit(sw.Mint, sw.DexType, ct, logModel, sw.PoolId);
            }
        }
        //卖出
        else
        {
            var model = ct.SellSettings;
            if (!model.IsOpen) return;
            //时间区间
            if (!IsHourInRange(model.ValidTime)) return;
            var sellPercentage = (decimal)sw.Percentage;
            var operation = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss}|跟随卖出";
            if (model.SellModel == 3)
            {
                Logger.LogDebug($"跟单任务 卖出 3止盈止损 {sw.Id} =>{sw.Mint} 不执行任务 返回");
                return;
            }

            operation = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss}|比例卖出";
            //卖出 跟单 百分比的百分比
            sellPercentage = (((decimal)sw.Percentage / 100m) * (model.SellRatio.Value / 100m)) * 100;


            var gas = 0m;
            var mev = 0m;
            switch (model.TradeModel)
            {
                case 1:
                    gas = model.Gas ?? 0.000005m;
                    mev = 0;
                    break;
                case 2:
                    gas = 0;
                    mev = model.Mev ?? 0;
                    break;
                case 3:
                    gas = model.Gas ?? 0.000005m;
                    mev = model.Mev ?? 0;
                    break;
                default:
                    break;
            }
            var isMev = model.IsMev;
            var mevType = model.MevType;
            //var account = Solnet.Wallet.Account.FromSecretKey(ct.SecretKey);
            Logger.LogDebug($"跟单任务 卖出 发送交易 {sw.Id} =>{sw.Mint}");
            var (successful, sign, errormsg) = await _solanaApi.Sell(sw.DexType, account, sw.Mint, sellPercentage, gas,
                mev, model.Slippage ?? 10m, sw.PoolId,isMev:isMev,mevList:mevType);
            /*var sign = await _solanaApi.SellAmount(sw.DexType, account, sw.Mint, 0.001506m, gas, mev,
                model.Slippage ?? 10m, sw.PoolId);*/
            Logger.LogDebug($"跟单任务 卖出 交易返回 {sw.Id} =>{sw.Mint}=>{successful}=>{sign}");
            //交易失败
            if (string.IsNullOrWhiteSpace(sign))
            {
                Logger.LogDebug($"跟单任务{sw.Id} 卖出  {sw.Mint} 交易失败 上链失败 ");
                await _nats.PublishAsync(ct.UId.ToString(), new NatsSolanaTradeMsg()
                {
                    TraderType = (int)TraderType.Trader_02,
                    TradeTypeMark = "跟单任务",
                    Successful = successful,
                    Sign = sign,
                    Errormsg = errormsg,
                    Data = new NatsSolanaTradeMsgData()
                    {

                        Mint = sw.Mint,
                        IsBuy =false,
                        SolAmount = 0,
                        TotalAmount = 0,
                    }
                });
                return;
            }

            await Task.Delay(500);
            //解析交易
            var (solAmount, tokenAmount) = await _solanaApi.ParserTransaction(sign, sw.Mint, sw.DexType, false);
            //上链交易 写交易记录
            var logModel = new UserAllTraderLog()
            {
                TraderType = TraderType.Trader_02,
                TaskId = sw.Id,
                UId = ct.UId,
                Chain = ct.Chain,
                IsBuy = sw.IsBuy,
                SolAmount = solAmount,
                TokenAmount = tokenAmount,
                Gas = gas,
                Mev = mev,
                MyWalletAddress = myWallet.PubKey,
                //MyWalletMark = myWallet.Name,
                FollowWalletAddress = followWallet?.WalletAddress ?? "",
                FollowWalletMark = followWallet?.WalletName ?? "",
                TokenAddress = sw.Mint,
                Signature = sign,
                Status = string.IsNullOrWhiteSpace(sign) ? "跟单-失败" : "跟单-成功",
                CreateTime = DateTime.Now,
            };
            if (token != null)
            {
                logModel.TokenName = token.BaseAsset.Name;
                logModel.TokenSymbol = token.BaseAsset.Symbol;
                logModel.TokenIcon = token.BaseAsset.Icon;
            }
            else
            {
                //请求获取 todo:待定
                logModel.TokenName = "-";
                logModel.TokenSymbol = "-";
                logModel.TokenIcon = "-";
            }

            logModel.Id = await _userAllTraderLogRepository.CopyNew().AsInsertable(logModel)
                .ExecuteReturnIdentityAsync();


            var atuae = new AllTradeUpdateAmountEvent(logModel.Id, sign, sw.Mint, sw.DexType, true, "跟单",
                logModel.FollowWalletAddress, 0);
            var (isSuccessful, solBuyAmount, tokenBAmount) =
                await _allTradesSolAmountTokenAmount.UpdateAllTrades_SolAmount_TokenAmount(atuae);
            if (!isSuccessful)
            {
                Logger.LogDebug($"跟单任务{sw.Id} 卖出 {sw.Mint} 获取交易记录 无法解析/交易失败 ");
                return;
            }
            
            await _nats.PublishAsync(ct.UId.ToString(), new NatsSolanaTradeMsg()
            {
                TraderType = (int)TraderType.Trader_02,
                TradeTypeMark = "跟单任务",
                Successful = successful,
                Sign = sign,
                Errormsg = errormsg,
                Data = new NatsSolanaTradeMsgData()
                {

                    Mint = sw.Mint,
                    IsBuy =false,
                    SolAmount = 0,
                    TotalAmount = 0,
                }
            });
            
            //百分百 卖出 时 标识所有未完成子任务为 失效 状态
            if (sw.Percentage == 100)
            {
                var dict = new Dictionary<string, string>()
                {
                    ["uid"] = ct.UId.ToString(),
                    ["wid"] = ct.WalletId.ToString(),
                    ["mint"] = sw.Mint,
                    ["publickey"] = ct.PubKey,
                    ["poolid"] = sw.PoolId
                };
                //发送消息
                await _redisClient.XAddAsync(StreamKey.RemoveTokenInventoryStream, dict);
            }

            //统计 代币 盈亏
            await _eventPublisher.PublishAsync(EventConsts.CopyTradeLossDataAnalysisAddUpLog,
                new CopyTradeLossDataAnalysisEvent(ct.UId, ct.Id, sw.Mint, false,
                    account.PublicKey, sw.Signer, solBuyAmount, sw.Percentage == 100)
            );
            //更新 最后操作
            await _eventPublisher.PublishAsync(EventConsts.DBUpdata_UserCopyTrade_Operation,
                new UpdataUserCopyTradeOperationEventData(ct.Id, operation));

            //更新 买入统计
            await _eventPublisher.PublishAsync(EventConsts.DBUpdata_UserCopyTrade_Statistical,
                new UpdataUserCopyTradeStatisticalEventData(ct.Id, sw.IsBuy, 0, solAmount));
        }
    }

    #endregion

    /// <summary>
    /// 跟单 交易 消息
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(EventConsts.CopyTradeBuySellEvent)]
    public async Task BuySell(EventHandlerExecutingContext context)
    {
        Logger.LogDebug($"收到 yellowstone-grpc 跟单 消息");
        var sw = (Dto.CopyTradeBuySellEvent)context.Source.Payload;
        await BuySellAction(sw: sw);
    }

    /// <summary>
    /// 跟单 延时购买消息
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(EventConsts.CopyTradeDelayBuyEvent)]
    public async Task DelayBuy(EventHandlerExecutingContext context)
    {
        var sw = (Dto.CopyTradeDelayBuyEvent)context.Source.Payload;
        Logger.LogDebug($"收到 跟单 延时购买 消息{JsonSerializer.Serialize(sw)}");

        if (await _redisClient.HExistsAsync(RedisKey.CopyTradeDelayTaskBuying, $"{sw.Id}:{sw.Owner}:{sw.Mint}"))
        {
            Logger.LogDebug($"跟单 延时购买 {sw.Id}=>{sw.Mint} 正在交易 返回");
            return;
        }

        // 正在交易标识
        await _redisClient.HSetAsync(RedisKey.CopyTradeDelayTaskBuying, $"{sw.Id}:{sw.Owner}:{sw.Mint}", "1");
        var ct = await _userCopyTradeRepository.AsQueryable().Where(it => it.Id == sw.Id).FirstAsync();
        var model = ct.BuySettings;
        var gas = 0m;
        var mev = 0m;
        switch (model.TradeModel)
        {
            case 1:
                gas = model.Gas ?? 0.000005m;
                mev = 0;
                break;
            case 2:
                gas = 0;
                mev = model.Mev ?? 0;
                break;
            case 3:
                gas = model.Gas ?? 0.000005m;
                mev = model.Mev ?? 0;
                break;
            default:
                break;
        }
        var isMev = model.IsMev;
        var mevType = model.MevType;
        var myWallet = await _userMyWalletRepository.AsQueryable()
            .Where(it => it.UId == ct.UId && it.Id == ct.WalletId).FirstAsync();
        //聪明钱包 解析
        var followWallet = ct.FollowWallet.First(it => it.WalletAddress == sw.Owner);

        //获取代币信息
        var token = await _solanaApi.GetMint(sw.Mint, sw.DexType);

        ////0409 条件买入 结构变更 新增
        //if (model.BuyConditionData != null && model.BuyConditionData.Any())
        //{
        //    var mcap = (token.BaseAsset.Mcap ?? 0) / 1000; //市值区间基础单位用K来表示 1K=1000
        //    var holderCount = token.BaseAsset.HolderCount ?? 0;
        //    var smartWalletBuy = sw.SolAmount;
        //    var isBuyFlag = model.BuyConditionData.All(condition => condition switch
        //    {
        //        { Condition: 1 } => CheckRange(mcap, condition.Min, condition.Max),
        //        { Condition: 2 } => CheckRange(holderCount, condition.Min, condition.Max),
        //        { Condition: 3 } => CheckRange(smartWalletBuy, condition.Min, condition.Max),
        //        _ => true // 未知条件默认通过
        //    });
        //    if (isBuyFlag == false)
        //    {
        //        Logger.LogWarning(
        //            $"跟单任务 延时买入 {sw.Id} {sw.Mint} 条件买入不符合 实际值:{mcap}：{holderCount}：{smartWalletBuy} 设置值:{JsonSerializer.Serialize(model.BuyConditionData)}");
        //        //交易失败 删除标识
        //        await _redisClient.HDelAsync(RedisKey.CopyTradeDelayTaskBuying, $"{sw.Owner}{sw.Mint}");
        //        return;
        //    }
        //}

        var account = Solnet.Wallet.Account.FromSecretKey(ct.SecretKey);
        var tokenPrice = await _solanaApi.GetMintPrice(sw.Mint, sw.DexType, sw.PoolId);
        Logger.LogDebug($"跟单任务 延时买入 发送交易 {sw.Id} =>{sw.Mint}=>{tokenPrice}");

        var (successful, sign, errormsg) = await _solanaApi.Buy(sw.DexType, account, sw.Mint, sw.SolAmount,
            model.Slippage ?? 10m, gas, mev, sw.PoolId, tokenPrice: tokenPrice, isRise: sw.IsRise,isMev:isMev,mevList:mevType);
        Logger.LogDebug($"跟单任务 延时买入 交易返回 {sw.Id} =>{sw.Mint}=>{sign}=>{errormsg}");
        
        //如果用户 余额为0 或者 不足以购买代币 则 取消任务  2025 05 13 1441
        var userBlanece = await _solanaApi.GetBalanceAsync(ct.PubKey);
        if (userBlanece < sw.SolAmount)
        {
            Logger.LogDebug($"跟单任务:{sw.Id} {sw.Mint} 取消 用户 {ct.PubKey} 余额为0");
            //更新任务状态
            await _userCopyTradeRepository.AsUpdateable()
                .SetColumns(it => it.Status == 0)
                .Where(it => it.Id == ct.Id)
                .ExecuteCommandAsync();
            var ids = new string[] { ct.Id.ToString() };
            var address = ct.FollowWallet.Select(it => it.WalletAddress).ToArray();
            //发送停止消息
            var dict = new Dictionary<string, string>()
            {
                //用户ID-》任务 ID=》操作类型=》运行状态
                ["uid"] = ct.UId.ToString(),
                ["type"] = "2",//停止
                ["tid"] = string.Join(",", ids),
                ["address"] = string.Join(",", address),
                ["status"] = "0"
            };
            //发送消息
            await _redisClient.XAddAsync(StreamKey.CopyTraderWsStream, dict);
            Logger.LogDebug($"跟单任务:{sw.Id} {sw.Mint} 取消 用户 {ct.PubKey} 发送取消订阅消息");
            return;
        }
        //交易失败
        if (string.IsNullOrWhiteSpace(sign))
        {
            //交易失败 删除标识
            await _redisClient.HDelAsync(RedisKey.CopyTradeDelayTaskBuying, $"{sw.Id}:{sw.Owner}:{sw.Mint}");
            Logger.LogDebug($"延时 任务{sw.Id} 上链失败 {sw.Mint} 交易失败 {errormsg}");
            await _nats.PublishAsync(ct.UId.ToString(), new NatsSolanaTradeMsg()
            {
                TraderType = (int)TraderType.Trader_02,
                TradeTypeMark = "跟单任务 延时买入",
                Successful = successful,
                Sign = String.Empty,
                Errormsg = errormsg,
                Data = new NatsSolanaTradeMsgData()
                {

                    Mint = sw.Mint,
                    IsBuy =true,
                    SolAmount = 0,
                    TotalAmount = 0,
                }
            });
            return;
        }

        await Task.Delay(500);
        //解析交易
        var (solAmount, tokenAmount) = await _solanaApi.ParserTransaction(sign, sw.Mint, sw.DexType, true);
        //上链交易 写交易记录
        var logModel = new UserAllTraderLog()
        {
            TraderType = TraderType.Trader_02,
            TaskId = sw.Id,
            UId = ct.UId,
            Chain = ct.Chain,
            IsBuy = true,
            SolAmount = solAmount,
            TokenAmount = tokenAmount,
            Gas = gas,
            Mev = mev,
            MyWalletAddress = myWallet.PubKey,
            //MyWalletMark = myWallet.Name,
            FollowWalletAddress = followWallet?.WalletAddress ?? "",
            FollowWalletMark = followWallet?.WalletName ?? "",
            TokenAddress = sw.Mint,
            Signature = sign,
            Status = string.IsNullOrWhiteSpace(sign) ? $"延时买入-失败" : $"延时买入-成功",
            CreateTime = DateTime.Now,
        };
        if (token != null)
        {
            logModel.TokenName = token.BaseAsset.Name;
            logModel.TokenSymbol = token.BaseAsset.Symbol;
            logModel.TokenIcon = token.BaseAsset.Icon;
        }
        else
        {
            //请求获取 todo:待定
            logModel.TokenName = "-";
            logModel.TokenSymbol = "-";
            logModel.TokenIcon = "-";
        }

        logModel.Id = await _userAllTraderLogRepository.CopyNew().AsInsertable(logModel)
            .ExecuteReturnIdentityAsync();


        ////交易成功 标识完成  删除标识
        //await _redisClient.HSetAsync(RedisKey.CopyTradeDelayBuyTokenSell, $"{sw.Owner}:{sw.Mint}", "2");
        ////删除 正在购买标识
        //await _redisClient.HDelAsync(RedisKey.CopyTradeDelayTaskBuying, $"{sw.Owner}{sw.Mint}");
        var atuae = new AllTradeUpdateAmountEvent(logModel.Id, sign, sw.Mint, sw.DexType, true, "延时买入",
            logModel.FollowWalletAddress, 0);
        var (isSuccessful, solBuyAmount, tokenBAmount) =
            await _allTradesSolAmountTokenAmount.UpdateAllTrades_SolAmount_TokenAmount(atuae);
        if (!isSuccessful)
        {
            Logger.LogDebug($"跟单任务{sw.Id} 延时买入 {sw.Mint} 获取交易记录 无法解析/交易失败 ");
            return;
        }
        await _nats.PublishAsync(ct.UId.ToString(), new NatsSolanaTradeMsg()
        {
            TraderType = (int)TraderType.Trader_02,
            TradeTypeMark = "跟单任务 延时买入",
            Successful = successful,
            Sign = sign,
            Errormsg = errormsg,
            Data = new NatsSolanaTradeMsgData()
            {

                Mint = sw.Mint,
                IsBuy =true,
                SolAmount = solBuyAmount,
                TotalAmount = tokenBAmount,
            }
        });
        //存 redis 只买一次
        await SetSameTokenOnceBuy(ct.UId, ct.Id, sw.Mint);
        //存 redis 持仓
        await SetTokenInventory(ct.UId, ct.PubKey, sw.Mint);
        var operation = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss}|延时买入";
        //更新 最后操作
        await _eventPublisher.PublishAsync(EventConsts.DBUpdata_UserCopyTrade_Operation,
            new UpdataUserCopyTradeOperationEventData(ct.Id, operation));
        //更新 买入统计
        await _eventPublisher.PublishAsync(EventConsts.DBUpdata_UserCopyTrade_Statistical,
            new UpdataUserCopyTradeStatisticalEventData(ct.Id, true, 0, sw.SolAmount)
        );
        //更新 代币持有
        await _eventPublisher.PublishAsync(EventConsts.DBUpdata_UserMyWallet_Held,
            new UpdataUserMyWalletHeldEventData(ct.WalletId));

        //统计 代币 盈亏
        await _eventPublisher.PublishAsync(EventConsts.CopyTradeLossDataAnalysisAddUpLog,
            new CopyTradeLossDataAnalysisEvent(ct.UId, ct.Id, sw.Mint, true,
                account.PublicKey, sw.Owner, solBuyAmount, false)
        );

        //建立 止盈止损 子任务 
        if (ct.SellSettings.SellModel == 3 || ct.SellSettings.SellModel == 1)
        {
            //建立子任务 发送消息
            await CreateStopLimit(sw.Mint, sw.DexType, ct, logModel, sw.PoolId);
        }
    }

    /// <summary>
    /// 自动/移动 止盈止损 卖出 
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(EventConsts.CopyTradeSubTaskSellEvent)]
    public async Task SubTaskSell(EventHandlerExecutingContext context)
    {
        var sw = (Dto.CopyTradeSubTaskSellItem)context.Source.Payload;
        UserCopyTradeTask? tradeTask = null;
        var logId = sw.LogId; // 跟单 钱包买入记录ID
        var operation = $"";
        var status = "";
        // 获取当前时间，避免重复调用
        string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        //全部卖出
        if (sw.TaskId == 0)
        {
            //更新所有子任务状态 只取一条
            tradeTask = await _userCopyTradeTaskRepository.AsQueryable()
                .Where(it => it.LogId == sw.LogId && it.TokenAddress == sw.Mint)
                .FirstAsync();
            operation = $"{timestamp}|止盈止损过期";
        }
        else if (sw.TaskId > 0)
        {
            //处理单条卖出
            tradeTask = await _userCopyTradeTaskRepository.AsQueryable()
                .Where(it => it.Id == sw.TaskId)
                .FirstAsync();
        }


        if (tradeTask == null) return;
        logId = tradeTask.LogId;
        if (string.IsNullOrWhiteSpace(operation))
        {
            operation = tradeTask.StopLimitMode switch
            {
                2 => $"{timestamp}|追踪止盈止损",
                1 => tradeTask.AutoMode switch
                {
                    1 => $"{timestamp}|自动止盈",
                    2 => $"{timestamp}|自动止损",
                },
            };
        }

        var statusmark = tradeTask.StopLimitMode switch
        {
            2 => $"追踪止盈止损",
            1 => tradeTask.AutoMode switch
            {
                1 => $"自动止盈",
                2 => $"自动止损",
            },
        };

        /*
         *var gas = model.TradeModel == 1 ? model.Gas==null?0:model.Gas.Value : 0m;
            var mev = model.TradeModel == 2 ? model.Mev==null?0:model.Mev.Value : 0m;
         *
         */
        //var gas = tradeTask.TradeMode == 1 ? tradeTask.Gas == null ? 0 : tradeTask.Gas.Value : 0m;
        //var mev = tradeTask.TradeMode == 2 ? tradeTask.Mev == null ? 0 : tradeTask.Mev.Value : 0m;
        var gas = 0m;
        var mev = 0m;
        switch (tradeTask.TradeMode)
        {
            case 1:
                gas = tradeTask.Gas ?? 0.000005m;
                mev = 0;
                break;
            case 2:
                gas = 0;
                mev = tradeTask.Mev ?? 0;
                break;
            case 3:
                gas = tradeTask.Gas ?? 0.000005m;
                mev = tradeTask.Mev ?? 0;
                break;
            default:
                break;
        }
        var isMev = tradeTask.IsMev;
        var mevType = tradeTask.MevType;
        var myWallet = await _userMyWalletRepository.AsQueryable()
            .Where(it => it.UId == tradeTask.UId && it.Id == tradeTask.WalletId).FirstAsync();

        var account = Solnet.Wallet.Account.FromSecretKey(tradeTask.SecretKey);
        var (successful, sign, errormsg) = await _solanaApi.Sell(sw.DexType, account, tradeTask.TokenAddress,
            sw.Percentage, gas, mev, tradeTask.Slippage, sw.PoolId,isMev:isMev,mevList:mevType);
        if (string.IsNullOrWhiteSpace(sign))
        {
            var logModelLog = new UserAllTraderLog()
            {
                TraderType = TraderType.Trader_02,
                TaskId = tradeTask.CtId, //ctid 跟单任务  ID
                SubTaskId = sw.TaskId,
                UId = tradeTask.UId,
                Chain = tradeTask.Chain,
                IsBuy = false,
                SolAmount = 0,
                TokenAmount = 0,
                Gas = gas,
                Mev = mev,
                MyWalletAddress = myWallet.PubKey,
                //MyWalletMark = myWallet.Name,
                FollowWalletAddress = tradeTask.FollowWalletAddress,
                FollowWalletMark = tradeTask.FollowWalletMark,
                TokenAddress = tradeTask.TokenAddress,
                TokenName = tradeTask.TokenName,
                TokenSymbol = tradeTask.TokenSymbol,
                TokenIcon = tradeTask.TokenIcon,
                Signature = sign,
                Status = string.IsNullOrWhiteSpace(sign) ? $"{statusmark}-失败" : $"{statusmark}-成功",
                CreateTime = DateTime.Now,
                CompleteTime = DateTime.Now
            };
            logModelLog.Id = await _userAllTraderLogRepository.CopyNew().AsInsertable(logModelLog)
                .ExecuteReturnIdentityAsync();
            Logger.LogDebug($"上链失败{sw.TaskId} {sw.Mint} {errormsg}");
            
            await _userCopyTradeTaskRepository.CopyNew().AsUpdateable()
                //.SetColumns(it => it.Signature == sign)
                .SetColumns(it => it.Status == "completed")
                .SetColumns(it => it.CompleteTime == DateTime.Now)
                .Where(it => it.Id == sw.TaskId)
                .ExecuteCommandAsync();
            //发送消息
            await _nats.PublishAsync(tradeTask.UId.ToString(), new NatsSolanaTradeMsg()
            {
                TraderType = (int)TraderType.Trader_02,
                TradeTypeMark = statusmark,
                Successful = successful,
                Sign = sign,
                Errormsg = errormsg,
                Data = new NatsSolanaTradeMsgData()
                {

                    Mint = sw.Mint,
                    IsBuy =false,
                    SolAmount = 0,
                    TotalAmount = 0,
                }
            });
            return;
        }

        //百分百 卖出 时 标识所有未完成子任务为 失效 状态
        if (sw.Percentage == 100)
        {
            var dict = new Dictionary<string, string>()
            {
                ["uid"] = tradeTask.UId.ToString(),
                ["wid"] = tradeTask.WalletId.ToString(),
                ["mint"] = tradeTask.TokenAddress,
                ["publickey"] = tradeTask.PubKey,
            };
            //发送消息
            await _redisClient.XAddAsync(StreamKey.RemoveTokenInventoryStream, dict);
        }

        //更新 任务 记录 交易hash
        await _userCopyTradeTaskRepository.CopyNew().AsUpdateable()
            .SetColumns(it => it.Signature == sign)
            .SetColumns(it => it.Status == "completed")
            .SetColumns(it => it.CompleteTime == DateTime.Now)
            .Where(it => it.Id == sw.TaskId)
            .ExecuteCommandAsync();
        await Task.Delay(500);
        //解析交易
        var (solAmount, tokenAmount) =
            await _solanaApi.ParserTransaction(sign, tradeTask.TokenAddress, sw.DexType, false);
        //上链交易 写交易记录
        var logModel = new UserAllTraderLog()
        {
            TraderType = TraderType.Trader_02,
            TaskId = tradeTask.CtId,
            SubTaskId = sw.TaskId,
            UId = tradeTask.UId,
            Chain = tradeTask.Chain,
            IsBuy = false,
            SolAmount = solAmount,
            TokenAmount = tokenAmount,
            Gas = gas,
            MyWalletAddress = myWallet.PubKey,
            FollowWalletAddress = tradeTask.FollowWalletAddress,
            FollowWalletMark = tradeTask.FollowWalletMark,
            TokenAddress = tradeTask.TokenAddress,
            TokenName = tradeTask.TokenName,
            TokenSymbol = tradeTask.TokenSymbol,
            TokenIcon = tradeTask.TokenIcon,
            Signature = sign,
            Status = string.IsNullOrWhiteSpace(sign) ? $"{statusmark}-失败" : $"{statusmark}-成功",
            CreateTime = DateTime.Now,
            CompleteTime = DateTime.Now
        };
        logModel.Id = await _userAllTraderLogRepository.CopyNew().AsInsertable(logModel)
            .ExecuteReturnIdentityAsync();
        var atuae = new AllTradeUpdateAmountEvent(logModel.Id, sign, sw.Mint, sw.DexType, true, statusmark,
            logModel.FollowWalletAddress, 0);
        var (isSuccessful, solBuyAmount, tokenBAmount) =
            await _allTradesSolAmountTokenAmount.UpdateAllTrades_SolAmount_TokenAmount(atuae);
        if (!isSuccessful)
        {
            Logger.LogDebug($"跟单任务{sw.LogId} {statusmark} {sw.Mint} 获取交易记录 无法解析/交易失败 ");
            return;
        }
        await _nats.PublishAsync(tradeTask.UId.ToString(), new NatsSolanaTradeMsg()
        {
            TraderType = (int)TraderType.Trader_02,
            TradeTypeMark = statusmark,
            Successful = successful,
            Sign = sign,
            Errormsg = errormsg,
            Data = new NatsSolanaTradeMsgData()
            {

                Mint = sw.Mint,
                IsBuy =false,
                SolAmount = solBuyAmount,
                TotalAmount = tokenBAmount,
            }
        });
        //统计 代币 盈亏
        await _eventPublisher.PublishAsync(EventConsts.CopyTradeLossDataAnalysisAddUpLog,
            new CopyTradeLossDataAnalysisEvent(tradeTask.UId, tradeTask.CtId, sw.Mint, false,
                account.PublicKey, tradeTask.FollowWalletAddress, solBuyAmount, sw.Percentage == 100)
        );
        //更新 最后操作
        await _eventPublisher.PublishAsync(EventConsts.DBUpdata_UserCopyTrade_Operation,
            new UpdataUserCopyTradeOperationEventData(tradeTask.CtId, operation));

        //更新 买出统计
        await _eventPublisher.PublishAsync(EventConsts.DBUpdata_UserCopyTrade_Statistical,
            new UpdataUserCopyTradeStatisticalEventData(tradeTask.CtId, false, 0, solAmount));
    }
}