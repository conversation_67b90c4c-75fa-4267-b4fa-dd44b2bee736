using Tron.Abp.Multiplex.Solana;

namespace Tran.Abp.MonitorServer.EventHandlers;

public class QuickTradeEventSubscriber : IEventSubscriber, ISingletonDependency
{
    private IRedisClient _redisClient;
    private ISolanaApi _solanaApi;
    public ILogger<QuickTradeEventSubscriber> Logger { get; set; }
    private readonly SqlSugarRepository<UserQuickTradeTask> _userQuickTradeTaskRepository;
    private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;
    private readonly SqlSugarRepository<UserMyWallet> _userMyWalletRepository;
    private IEventPublisher _publisher;
    public QuickTradeEventSubscriber(IRedisClient redisClient, ISolanaApi solanaApi,
        SqlSugarRepository<UserQuickTradeTask> userQuickTradeTaskRepository,
        SqlSugarRepository<UserAllTraderLog> userQuickTradeLogRepository, IEventPublisher publisher, SqlSugarRepository<UserMyWallet> userMyWalletRepository)
    {
        _redisClient = redisClient;
        _solanaApi = solanaApi;
        _userQuickTradeTaskRepository = userQuickTradeTaskRepository;
        _userAllTraderLogRepository = userQuickTradeLogRepository;
        _publisher = publisher;
        _userMyWalletRepository = userMyWalletRepository;
        Logger = NullLogger<QuickTradeEventSubscriber>.Instance;
    }


    [EventSubscribe(EventConsts.QuickTradeSubTaskSellEvent)]
    public async Task QuickTradeSubTaskSell(EventHandlerExecutingContext context)
    {
        var sw = (Dto.QuickTradeSubTaskSellItem)context.Source.Payload;
        var logId = sw.LogId;
        var operation = $"";
        // 获取当前时间，避免重复调用
        string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        UserQuickTradeTask? tradeTask = null;
        var status = "";
        //全部卖出
        if (sw.TaskId == 0)
        {
            //更新所有子任务状态
            tradeTask = await _userQuickTradeTaskRepository.AsQueryable().WithCache()
                .Where(it => it.LogId == sw.LogId)
                .FirstAsync();
            operation = $"止盈止损过期";
            status = "canceled";
        }
        else if (sw.TaskId > 0)
        {
            //处理单条卖出
            tradeTask = await _userQuickTradeTaskRepository.AsQueryable().WithCache()
                .Where(it => it.Id == sw.TaskId)
                .FirstAsync();
            status = "completed";
        }

        if (tradeTask == null) return;
        if (string.IsNullOrWhiteSpace(operation))
        {
            operation = tradeTask.StopLimitMode switch
            {
                2 => $"移动止盈止损",
                1 => tradeTask.AutoMode switch
                {
                    1 => $"自动止盈",
                    2 => $"自动止损",
                },
            };
        }

        var myWallet = await _userMyWalletRepository.AsQueryable()
            .Where(it => it.UId == tradeTask.UId && it.Id == tradeTask.WalletId).FirstAsync();
        var gas = 0m;
        var mev = 0m;
        switch (tradeTask.TradeMode)
        {
            case 1:
                gas = tradeTask.Gas ?? 0;
                mev = 0;
                break;
            case 2:
                gas = 0;
                mev = tradeTask.Mev ?? 0;
                break;
            case 3:
                gas = tradeTask.Gas ?? 0;
                mev = tradeTask.Mev ?? 0;
                break;
            default:
                gas = 0;
                mev = 0;
                break;
        }

        var account = Solnet.Wallet.Account.FromSecretKey(tradeTask.SecretKey);
        var (successful, sign, errormsg) = await _solanaApi.Sell(sw.DexType, account, tradeTask.TokenAddress, sw.Percentage, gas, mev,
            tradeTask.Slippage, sw.PoolId);
        Logger.LogDebug($"快速交易 子任务 卖出 {tradeTask.TokenAddress} {sw.DexType} 百分比:{sw.Percentage} 记录ID{logId} 任务ID{sw.TaskId} =>{sign}");

        //await Task.Delay(500);
        //解析交易
        //var (solAmount, tokenAmount) = await _solanaApi.ParserTransaction(sign, tradeTask.TokenAddress, sw.IsPump, false);
        if (string.IsNullOrWhiteSpace(sign))
        {
            //更新
            await _userQuickTradeTaskRepository.CopyNew().AsUpdateable()
                //.SetColumns(it => it.Signature == sign)
                .SetColumns(it => it.Status == status)
                .SetColumns(it => it.CompleteTime == DateTime.Now)
                .Where(it => it.LogId == logId)
                .WhereIF(sw.TaskId > 0, it => it.Id == sw.TaskId)
                .ExecuteCommandAsync();
            Logger.LogDebug($"快速交易 子任务 卖出 {tradeTask.TokenAddress} 上链失败 {errormsg}");
            return;
        }
        //更新
        await _userQuickTradeTaskRepository.CopyNew().AsUpdateable()
            .SetColumns(it => it.Signature == sign)
            .SetColumns(it => it.Status == status)
            .SetColumns(it => it.CompleteTime == DateTime.Now)
            .Where(it => it.LogId == logId)
            .WhereIF(sw.TaskId > 0, it => it.Id == sw.TaskId)
            .ExecuteCommandAsync();



        /*await Task.Delay(500);
        //解析交易
        var (solAmount, tokenAmount) = await _solanaApi.ParserTransaction(sign, tradeTask.TokenAddress, sw.IsPump, false);*/
        //插入日志 
        //写log
        var logModel = new UserAllTraderLog()
        {
            TraderType = TraderType.Trader_01,
            UId = tradeTask.UId,
            Chain = tradeTask?.Chain ?? "solana",
            TokenName = tradeTask?.TokenName ?? "",
            TokenSymbol = tradeTask?.TokenSymbol ?? "",
            TokenAddress = tradeTask.TokenAddress,
            TokenIcon = tradeTask?.TokenIcon ?? "",
            IsBuy = false,
            SolAmount = 0,
            TokenAmount = 0,
            Gas = gas,
            Mev = mev,
            MyWalletAddress = myWallet.PubKey,
            //MyWalletMark = myWallet.Name,
            Status = $"{operation}-成功",
            CreateTime = DateTime.Now,
            CompleteTime = DateTime.Now,
            Signature = sign
        };
        logModel.Id = await _userAllTraderLogRepository.CopyNew().AsInsertable(logModel).ExecuteReturnIdentityAsync();
        //更新  Amount = 0,  TokenAmount = 0,
        await _publisher.PublishAsync(EventConsts.DBUpdata_AllTrades_SolAmount_TokenAmount,
            new AllTradeUpdateAmountEvent(logModel.Id, sign, tradeTask.TokenAddress, sw.DexType,
                false, $"快速交易 {operation}", "", sw.Percentage));

    }
}