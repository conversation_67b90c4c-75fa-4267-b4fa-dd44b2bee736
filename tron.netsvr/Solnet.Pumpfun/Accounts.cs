using Solnet.Programs.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Solnet.Wallet;

namespace Solnet.Pumpfun
{
    public class Accounts
    {
        public partial class BondingCurve
        {

            public ulong virtualTokenReserves;
            public ulong virtualSolReserves;
            public ulong realTokenReserves;
            public ulong realSolReserves;
            public ulong tokenTokenSupply;
            public bool complete;
            public PublicKey Creater { get; set; }
            public static BondingCurve Deserialize(ReadOnlySpan<byte> _data)
            {
                int offset = 8;
                BondingCurve result = new BondingCurve();
                result.virtualTokenReserves = _data.GetU64(offset);
                offset += 8;
                result.virtualSolReserves = _data.GetU64(offset);
                offset += 8;
                result.realTokenReserves = _data.GetU64(offset);
                offset += 8;
                result.realSolReserves = _data.GetU64(offset);
                offset += 8;
                result.tokenTokenSupply = _data.GetU64(offset);
                offset += 8;
                result.complete = _data.GetBool(offset);
                offset += 1;
                result.Creater=_data.GetPubKey(offset);
                return result;
            }
        }
        
        public partial class GlobalBondingCurve
        {
            public ulong discriminator { get; set; }
            
            public bool initialized { get; set; }
            public PublicKey authority { get; set; }
            public PublicKey feeRecipient { get; set; }
            public ulong initialVirtualTokenReserves{ get; set; }
            public ulong initialVirtualSolReserves{ get; set; }
            public ulong initialRealTokenReserves{ get; set; }
            public ulong tokenTotalSupply{ get; set; }
            public ulong feeBasisPoints{ get; set; }
            public static GlobalBondingCurve Deserialize(ReadOnlySpan<byte> _data)
            {
                int offset = 0;
                GlobalBondingCurve result=new GlobalBondingCurve();
                result.discriminator = _data.GetU64(offset);
                offset += 8;
                result.initialized = _data.GetBool(offset);
                offset += 1;
                result.authority = _data.GetPubKey(offset);
                offset += 32;
                result.feeRecipient = _data.GetPubKey(offset);
                offset += 32;
                result.initialVirtualTokenReserves = _data.GetU64(offset);
                offset += 8;
                result.initialVirtualSolReserves = _data.GetU64(offset);
                offset += 8;
                result.initialRealTokenReserves = _data.GetU64(offset);
                offset += 8;
                result.tokenTotalSupply = _data.GetU64(offset);
                offset += 8;
                result.feeBasisPoints = _data.GetU64(offset);
                //offset += 8;
                
                return result;
            }
        }

        
    }
}
