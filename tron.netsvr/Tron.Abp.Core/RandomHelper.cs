
using System.Security.Cryptography;
using System.Text;

namespace Tron.Abp.Core;
/// <summary>随机数</summary>
public class RandomHelper
{
    /// <summary>生成随机纯字母随机数</summary>
    /// <param name="Length">生成长度</param>
    /// <returns></returns>
    public static string CreateLetter(int Length)
    {
        char[] chArray = new char[52]
        {
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
        'a',
        'b',
        'c',
        'd',
        'e',
        'f',
        'g',
        'h',
        'i',
        'j',
        'k',
        'l',
        'm',
        'n',
        'o',
        'p',
        'q',
        'r',
        's',
        't',
        'u',
        'v',
        'w',
        'x',
        'y',
        'z'
        };
        string letter = "";
        int length = chArray.Length;
        Random random = new Random(~(int)DateTime.Now.Ticks);
        for (int index1 = 0; index1 < Length; ++index1)
        {
            int index2 = random.Next(0, length);
            letter += chArray[index2].ToString();
        }
        return letter;
    }

    /// <summary>生成随机字母和数字随机数</summary>
    /// <param name="Length">生成长度</param>
    /// <returns></returns>
    public static string CreateLetterAndNumber(int Length)
    {
        char[] chArray = new char[62]
        {
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
        'a',
        'b',
        'c',
        'd',
        'e',
        'f',
        'g',
        'h',
        'i',
        'j',
        'k',
        'l',
        'm',
        'n',
        'o',
        'p',
        'q',
        'r',
        's',
        't',
        'u',
        'v',
        'w',
        'x',
        'y',
        'z',
        '0',
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9'
        };
        string letterAndNumber = "";
        int length = chArray.Length;
        Random random = new Random(~(int)DateTime.Now.Ticks);
        for (int index1 = 0; index1 < Length; ++index1)
        {
            int index2 = random.Next(0, length);
            letterAndNumber += chArray[index2].ToString();
        }
        return letterAndNumber;
    }

    /// <summary>生成随机小写字母和数字随机数</summary>
    /// <param name="Length">生成长度</param>
    /// <returns></returns>
    public static string CreateLetterAndNumberLower(int Length)
    {
        char[] chArray = new char[36]
        {
        'a',
        'b',
        'c',
        'd',
        'e',
        'f',
        'g',
        'h',
        'i',
        'j',
        'k',
        'l',
        'm',
        'n',
        'o',
        'p',
        'q',
        'r',
        's',
        't',
        'u',
        'v',
        'w',
        'x',
        'y',
        'z',
        '0',
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9'
        };
        string letterAndNumberLower = "";
        int length = chArray.Length;
        Random random = new Random(~(int)DateTime.Now.Ticks);
        for (int index1 = 0; index1 < Length; ++index1)
        {
            int index2 = random.Next(0, length);
            letterAndNumberLower += chArray[index2].ToString();
        }
        return letterAndNumberLower;
    }

    /// <summary>生成随机字符串</summary>
    /// <param name="length">字符串的长度</param>
    /// <returns></returns>
    public static string CreateRandomString(int length)
    {
        StringBuilder stringBuilder = new StringBuilder();
        for (int index = 0; index < length; ++index)
        {
            switch (new Random(Guid.NewGuid().GetHashCode()).Next(3))
            {
                case 0:
                    stringBuilder.Append(CreateNum());
                    break;
                case 1:
                    stringBuilder.Append(CreateSmallAbc());
                    break;
                case 2:
                    stringBuilder.Append(CreateBigAbc());
                    break;
            }
        }
        return stringBuilder.ToString();
    }

    /// <summary>生成单个随机数字</summary>
    public static int CreateNum() => new Random(Guid.NewGuid().GetHashCode()).Next(10);

    /// <summary>生成指定长度的随机数字字符串</summary>
    /// <param name="length"></param>
    /// <returns></returns>
    public static string CreateNum(int length = 1)
    {
        Random random = new Random(Guid.NewGuid().GetHashCode());
        string num = "";
        for (int index = 0; index < length; ++index)
            num += random.Next(10).ToString();
        return num;
    }

    /// <summary>生成单个大写随机字母</summary>
    public static string CreateBigAbc() => Convert.ToChar(new Random(Guid.NewGuid().GetHashCode()).Next(65, 91)).ToString();

    /// <summary>生成单个小写随机字母</summary>
    public static string CreateSmallAbc() => Convert.ToChar(new Random(Guid.NewGuid().GetHashCode()).Next(97, 123)).ToString();

    public static T RandomEnumValue<T>()
    {
        var v = Enum.GetValues(typeof(T));
        return (T)v.GetValue(new Random(Guid.NewGuid().GetHashCode()).Next(v.Length));
    }

    /// <summary>
    /// 生成一个新的字符串
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static string CreateStr(string str = "")
    {
        long timeStamp = new DateTimeOffset(DateTime.Now.ToUniversalTime()).ToUnixTimeSeconds();
        if (string.IsNullOrWhiteSpace(timeStamp.ToString())) str = timeStamp.ToString();
        string[] chars = new string[]{
            "a","b","c","d","e","f","g","h",
            "i","j","k","l","m","n","o","p",
            "q","r","s","t","u","v","w","x",
            "y","z","0","1","2","3","4","5",
            "6","7","8","9","A","B","C","D",
            "E","F","G","H","I","J","K","L",
            "M","N","O","P","Q","R","S","T",
            "U","V","W","X","Y","Z"
        };

        string key = "www.QQ.com";
        string hex = "";
        using (var md5 = MD5.Create())
        {
            var data = md5.ComputeHash(Encoding.UTF8.GetBytes($"{key}{str}"));
            StringBuilder builder = new StringBuilder();
            // 循环遍历哈希数据的每一个字节并格式化为十六进制字符串 
            for (int i = 0; i < data.Length; i++)
            {
                builder.Append(data[i].ToString("X2"));
            }
            hex = builder.ToString();
        }
        string[] resUrl = new string[4];
        for (int i = 0; i < 4; i++)
        {
            //把加密字符按照8位一组16进制与0x3FFFFFFF进行位与运算 
            int hexint = 0x3FFFFFFF & Convert.ToInt32("0x" + hex.Substring(i * 8, 8), 16);
            string outChars = string.Empty;
            for (int j = 0; j < 6; j++)
            {
                //把得到的值与0x0000003D进行位与运算，取得字符数组chars索引 
                int index = 0x0000003D & hexint;
                //把取得的字符相加 
                outChars += chars[index];
                //每次循环按位右移5位 
                hexint = hexint >> 5;
            }
            //把字符串存入对应索引的输出数组 
            resUrl[i] = outChars;

        }
        return resUrl[0];

    }
    /// <summary>
    /// 随机 小数
    /// </summary>
    /// <param name="minValue"></param>
    /// <param name="maxValue"></param>
    /// <returns></returns>
    public static decimal GetRandomDouble(double minValue, double maxValue)
    {
        using (var rng = RandomNumberGenerator.Create())
        {
            byte[] buffer = new byte[4];
            rng.GetBytes(buffer);
            uint randomUInt = BitConverter.ToUInt32(buffer, 0);
            double randomFactor = randomUInt / (double)uint.MaxValue; // 转换到 [0,1)
            var result = minValue + (maxValue - minValue) * randomFactor;

            return Convert.ToDecimal(Math.Truncate(result * 1000) / 1000);
        }
    }
    /*public static decimal GenerateRandomDecimalInRange(decimal min, decimal max)
    {
        using (var rng = RandomNumberGenerator.Create())
        {
            byte[] buffer = new byte[4];
            rng.GetBytes(buffer);
            uint randomUInt = BitConverter.ToUInt32(buffer, 0);
            double randomFactor = randomUInt / (double)uint.MaxValue; // [0,1)
            var result = (double)min + ((double)max - (double)min) * randomFactor;
            return Convert.ToDecimal(Math.Truncate(result * 1000) / 1000); // 保留3位小数
        }
    }*/
    /// <summary>
    /// 
    /// </summary>
    /// <param name="minValue"></param>
    /// <param name="maxValue"></param>
    /// <returns></returns>
    public static int GetRandom(int minValue, int maxValue)
    {
        using (var rng = RandomNumberGenerator.Create())
        {
            byte[] buffer = new byte[4];
            rng.GetBytes(buffer);
            // 将字节转换为一个无符号整数
            uint randomUInt = BitConverter.ToUInt32(buffer, 0);

            // 通过模运算确保随机数在 minValue 和 maxValue 之间
            int result = (int)(minValue + (maxValue - minValue + 1) * (randomUInt / (double)uint.MaxValue));

            return result;
        }
    }
    
    
   public static decimal GenerateRandomDecimalInRange(decimal min, decimal max, int num = 1000)
    {
        using (var rng = RandomNumberGenerator.Create())
        {
            byte[] buffer = new byte[4];
            rng.GetBytes(buffer);
            uint randomUInt = BitConverter.ToUInt32(buffer, 0);
            double randomFactor = randomUInt / ((double)uint.MaxValue + 1); // [0,1)
            var result = (double)min + ((double)max - (double)min) * randomFactor;
            var scaled = result * num;
            // 如果 scaled 接近 max * num，则直接返回 max
            if (scaled >= ((double)max * num) - 0.5) return max;
            return Convert.ToDecimal(Math.Truncate(scaled) / num);
        }
    }

    public static int GetDecimalPlaces(decimal number)
    {
        string[] parts = number.ToString(System.Globalization.CultureInfo.InvariantCulture).Split('.');
        return parts.Length == 1 ? 0 : parts[1].TrimEnd('0').Length;
    }
    
}