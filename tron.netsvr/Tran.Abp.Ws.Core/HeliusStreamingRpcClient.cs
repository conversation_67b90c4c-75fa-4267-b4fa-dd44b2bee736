using System.Net.WebSockets;
using Microsoft.Extensions.Logging;
using Solnet.Rpc.Types;
using TouchSocket.Core;
using TouchSocket.Http.WebSockets;
using TouchSocket.Sockets;

namespace Tran.Abp.Ws.Core;

public abstract class HeliusStreamingRpcClient
{
    private TouchSocket.Http.WebSockets.WebSocketClient _client;
    private SemaphoreSlim _sem;
    protected readonly ILogger _logger;
    private bool disposedValue;
    public Uri NodeAddress { get; }
    private string _url;
    public WebSocketState State { get; set; }
    public event EventHandler<WebSocketState> ConnectionStateChangedEvent;
    private ConnectionStats _connectionStats;
    public IConnectionStatistics Statistics => _connectionStats;
    public HeliusStreamingRpcClient(string url, ILogger logger)
    {
        _url = url;
        NodeAddress = new Uri(url);
        _sem = new SemaphoreSlim(1, 1);
        State= WebSocketState.None;
        _connectionStats = new ConnectionStats();
        
        
    }

    private void InitWs()
    {
        _client = new TouchSocket.Http.WebSockets.WebSocketClient();
        _client.Setup(new TouchSocket.Core.TouchSocketConfig()
            .SetRemoteIPHost(_url).ConfigurePlugins(a =>
            {
                //a.UseWebSocketReconnection();
                a.UseWebSocketHeartbeat() //使用心跳插件
                    .SetTick(TimeSpan.FromSeconds(15)); //每5秒ping一次。
            })
        );
        _client.Closing += OnClosing;
        _client.Received += OnClientReceived;
        _client.Closed += OnClosed;
    }

    private async Task OnClosing(IWebSocketClient client, ClosingEventArgs e)
    {
        _logger.LogDebug($"断开连接=>{DateTime.Now}");
    }

    private async Task OnClosed(TouchSocket.Http.WebSockets.IWebSocketClient client, ClosedEventArgs e)
    {
        State = WebSocketState.Closed;
        _logger.LogDebug($"断开连接=>{DateTime.Now}");
        await e.InvokeNext();
    }
    private async Task OnClientReceived(TouchSocket.Http.WebSockets.IWebSocketClient c, WSDataFrameEventArgs e)
    {
        switch (e.DataFrame.Opcode)
        {
            case WSDataType.Cont:
                break;
            case WSDataType.Text:

                var mem = e.DataFrame.PayloadData.Memory;
                HandleNewMessage(mem);
                
                break;
            case WSDataType.Binary:
                break;
            case WSDataType.Close:
                Console.WriteLine($"{DateTime.Now:HH:mm:ss,fff} 远端断开");
                break;
            case WSDataType.Ping:
                Console.WriteLine($"{DateTime.Now:HH:mm:ss,fff} 收到ping");
                break;
            case WSDataType.Pong:
                Console.WriteLine($"{DateTime.Now:HH:mm:ss,fff} 收到pong");
                break;
            default:
                break;
        }

        await e.InvokeNext();
    }
    /// <summary>
    /// Handless a new message payload.
    /// </summary>
    /// <param name="messagePayload">The message payload.</param>
    protected abstract void HandleNewMessage(ReadOnlyMemory<byte> messagePayload);

    /// <summary>
    /// Clean up subscription objects after disconnection.
    /// </summary>
    protected abstract void CleanupSubscriptions();


    public async Task SendAsync(ReadOnlyMemory<byte> message)
    {
        await _client.SendAsync(message);
    }
    public async Task ConnectAsync()
    {
        InitWs();
        _sem.Wait();
        try
        {
            if (State != WebSocketState.Open)
            {
                await _client.ConnectAsync();
                State = WebSocketState.Open;
                ConnectionStateChangedEvent?.Invoke(this, State);

            }
        }
        finally
        {
            _sem.Release();
        }
    }
    public async Task DisconnectAsync()
    {
        _sem.Wait();
        try
        {
            if (State == WebSocketState.Open)
            {
                await _client.CloseAsync();
                _client.Dispose();
                State = WebSocketState.Closed;
                CleanupSubscriptions();
            }
        }
        finally
        {
            _sem.Release();
        }
    }
    protected virtual void Dispose(bool disposing)
    {
        if (!disposedValue)
        {
            if (disposing)
            {
                _client.Dispose();
                _sem.Dispose();
            }
        }
    }
    public void Dispose()
    {
        // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }
}