using Solnet.Rpc.Core.Sockets;

namespace Tran.Abp.Ws.Core;

public class SubscriptionState<T> : SubscriptionState
{
    /// <summary>
    /// The data handler reference.
    /// </summary>
    public Action<SubscriptionState<T>, T> DataHandler;

    /// <summary>
    /// Constructor with all parameters related to a given subscription.
    /// </summary>
    /// <param name="rpcClient">The streaming rpc client reference.</param>
    /// <param name="chan">The channel of this subscription.</param>
    /// <param name="handler">The handler for the data received.</param>
    /// <param name="additionalParameters">Additional parameters for this given subscription.</param>
    public SubscriptionState(HeliusWebSocketStreamingRpcClient rpcClient, SubscriptionChannel chan, Action<SubscriptionState, T> handler, IList<object> additionalParameters = default)
        : base(rpcClient, chan, additionalParameters)
    {
        DataHandler = handler;
    }

    /// <inheritdoc cref="SubscriptionState.HandleData(object)"/>
    public override void HandleData(object data) => DataHandler(this, (T)data);
}