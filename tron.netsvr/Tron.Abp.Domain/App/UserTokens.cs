
//
// Copyright (c) 2021-present zuohuaijun, Daming Co.,Ltd and Contributors
//


using SqlSugar;
using Tron.Abp.Domain.Contracts.Enum;
using Tron.Abp.Domain.Entity;

namespace Tron.Abp.Domain;
/// <summary>
/// 用户持仓
///</summary>
[SugarTable("T_UserTokens", TableDescription = "用户持仓")]
public class UserTokens : BaseId
{
    /// <summary>
    /// 所属用户ID
    /// </summary>
    public int UId { get; set; }
    /// <summary>
    /// 代币名称
    /// </summary>
    public string TokenName { get; set; }
    /// <summary>
    /// 代币缩写
    /// </summary>
    public string TokenSymbol { get; set; }
    /// <summary>
    /// 代币地址
    /// </summary>
    public string TokenAddress { get; set; }
    /// <summary>
    /// 代币Icon
    /// </summary>
    public string TokenIcon { get; set; }
    /// <summary>
    /// 代币余额
    /// </summary>
    [SugarColumn(ColumnDescription = "代币余额", ColumnDataType = "decimal(18,6)", IsNullable = true)]
    public decimal TokenAmount { get; set; }
    /// <summary>
    /// 代币总买入
    /// </summary>
    [SugarColumn(ColumnDescription = "代币总买入", ColumnDataType = "decimal(18,6)", IsNullable = true)]
    public decimal TokenAmountBuy { get; set; }
    /// <summary>
    /// 代币总卖出
    /// </summary>
    [SugarColumn(ColumnDescription = "代币总卖出", ColumnDataType = "decimal(18,6)", IsNullable = true)]
    public decimal TokenAmountSell { get; set; }
    /// <summary>
    /// 所属分组名称
    /// </summary>
    [SugarColumn(ColumnDescription = "所属分组名称", ColumnDataType = "text", IsNullable = true, IsJson = true)]
    public List<string> GName { get; set; }
    /// <summary>
    /// 所属标签名称
    /// </summary>
    [SugarColumn(ColumnDescription = "所属标签名称", ColumnDataType = "text", IsNullable = true, IsJson = true)]
    public List<string> TName { get; set; }
    /// <summary>
    /// 持仓钱包数量
    /// </summary>
    public int TokenWalletCount { get; set; } = 0;
    /// <summary>
    /// 持仓钱包私钥
    /// </summary>
    [SugarColumn(ColumnDescription = "持仓钱包私钥", ColumnDataType = "text", IsNullable = true, IsJson = true)]
    public List<string> WalletSecretKeys { get; set; }
    /// <summary>
    /// 生成时间
    /// </summary>
    public virtual DateTime? CreateTime { get; set; } = DateTime.Now;
}