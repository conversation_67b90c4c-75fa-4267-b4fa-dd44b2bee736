
//
// Copyright (c) 2021-present zuohuaijun, Daming Co.,Ltd and Contributors
//


using SqlSugar;
using System.ComponentModel;
using Tron.Abp.Domain.Contracts.Enum;
using Tron.Abp.Domain.Entity;

namespace Tron.Abp.Domain;
/// <summary>
/// 批量交易主任务表
///</summary>
[SugarTable("T_UserBatchTrade", TableDescription = "批量交易主任务表")]
public class UserBatchTrade : BaseId
{
    /// <summary>
    /// 链
    /// </summary>
    public string Chain { get; set; }

    /// <summary>
    /// 所属用户ID
    /// </summary>
    public int UId { get; set; }

    /// <summary>
    /// 代币名称
    /// </summary>
    public string TokenName { get; set; }
    /// <summary>
    /// 代币缩写
    /// </summary>
    public string TokenSymbol { get; set; }
    /// <summary>
    /// 代币地址
    /// </summary>
    public string TokenAddress { get; set; }
    /// <summary>
    /// 代币Icon
    /// </summary>
    public string TokenIcon { get; set; }

    /// <summary>
    /// 是否购买
    /// </summary>
    public bool IsBuy { get; set; }

    /// <summary>
    /// 钱包分组ID
    /// </summary>
    [SugarColumn(ColumnDescription = "钱包分组ID", ColumnDataType = "text", IsJson = true, IsNullable = true)]
    public List<int> GroupId { get; set; }

    /// <summary>
    /// 钱包ID
    /// </summary>
    [SugarColumn(ColumnDescription = "钱包ID", IsNullable = true)]
    public int WalletId { get; set; }

    /// <summary>
    /// 钱包列表
    /// </summary>
    [SugarColumn(ColumnDescription = "钱包列表", ColumnDataType = "text", IsJson = true, IsNullable = true)]
    public List<WalletModel> Wallets { get; set; }

    /// <summary>
    /// 交易模式1高速，2防夹,3混合模式
    /// </summary>
    public int TradeModel { get; set; }

    /// <summary>
    /// 高速模式费用为空自动费用
    /// </summary>
    public decimal? Gas { get; set; } // 使用 decimal? 以支持 null  
    /// <summary>
    /// 是否开启防夹
    /// </summary>
    public bool IsMev { get; set; } = false;
    /// <summary>
    /// 防夹类型只有在IsMev开启时有效 1:0solt:2:nextblock
    /// </summary>
    [SugarColumn(ColumnDescription = "防夹类型", IsJson = true, IsNullable = true)]
    public List<int> MevType { get; set; } = new List<int>();
    /// <summary>
    /// 防夹模式用为空自动费用
    /// </summary>
    public decimal? Mev { get; set; } // 使用 decimal? 以支持 null  
    /// <summary>
    /// 滑点
    /// </summary>
    public int? Slippage { get; set; }

    /// <summary>
    /// 止盈配置
    /// </summary>
    [SugarColumn(ColumnDescription = "止盈配置", ColumnDataType = "text", IsJson = true, IsNullable = true)]
    public StopLimitModel StoplimitData { get; set; }

    /// <summary>
    ///  止盈止损过期自动卖出 
    /// </summary>
    public bool ExpiredAuto { get; set; }

    /// <summary>
    /// 过期时间（小时）
    /// </summary>
    public int? ExpiredTime { get; set; } = 120;

    /// <summary>
    /// 金额配置信息
    /// </summary>
    [SugarColumn(ColumnDescription = "金额配置信息", ColumnDataType = "text", IsJson = true, IsNullable = true)]
    public AmountModel AmountData { get; set; }

    /// <summary>
    /// 状态 0 已停止，1运行中,2已完成,3失败
    /// </summary>
    public int Status { get; set; } = 1;

    /// <summary>
    /// 添加时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompleteTime { get; set; }

    public string? Mark { get; set; }
}

public class StopLimitModel
{
    /// <summary>
    /// 1自动止盈，2移动止盈
    /// </summary>
    public int LimitModel { get; set; }
    /// <summary>
    /// 自动止盈
    /// </summary>
    public List<RiseSlumpModel>? RiseData { get; set; }
    /// <summary>
    /// 自动止损
    /// </summary>
    public List<RiseSlumpModel>? SlumpData { get; set; }
    /// <summary>
    /// 追踪止盈
    /// </summary>
    public List<MoveDataModel>? MoveData { get; set; }
}

public class RiseSlumpModel
{
    public int? PriceRatio { get; set; }
    public int? SellRatio { get; set; }
}

public class MoveDataModel
{
    /// <summary>
    /// 上涨多少
    /// </summary>
    public int? PriceRatio { get; set; }
    /// <summary>
    /// 回落多少
    /// </summary>
    public int? DeclineRatio { get; set; }
    /// <summary>
    /// 卖出
    /// </summary>
    public int? SellRatio { get; set; }
}

public class AmountModel
{
    /// <summary>
    /// 类型 0 全部金额，1随机金额，2随机百分比，3固定金额
    /// </summary>
    public int Type { get; set; } = 0;

    /// <summary>
    /// 随机金额[最小值,最大值,小数点保留位数]
    /// </summary>
    public List<decimal?> AmountRange { get; set; }

    /// <summary>
    /// 随机百分比[最小值,最大值]
    /// </summary>
    public List<decimal?> PercentRange { get; set; }

    /// <summary>
    /// 固定金额
    /// </summary>
    public decimal? Amount { get; set; }
}

public class WalletModel
{
    /// <summary>
    /// 钱包ID
    /// </summary>
    public int WalletId { get; set; }
    public string? Name { get; set; }
    /// <summary>
    /// 公钥
    /// </summary>
    public string PubKey { get; set; }
    /// <summary>
    /// 私钥
    /// </summary>
    public string SecretKey { get; set; }
}