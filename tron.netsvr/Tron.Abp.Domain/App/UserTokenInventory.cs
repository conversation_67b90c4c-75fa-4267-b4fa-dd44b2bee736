using SqlSugar;
using Tron.Abp.Domain.Entity;

namespace Tron.Abp.Domain;

[SugarTable("T_UserTokenInventory", TableDescription = "用户持仓记录")]
public class UserTokenInventory : BaseId
{
    public int UId { get; set; }
    public string Mint { get; set; }
    public string PublicKey { get; set; }
    public string PrivateKey { get; set; }
    [SugarColumn(ColumnDescription = "代币余额", ColumnDataType = "decimal(18,6)", IsNullable = true)]
    public decimal TokenAmount { get; set; } = 0;
    public bool IsInventory { get; set; } = false;
    public DateTime CreateTime { get; set; } = DateTime.Now;
    public DateTime? UpdateTime { get; set; }
}