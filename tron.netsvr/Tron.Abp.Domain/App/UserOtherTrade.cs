//
// Copyright (c) 2021-present zuohuaijun, Daming Co.,Ltd and Contributors
//


using SqlSugar;
using System.ComponentModel;
using Tron.Abp.Domain.Contracts.Enum;
using Tron.Abp.Domain.Entity;

namespace Tron.Abp.Domain;

/// <summary>
/// 给他人买币
///</summary>
[SugarTable("T_UserOtherTrade", TableDescription = "给他人买币")]
public class UserOtherTrade : BaseId
{
    /// <summary>
    /// 链
    /// </summary>
    public string Chain { get; set; }

    /// <summary>
    /// 所属用户ID
    /// </summary>
    public int UId { get; set; }

    /// <summary>
    /// 代币名称
    /// </summary>
    public string TokenName { get; set; }

    /// <summary>
    /// 代币缩写
    /// </summary>
    public string TokenSymbol { get; set; }

    /// <summary>
    /// 代币地址
    /// </summary>
    public string TokenAddress { get; set; }

    /// <summary>
    /// 代币Icon
    /// </summary>
    public string TokenIcon { get; set; }

    /// <summary>
    /// 钱包分组ID
    /// </summary>
    [SugarColumn(ColumnDescription = "钱包分组ID", ColumnDataType = "text", IsJson = true, IsNullable = true)]
    public List<int> GroupIds { get; set; }

    /// <summary>
    /// 钱包ID
    /// </summary>
    [SugarColumn(ColumnDescription = "钱包ID", IsNullable = true)]
    public int WalletId { get; set; }

    /// <summary>
    /// 钱包列表
    /// </summary>
    [SugarColumn(ColumnDescription = "钱包列表", ColumnDataType = "text", IsJson = true, IsNullable = true)]
    public List<WalletModel> Wallets { get; set; }

    /// <summary>
    /// 给用户列表
    /// </summary>
    [SugarColumn(ColumnDescription = "给用户列表", ColumnDataType = "text", IsJson = true, IsNullable = true)]
    public List<string> ToUsers { get; set; }

    /// <summary>
    /// 高速模式费用为空自动费用
    /// </summary>
    public decimal? Gas { get; set; } // 使用 decimal? 以支持 null  

    /// <summary>
    /// 是否开启防夹
    /// </summary>
    public bool IsMev { get; set; } = false;

    /// <summary>
    /// 防夹类型只有在IsMev开启时有效 1:0solt:2:nextblock
    /// </summary>
    [SugarColumn(ColumnDescription = "防夹类型", IsJson = true, IsNullable = true)]
    public List<int> MevType { get; set; } = new List<int>();

    /// <summary>
    /// 防夹模式用为空自动费用
    /// </summary>
    public decimal? Mev { get; set; } // 使用 decimal? 以支持 null  

    /// <summary>
    /// 滑点
    /// </summary>
    public int? Slippage { get; set; }

    /// <summary>
    /// 买入随机金额[最小值,最大值,小数位]
    /// </summary>
    [SugarColumn(ColumnDescription = "买入随机金额", ColumnDataType = "text", IsJson = true, IsNullable = true)]
    public List<decimal?> AmountData { get; set; }

    /// <summary>
    /// 买入随机时间单位秒[最小值,最大值]
    /// </summary>
    [SugarColumn(ColumnDescription = "买入随机时间单位秒", ColumnDataType = "text", IsJson = true, IsNullable = true)]
    public List<int?> IntervalData { get; set; }

    /// <summary>
    /// 添加时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompleteTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Mark { get; set; }
}