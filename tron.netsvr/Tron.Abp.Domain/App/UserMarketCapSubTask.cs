
//
// Copyright (c) 2021-present zuohuaijun, Daming Co.,Ltd and Contributors
//


using SqlSugar;
using Tron.Abp.Domain.Contracts.Enum;
using Tron.Abp.Domain.Entity;
using Tron.Abp.SqlSugar.SqlSugar;

namespace Tron.Abp.Domain;
/// <summary>
/// 市值管理子任务表
///</summary>
[SugarTable("T_UserMarketCapSubTask", TableDescription = "市值管理子任务表")]
public class UserMarketCapSubTask : BaseId,IDeletedFilter
{
    /// <summary>
    /// 所属主任务ID
    /// </summary>
    public int TaskId { get; set; }
    /// <summary>
    /// 所属用户ID
    /// </summary>
    public int UId { get; set; }
    /// <summary>
    /// 链
    /// </summary>
    public string Chain { get; set; }
    /// <summary>
    /// 代币地址
    /// </summary>
    public string TokenAddress { get; set; }
    /// <summary>
    /// 代币名称
    /// </summary>
    public string TokenName { get; set; }
    /// <summary>
    /// 代币缩写
    /// </summary>
    public string TokenSymbol { get; set; }
    /// <summary>
    /// 代币Icon
    /// </summary>
    public string TokenIcon { get; set; }
    /// <summary>
    /// 分组Ids
    /// </summary>
    [SugarColumn(ColumnDescription = "分组Ids", ColumnDataType = "text", IsJson = true, IsNullable = true)]
    public List<int> GroupId { get; set; }
    /// <summary>
    /// 模式 1拉盘/买入，2砸盘/卖出，3防夹刷量
    /// </summary>
    public int Mode { get; set; }
    /// <summary>
    /// 拉盘买入金额区间[开始，结束]单位SOL
    /// </summary>
    [SugarColumn(ColumnDescription = "拉盘买入金额区间", IsJson = true, IsNullable = true)]
    public List<decimal?> AmountData { get; set; } = new() { null, null };
    /// <summary>
    /// 砸盘卖出百分比
    /// </summary>
    [SugarColumn(ColumnDescription = "砸盘百分比", IsNullable = true)]
    public int? SellPercent { get; set; }
    /// <summary>
    /// 时间区间[开始，结束]单位秒
    /// </summary>
    [SugarColumn(ColumnDescription = "时间区间", IsJson = true, IsNullable = true)]
    public List<int> IntervalData { get; set; }
    /// <summary>
    /// 滑点
    /// </summary>
    [SugarColumn(ColumnDescription = "滑点", IsNullable = true)]
    public int Slippage { get; set; }
    /// <summary>
    /// gas费
    /// </summary>
    [SugarColumn(ColumnDescription = "gas费", IsNullable = true)]
    public decimal? Gas { get; set; }
    /// <summary>
    /// 是否开启mev
    /// </summary>
    [SugarColumn(ColumnDescription = "是否开启mev", IsNullable = true)]
    public bool IsMev { get; set; }
    /// <summary>
    /// mev费
    /// </summary>
    [SugarColumn(ColumnDescription = "mev费", IsNullable = true)]
    public decimal? Mev { get; set; }
    /// <summary>
    /// 目标价格
    /// </summary>
    [SugarColumn(ColumnDescription = "目标价格", ColumnDataType = "decimal(30,15)", IsNullable = true)]
    public decimal? TargetPrice { get; set; }
    /// <summary>
    /// 当前价格
    /// </summary>
    [SugarColumn(ColumnDescription = "当前价格", ColumnDataType = "decimal(30,15)", IsNullable = true)]
    public decimal? CurrentPrice { get; set; }
    /// <summary>
    /// 刷量次数
    /// </summary>
    [SugarColumn(ColumnDescription = "刷量次数", IsNullable = true)]
    public int? BrushCount { get; set; }
    /// <summary>
    /// 当前刷量次数
    /// </summary>
    [SugarColumn(ColumnDescription = "当前刷量次数", IsNullable = true)]
    public int? CurrentBrushCount { get; set; }
    /// <summary>
    /// 状态 -1终止，0暂停，1运行
    /// </summary>
    [SugarColumn(ColumnDescription = "状态", IsNullable = true)]
    public int Status { get; set; }
    /// <summary>
    /// 是否开启 开启后当外部买入总金额超过配置的总金额 暂停任务
    /// </summary>
    public bool IsOpen { get; set; }
    /// <summary>
    /// 外部买入总金额
    /// </summary>
    [SugarColumn(ColumnDescription = "当前价格", ColumnDataType = "decimal(30,6)", IsNullable = true)]
    public decimal? AmountAll { get; set; }
    /// <summary>
    /// 当前外部买入总金额
    /// </summary>
    [SugarColumn(ColumnDescription = "当前价格", ColumnDataType = "decimal(30,6)", IsNullable = true)]
    public decimal? CurrentAmountAll { get; set; }
    /// <summary>
    /// 添加时间
    /// </summary>
    public DateTime CreateTime { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public string? Mark { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    [SugarColumn(ColumnDescription = "软删除")]
    public virtual bool IsDelete { get; set; } = false;
}

