
//
// Copyright (c) 2021-present zuohuaijun, Daming Co.,Ltd and Contributors
//


using SqlSugar;
using Tron.Abp.Domain.Contracts.Enum;
using Tron.Abp.Domain.Entity;
using Tron.Abp.SqlSugar.SqlSugar;

namespace Tron.Abp.Domain;
/// <summary>
/// 市值管理主任务
///</summary>
[SugarTable("T_UserMarketCapTask", TableDescription = "市值管理主任务")]
public class UserMarketCapTask : BaseId,IDeletedFilter
{
    /// <summary>
    /// 所属用户ID
    /// </summary>
    public int UId { get; set; }
    /// <summary>
    /// 链
    /// </summary>
    public string Chain { get; set; }
    /// <summary>
    /// 代币地址
    /// </summary>
    public string TokenAddress { get; set; }
    /// <summary>
    /// 代币名称
    /// </summary>
    public string TokenName { get; set; }
    /// <summary>
    /// 代币缩写
    /// </summary>
    public string TokenSymbol { get; set; }
    /// <summary>
    /// 代币Icon
    /// </summary>
    public string TokenIcon { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    public int OrderIndex { get; set; } = 0;
    /// <summary>
    /// 添加时间
    /// </summary>
    public DateTime CreateTime { get; set; }
    /// <summary>
    /// 外部买入总金额
    /// </summary>
    [SugarColumn(ColumnDescription = "当前价格", ColumnDataType = "decimal(30,6)", IsNullable = true)]
    public decimal? AmountAll { get; set; }
    /// <summary>
    /// 当前外部买入总金额
    /// </summary>
    [SugarColumn(ColumnDescription = "当前价格", ColumnDataType = "decimal(30,6)", IsNullable = true)]
    public decimal? CurrentAmountAll { get; set; }
    /// <summary>
    /// 外部满足以后是1暂停所有子任务，2清仓
    /// </summary>
    [SugarColumn(ColumnDescription = "外部满足以后是1暂停所有子任务，2清仓", ColumnDataType = "decimal(30,6)", IsNullable = true)]
    public int AmountType { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public string? Mark { get; set; }
    /// <summary>
    /// 软删除
    /// </summary>
    [SugarColumn(ColumnDescription = "软删除")]
    public virtual bool IsDelete { get; set; } = false;
}