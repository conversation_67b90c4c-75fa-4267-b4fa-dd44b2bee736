
//
// Copyright (c) 2021-present zuohuaijun, Daming Co.,Ltd and Contributors
//


using SqlSugar;
using Tron.Abp.Domain.Contracts.Enum;
using Tron.Abp.Domain.Entity;

namespace Tron.Abp.Domain;
/// <summary>
/// 钱包信息临时表
///</summary>
[SugarTable("T_UserWalletTemp", TableDescription = "钱包信息临时表")]
public class UserWalletTemp : BaseId
{
    /// <summary>
    /// 所属用户ID
    /// </summary>
    public int UId { get; set; }
    /// <summary>
    /// 公钥
    /// </summary>
    public string PubKey { get; set; }
    /// <summary>
    /// 私钥
    /// </summary>
    public string SecretKey { get; set; }
    /// <summary>
    /// 是否使用过
    /// </summary>
    public bool IsUser { get; set; }
    /// <summary>
    /// 生成时间
    /// </summary>
    public virtual DateTime? CreateTime { get; set; } = DateTime.Now;
}
