
//
// Copyright (c) 2021-present zuohuaijun, Daming Co.,Ltd and Contributors
//


using Tron.Abp.Domain.Contracts.Enum;
using Tron.Abp.Domain.SysEntity;
using Tron.Abp.SqlSugar;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;

namespace Tron.Abp.Domain.SeedData.SysData;

/// <summary>
/// 系统菜单表种子数据
/// </summary>
public class SysMenuSeedData : IDataSeedContributor, ITransientDependency
{
    private readonly SqlSugarRepository<SysMenu> _repSysMenu;
    public SysMenuSeedData(SqlSugarRepository<SysMenu> repSysMenu)
    {
        _repSysMenu = repSysMenu;
    }

    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysMenu> HasData()
    {
        var menu = new[]
        {
            //工作台
            new SysMenu{ Id=1300000000101, Pid=0, Title="首页", Path="/dashboard/home", Name="home", Component="/home/<USER>", IsAffix=true, Icon="ele-HomeFilled", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=0 },
            new SysMenu{ Id=1300000000102, Pid=0, Title="数据总表", Path="/dashboard/all", Name="all", Component="/home/<USER>/index", Icon="ele-Histogram", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000103, Pid=0, Title="渠道报表", Path="/dashboard/companys", Name="companys", Component="/home/<USER>/index", Icon="ele-Medal", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=110 },
            new SysMenu{ Id=1300000000104, Pid=0, Title="推广员报表", Path="/dashboard/staff", Name="staff", Component="/home/<USER>/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=120 },

            //会员管理
            new SysMenu{ Id=1300000000201, Pid=0, Title="会员管理", Path="/user", Name="user", Component="Layout", Icon="fa fa-user", Type=MenuTypeEnum.Dir, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=200 },
            new SysMenu{ Id=1300000000202, Pid=1300000000201, Title="会员列表", Path="/user/list", Name="list", Component="/user/list/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=210 },
            new SysMenu{ Id=1300000000221, Pid=1300000000202, Title="会员编辑", Permission="user:update", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=210 },
            new SysMenu{ Id=1300000000203, Pid=1300000000201, Title="账变记录", Path="/user/trade", Name="trade", Component="/user/trade/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=220 },
            new SysMenu{ Id=1300000000204, Pid=1300000000201, Title="KYC信息", Path="/user/kyc", Name="kyc", Component="/user/kyc/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=230 },
            new SysMenu{ Id=1300000000205, Pid=1300000000201, Title="在线会员", Path="/user/onlinelst", Name="onlinelst", Component="/user/onlinelst/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=215 },

            //财务管理
            new SysMenu{ Id=1300000000301, Pid=0, Title="财务管理", Path="/finance", Name="finance", Component="Layout", Icon="fa fa-scribd", Type=MenuTypeEnum.Dir, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=300 },
            new SysMenu{ Id=1300000000302, Pid=1300000000301, Title="提现管理", Path="/finance/withdraw", Name="withdraw", Component="/finance/withdraw/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=310 },
            new SysMenu{ Id=1300000000303, Pid=1300000000301, Title="提现记录", Path="/finance/withdrawlog", Name="withdrawlog", Component="/finance/withdrawlog/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=320 },
            new SysMenu{ Id=1300000000304, Pid=1300000000301, Title="存款记录", Path="/finance/depositlog", Name="depositlog", Component="/finance/depositlog/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=330 },
            new SysMenu{ Id=1300000000305, Pid=1300000000301, Title="转账记录", Path="/finance/transferlog", Name="transferlog", Component="/finance/transferlog/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=340 },
            new SysMenu{ Id=1300000000306, Pid=1300000000301, Title="返佣记录", Path="/finance/brokeragelog", Name="brokeragelog", Component="/finance/brokeragelog/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=350 },
            

            //渠道管理
            new SysMenu{ Id=1300000000401, Pid=0, Title="渠道管理", Path="/channel", Name="channel", Component="Layout", Icon="ele-Avatar", Type=MenuTypeEnum.Dir, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=400 },
            new SysMenu{ Id=1300000000402, Pid=1300000000401, Title="渠道列表", Path="/channel/companylist", Name="companylist", Component="/channel/company/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=410 },
            new SysMenu{ Id=1300000000403, Pid=1300000000401, Title="推广员列表", Path="/channel/stafflist", Name="stafflist", Component="/channel/staff/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=420 },

            //支付管理
            new SysMenu{ Id=1300000000501, Pid=0, Title="支付管理", Path="/payment", Name="payment", Component="Layout", Icon="ele-DataBoard", Type=MenuTypeEnum.Dir, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=500 },
            new SysMenu{ Id=1300000000502, Pid=1300000000501, Title="支付列表", Path="/payment/paylist", Name="paylist", Component="/payment/paylist/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=510 },
            new SysMenu{ Id=1300000000503, Pid=1300000000501, Title="代付列表", Path="/payment/paying", Name="paying", Component="/payment/paying/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=520 },
            new SysMenu{ Id=1300000000504, Pid=1300000000501, Title="提现配置", Path="/payment/withdrawconf", Name="withdrawconf", Component="/payment/withdrawconf/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=530 },
            new SysMenu{ Id=1300000000505, Pid=1300000000501, Title="转账配置", Path="/payment/transfer", Name="transfer", Component="/payment/transfer/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=540 },
            new SysMenu{ Id=1300000000506, Pid=1300000000501, Title="存取报表", Path="/payment/payreport", Name="payreport", Component="/payment/payreport/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=550 },

            //VIP管理
            new SysMenu{ Id=1300000000551, Pid=0, Title="VIP管理", Path="/vip", Name="vip", Component="Layout", Icon="fa fa-vimeo", Type=MenuTypeEnum.Dir, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=550 },
            new SysMenu{ Id=1300000000552, Pid=1300000000551, Title="VIP配置", Path="/vip/viplist", Name="viplist", Component="/vip/viplist/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=510 },
            new SysMenu{ Id=1300000000553, Pid=1300000000551, Title="推荐配置", Path="/vip/spread", Name="spread", Component="/vip/spread/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=520 },
            new SysMenu{ Id=1300000000554, Pid=1300000000551, Title="返佣配置", Path="/vip/brokerage", Name="brokerage", Component="/vip/brokerage/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=530 },

            //游戏管理
            new SysMenu{ Id=1300000000651, Pid=0, Title="游戏管理", Path="/game", Name="game", Component="Layout", Icon="ele-Help", Type=MenuTypeEnum.Dir, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=600 },
            new SysMenu{ Id=1300000000652, Pid=1300000000651, Title="厂商配置", Path="/game/vendor", Name="vendor", Component="/game/vendor/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=730 },
            new SysMenu{ Id=1300000000653, Pid=1300000000651, Title="游戏分类", Path="/game/gametype", Name="gametype", Component="/game/gametype/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=740 },
            new SysMenu{ Id=1300000000654, Pid=1300000000651, Title="游戏列表", Path="/game/gamelist", Name="gamelist", Component="/game/gamelist/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=750 },
            new SysMenu{ Id=1300000000655, Pid=1300000000651, Title="游戏记录", Path="/game/gamerecord", Name="gamerecord", Component="/game/gamerecord/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=760 },

            //活动管理
            new SysMenu{ Id=1300000000601, Pid=0, Title="活动管理", Path="/activity", Name="activity", Component="Layout", Icon="ele-Notebook", Type=MenuTypeEnum.Dir, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=650 },
            new SysMenu{ Id=1300000000604, Pid=1300000000601, Title="抽奖配置", Path="/activity/luckdraw", Name="luckdraw", Component="/activity/luckdraw/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=605 },
            new SysMenu{ Id=1300000000602, Pid=1300000000601, Title="活动列表", Path="/activity/activitylist", Name="activitylist", Component="/activity/activitylist/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=610 },
            new SysMenu{ Id=1300000000603, Pid=1300000000601, Title="活动日志", Path="/activity/activitylog", Name="activitylog", Component="/activity/activitylog/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=620 },
            new SysMenu{ Id=1300000000605, Pid=1300000000601, Title="拼 多 多", Path="/activity/activityscanlog", Name="activityscanlog", Component="/activity/activityscanlog/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=630 },

            //运营管理
            new SysMenu{ Id=1300000000701, Pid=0, Title="运营管理", Path="/operate", Name="operate", Component="Layout", Icon="fa fa-map", Type=MenuTypeEnum.Dir, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=700 },
            new SysMenu{ Id=1300000000702, Pid=1300000000701, Title="系统配置", Path="/operate/sysconfig", Name="sysconfig", Component="/operate/sysconfig/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=710 },
            new SysMenu{ Id=1300000000703, Pid=1300000000701, Title="公告管理", Path="/operate/notice", Name="notice", Component="/operate/notice/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=760 },
            new SysMenu{ Id=1300000000704, Pid=1300000000701, Title="帮助中心", Path="/operate/help", Name="help", Component="/operate/help/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=770 },
            new SysMenu{ Id=1300000000705, Pid=1300000000701, Title="站内消息", Path="/operate/message", Name="message", Component="/operate/message/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=780 },
           

            //后台管理
            new SysMenu{ Id=1300000000801, Pid=0, Title="后台管理", Path="/system", Name="system", Component="Layout", Icon="ele-Setting", Type=MenuTypeEnum.Dir, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=800 },
            new SysMenu{ Id=1300000000803, Pid=1300000000801, Title="账号管理", Path="/system/user", Name="sysUser", Component="/system/user/index", Icon="ele-User", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=710 },
            new SysMenu{ Id=1300000000812, Pid=1300000000803, Title="查询", Permission="sysUser:page", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000813, Pid=1300000000803, Title="编辑", Permission="sysUser:update", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000814, Pid=1300000000803, Title="增加", Permission="sysUser:add", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000815, Pid=1300000000803, Title="删除", Permission="sysUser:delete", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000816, Pid=1300000000803, Title="详情", Permission="sysUser:detail", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000817, Pid=1300000000803, Title="授权角色", Permission="sysUser:grantRole", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000818, Pid=1300000000803, Title="重置密码", Permission="sysUser:resetPwd", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000819, Pid=1300000000803, Title="设置状态", Permission="sysUser:setStatus", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },

            new SysMenu{ Id=1300000000804, Pid=1300000000801, Title="角色管理", Path="/system/role", Name="sysRole", Component="/system/role/index", Icon="ele-Help", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=720 },
            new SysMenu{ Id=1300000000832, Pid=1300000000804, Title="查询", Permission="sysRole:page", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000833, Pid=1300000000804, Title="编辑", Permission="sysRole:update", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000834, Pid=1300000000804, Title="增加", Permission="sysRole:add", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000835, Pid=1300000000804, Title="删除", Permission="sysRole:delete", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000836, Pid=1300000000804, Title="授权菜单", Permission="sysRole:grantMenu", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000837, Pid=1300000000804, Title="授权数据", Permission="sysRole:grantDataScope", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1300000000838, Pid=1300000000804, Title="设置状态", Permission="sysRole:setStatus", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },

            //修改密码
            new SysMenu{ Id=1300000000000, Pid=0, Title="修改密码", Permission="sysUser:changePwd", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },

        };
        return menu;
    }

    public async Task SeedAsync(DataSeedContext context)
    {
        if (await _repSysMenu.CountAsync(it => true) > 0) return;

        await _repSysMenu.AsInsertable(HasData().ToArray()).OffIdentity().ExecuteCommandAsync();

        await Task.CompletedTask;
    }
}