namespace Tron.Abp.Multiplex.Contracts;

public class StreamKey
{
    /// <summary>
    /// 1对多
    /// </summary>
    public const string StreamOne2More = "stream_one2more";

    public const string StreamTransfer = "stream_transfer";
    public const string SmartWalletStream = nameof(SmartWalletStream);

    /// <summary>
    /// 快速交易 子任务 订阅 key
    /// </summary>
    public const string QuickTraderTaskStream = nameof(QuickTraderTaskStream);

    #region 批量交易

    /// <summary>
    ///  批量交易 监听服务 
    /// </summary>
    public const string BatchTradeStream = nameof(BatchTradeStream);
    /// <summary>
    /// 批量交易 子任务 监听服务 
    /// </summary>
    public const string BathchTradeSubTaskStream = nameof(BathchTradeSubTaskStream);
    /// <summary>
    /// 批量交易 给指定用户购买
    /// </summary>
    public const string BatchTradeToUsersStream = nameof(BatchTradeToUsersStream);
    #endregion
    /// <summary>
    /// 捆绑交易
    /// </summary>
    public const string BindTradeStream = nameof(BindTradeStream);

    /// <summary>
    /// 跟单 消息Key 用户ID-》任务 ID=》操作类型=》运行状态
    /// </summary>
    public const string CopyTraderStream = nameof(CopyTraderStream);

    /// <summary>
    /// 跟单 交易 ws消息
    /// </summary>
    public const string CopyTraderWsStream = nameof(CopyTraderWsStream);

    /// <summary>
    /// 跟单 交易 延时服务
    /// </summary>
    public const string CopyTradeDelayBuyTaskStream = nameof(CopyTradeDelayBuyTaskStream);

    /// <summary>
    /// 删除 持有 ，更新 钱包
    /// </summary>
    public const string RemoveTokenInventoryStream = nameof(RemoveTokenInventoryStream);

    /// <summary>
    /// 跟单钱包子任务
    /// </summary>
    public const string CopyTraderTaskStream = nameof(CopyTraderTaskStream);

    /// <summary>
    /// 删除跟单 消息KEY
    /// </summary>
    public const string CopyTraderStreamDel = nameof(CopyTraderStreamDel);

    /// <summary>
    /// 指量开启 跟单
    /// </summary>
    public const string CopyTraderStreamRun = nameof(CopyTraderStreamRun);

    /// <summary>
    /// 批量停止 跟单
    /// </summary>
    public const string CopyTraderStreamStop = nameof(CopyTraderStreamStop);

    /// <summary>
    /// redis 订阅 发布  跟单 交易
    /// </summary>
    public const string RedisSubscriberCopyTradeStream = nameof(RedisSubscriberCopyTradeStream);

    /// <summary>
    /// ws pump 价格
    /// </summary>
    public const string EnhancedWsPumpFunStream = nameof(EnhancedWsPumpFunStream);

    /// <summary>
    /// ws ray 价格
    /// </summary>
    public const string EnhancedWsRaydiumStream = nameof(EnhancedWsRaydiumStream);

    /// <summary>
    /// 持 仓 百分百卖出
    /// </summary>
    public const string UserMyTokensSellStream = nameof(UserMyTokensSellStream);

    /// <summary>
    /// pump swap 价格
    /// </summary>
    public const string SolanaPumpSwapPriceStream = nameof(SolanaPumpSwapPriceStream);
    /// <summary>
    /// 市值管理
    /// </summary>
    public const string MarketCapActionStream = nameof(MarketCapActionStream);
    /// <summary>
    /// 市值管理 清仓
    /// </summary>
    public const string MarketCapClearTokenStream = nameof(MarketCapClearTokenStream);
    /// <summary>
    /// 代币监控
    /// </summary>
    public const string SolanaMonitorMintStream=nameof(SolanaMonitorMintStream);
    /// <summary>
    /// 回收账号
    /// </summary>
    public const string SolanaBurnAndCloseAccountStream = nameof(SolanaBurnAndCloseAccountStream);
}