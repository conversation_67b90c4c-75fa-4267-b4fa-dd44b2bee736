using System.Text.Json;
using System.Text.Json.Serialization;

namespace Tron.Abp.Multiplex.Contracts;

public class NullableDecimalConverter : JsonConverter<decimal?>
{
    public override decimal? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.Null)
        {
            return null;
        }

        try
        {
            if (reader.TokenType == JsonTokenType.Number)
            {
                if (reader.TryGetDecimal(out var value))
                {
                    return value;
                }
            }
            else if (reader.TokenType == JsonTokenType.String)
            {
                var stringValue = reader.GetString();
                if (string.IsNullOrWhiteSpace(stringValue))
                {
                    return null;
                }

                if (decimal.TryParse(stringValue, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out var value))
                {
                    return value;
                }
            }
        }
        catch (Exception ex)
        {
            // 记录异常值以便调试
            Console.WriteLine($"Failed to parse decimal: {reader.GetString() ?? "null"}, Error: {ex.Message}");
            return null;
        }

        // 无法解析时返回 null
        Console.WriteLine($"Invalid decimal value: {reader.GetString() ?? "null"}");
        return null;
    }

    public override void Write(Utf8JsonWriter writer, decimal? value, JsonSerializerOptions options)
    {
        if (value.HasValue)
        {
            writer.WriteNumberValue(value.Value);
        }
        else
        {
            writer.WriteNullValue();
        }
    }
}