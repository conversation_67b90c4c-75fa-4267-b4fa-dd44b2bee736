// See https://aka.ms/new-console-template for more information

using System;
using System.Collections;
using System.Collections.Specialized;
using System.Drawing;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.WebSockets;
using System.Numerics;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using CliWrap;
using CliWrap.Buffered;
using Colorful;
using Cryptography.Obfuscation.Factory;
using Flurl.Http;
using Google.Protobuf;
using Google.Protobuf.Collections;
using Grpc.Net.Client;
using GrpcGeyser;
using Helius;
using NATS.Client.Core;
using NATS.Net;
using SolanaConsoleApp;
using Solnet.AABot.Pumpfun;
using Solnet.AABot.PumpSwap;
using Solnet.AABot.Raydium.AMM;
using Solnet.AABot.RaydiumClmm.Model;
using Solnet.Programs;
using Solnet.Programs.Utilities;
using Solnet.Raydium;
using Solnet.Rpc;
using Solnet.Rpc.Models;
using Solnet.Rpc.Types;
using Solnet.Wallet;
using Solnet.Metaplex;
using TouchSocket.Core;
using TouchSocket.Http;
using TouchSocket.Http.WebSockets;
using TouchSocket.Sockets;
using Console = Colorful.Console;
using static Tron.Abp.Multiplex.Solana.Dto.AccountInfo;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Primitives;
using NATS.Client;
using NATS.Client.JetStream;
using NATS.Client.JetStream.Models;
using NATS.Client.Serializers.Json;
using Serilog.Extensions.Logging;
using Solnet.AABot.Launchpad;
using Solnet.AABot.MeteoraDBC;
using Solnet.AABot.MeteoraDLMM;
using Solnet.AABot.MeteoraDYN;
using Solnet.AABot.RaydiumClmm;
using Solnet.AABot.RaydiumCpmm;
using Solnet.Launchpad;
using Solnet.Pumpfun;
using Tron.Abp.Multiplex.Contracts;
using Tron.Abp.Multiplex.Solana;
using JupAgStreamResult = SolanaConsoleApp.Model.JupAgStreamResult;
using Log = Serilog.Log;
using PumpfunProgram = Solnet.AABot.Pumpfun.PumpfunProgram;


Figlet figlet = new Figlet();
Console.WriteLine(figlet.ToAscii("Solana"), ColorTranslator.FromHtml("#FAD6FF"));
//https://mainnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3
IRpcClient rpcClient = ClientFactory.GetClient("https://solana-rpc.publicnode.com");
var url = "https://devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3";
var wssUrl = "wss://mainnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3";
var RpcClient = ClientFactory.GetClient(url);
var streamingRpcClient = ClientFactory.GetStreamingClient(wssUrl);

//await 获取余额();
//await Pump订阅价格();
//await Raydium订阅价格();
//await gRpc测试();
//await 消息解析();
//await PumpWebSocket测试();
//await Nats测试();
//await 木星ws测试();
//await Price测试();
//await WssPrice测试();
//await Example();
//await Jup解析();
//await Ws测试();
//await 合约测试();
//await PumpWebSocket测试TouchWs();
//await SolanaWsPrice();
//await Work测试();

//var aa = Convert.FromHexString("clmm_swap_base_in");
//await Raydiumclmm测试();
//await Helius测试();
//await RayCPMM测试();
//await PumpSwap测试();
//await AABot测试();
//await SolApi测试();
//await AABot测试();
await SolApi测试();
//await AxiomWs测试();
//await PumpNatsTest();
//await NatsTest();
Console.ReadLine();


async Task PumpNatsTest()
{
    // 配置 NATS 连接
    var url = "wss://prod-v2.nats.realtime.pump.fun/";
    var opts = NatsOpts.Default with
    {
        Url = url,
        SerializerRegistry = NatsJsonSerializerRegistry.Default,
        AuthOpts = new NatsAuthOpts()
        {
            Username = "subscriber",
            Password = "lW5a9y20NceF6AE9"
        },
        WebSocketOpts =  NatsWebSocketOpts.Default with
        {
            RequestHeaders = new Dictionary<string, StringValues>()
            {
                {"Origin", "https://pump.fun"},
                {"Sec-Websocket-Extensions", "permessage-deflate; client_no_context_takeover; server_no_context_takeover;client_max_window_bits"},
                {"User-Agent", "Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"}
            }
        }
    };
    var client = new NatsClient(opts);
    await client.ConnectAsync();
    await foreach(var msg in  client.SubscribeAsync<object>("newCoinCreated.prod"))
    {
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss,fff}]: {msg.Subject}: {msg.Data}");
    }
    

}

async Task NatsTest()
{
    /*
     *  VITE_NATS_SERVERS=ws://192.168.31.23:8080
        VITE_NATS_USER=ruser
        VITE_NATS_PASS=T0pS3cr3t
        VITE_NATS_DEBUG=false
     */
    // 配置 NATS 连接
    var url = "nats://192.168.31.23:4222/";
    var opts = NatsOpts.Default with
    {
        Url = url,
        SerializerRegistry = NatsJsonSerializerRegistry.Default,
        AuthOpts = new NatsAuthOpts()
        {
            Username = "admin",
            Password = "tron_admin_2024"
        }
    };

    var client = new NatsClient(opts);
    INatsJSContext js = client.CreateJetStreamContext();

    // 流配置
    var streamName = "EVENTS";
    var streamConfig = new StreamConfig(streamName, subjects: new[] { "token.>" })
    {
        MaxBytes = 1024000, // 增加到 100 KB
        Retention = StreamConfigRetention.Workqueue
    };
    INatsJSStream streamInfo;
    // 检查并创建/更新流
    try
    {
        streamInfo = await js.GetStreamAsync(streamName);

        Console.WriteLine(
            $"流 {streamName} 已存在，存储: {streamInfo.Info.State.Bytes} 字节，消息: {streamInfo.Info.State.Messages}  消费者: {streamInfo.Info.State.ConsumerCount}");
        if (streamInfo.Info.Config.MaxBytes != streamConfig.MaxBytes)
        {
            Console.WriteLine("流存储限制不匹配，尝试更新");
            streamInfo = await js.UpdateStreamAsync(streamConfig);
            Console.WriteLine($"流 {streamName} 更新成功");
        }
    }
    catch (NatsJSException ex) when (ex.Message.Contains("stream not found"))
    {
        streamInfo = await js.CreateStreamAsync(streamConfig);
        Console.WriteLine($"流 {streamName} 创建成功");
    }

    // 检查并清理多余消费者
    var consumers = streamInfo.ListConsumerNamesAsync(); // await js.GetConsumerAsync(streamName);
    await foreach (var c in consumers)
    {
        if (c != "foo-durable-pool" || c != "foo-durable-price")
        {
            await js.DeleteConsumerAsync(streamName, c);
            Console.WriteLine($"删除多余消费者: {c}");
        }
    }

    // 发布消息（后台任务）
    var cts = new CancellationTokenSource();
    Console.CancelKeyPress += async (s, e) =>
    {
        cts.Cancel();
        e.Cancel = true;
        //await client.DisposeAsync();
    };
    Task publisher = Task.Run(async () =>
    {
        while (!cts.Token.IsCancellationRequested)
        {
            try
            {
                await js.PublishAsync("token.solana.pool", $"event-data {DateTime.Now:HH:mm:ss,fff}");
                await js.PublishAsync("token.solana.price", $"event-data {DateTime.Now:HH:mm:ss,fff}");
                //Console.WriteLine("消息发布成功");
                await Task.Delay(10, cts.Token);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发布消息失败: {ex.Message}");
                await Task.Delay(5000, cts.Token);
            }
        }
    }, cts.Token);

    Task subscriberPool = Task.Run(async () =>
    {
        try
        {
            // 配置消费者
            var consumerName = "foo-durable-pool";
            var consumerConfig = new ConsumerConfig(consumerName)
            {
                FilterSubject = "token.solana.pool",
                DurableName = consumerName,
                AckPolicy = ConsumerConfigAckPolicy.Explicit, // 使用显式确认
                MaxBatch = 5,
                MaxExpires = TimeSpan.FromSeconds(30)
            };
            var consumer = await streamInfo.CreateOrUpdateConsumerAsync(consumerConfig);
            Console.WriteLine("pool 消费者创建或更新成功");
            await foreach (var msg in consumer.ConsumeAsync<string>(opts: new NatsJSConsumeOpts
                           {
                               MaxMsgs = 2,
                               Expires = TimeSpan.FromSeconds(30),
                           }))
            {
                Console.WriteLine($"收到消息: {msg.Subject} - {msg.Data}");
                await msg.AckAsync();
            }
        }
        catch (Exception e)
        {
            System.Console.WriteLine(e);
        }
    }, cts.Token);
    Task subscriberPrice = Task.Run(async () =>
    {
        try
        {
            // 配置消费者
            var consumerName = "foo-durable-price";
            var consumerConfig = new ConsumerConfig(consumerName)
            {
                FilterSubject = "token.solana.price",
                DurableName = consumerName,
                AckPolicy = ConsumerConfigAckPolicy.Explicit, // 使用显式确认
                MaxBatch = 5,
                MaxExpires = TimeSpan.FromSeconds(30)
            };
            var consumer = await streamInfo.CreateOrUpdateConsumerAsync(consumerConfig);
            Console.WriteLine("price 消费者创建或更新成功");
            await foreach (var msg in consumer.ConsumeAsync<string>(opts: new NatsJSConsumeOpts
                           {
                               MaxMsgs = 2,
                               Expires = TimeSpan.FromSeconds(30),
                           }))
            {
                Console.WriteLine($"收到消息: {msg.Subject} - {msg.Data}");
                await msg.AckAsync();
            }
        }
        catch (Exception e)
        {
            System.Console.WriteLine(e);
        }
    }, cts.Token);
    // 等待所有任务
    await Task.WhenAll(publisher, subscriberPool, subscriberPrice);

    // 释放客户端
    await client.DisposeAsync();
}

async Task AxiomWs测试()
{
    async Task<string> RefreshToken(string authRefreshToken)
    {
        var response = await "https://api2.axiom.trade/"
                //.WithHeader("accept", "application/json")
                .WithHeader("origin", "https://axiom.trade")
                .WithHeader("referer", "https://axiom.trade")
                .WithHeader("priority", "u=1, i")
                .WithCookie("auth-refresh-token", authRefreshToken)
                .WithHeader("User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0")
                .AppendPathSegment($"/refresh-access-token")
                .PostAsync()
            //.ReceiveString()
            ;
        // 验证响应体
        var responseBody = await response.GetStringAsync();
        var accountToken = response.Cookies.FirstOrDefault(it => it.Name == "auth-access-token")?.Value;

        return accountToken;
    }

    var url = "wss://cluster2.axiom.trade/";
    NameValueCollection header = new NameValueCollection();
    header.Add("Origin", "https://axiom.trade");
    header.Add("Sec-Websocket-Extensions", //permessage-deflate; client_max_window_bits
        "permessage-deflate; client_no_context_takeover; server_no_context_takeover;client_max_window_bits");
    header.Add("User-Agent",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0");
    //   auth-access-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.CivOH2B-Q91pFlpYSEa6H9hqynYxTkiWeXWfa3583Jo
    var authAccessToken =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.G5hU3-d8_egzTexCf4LuL_uBuz9--z5xqE-7D3O_4Gc";
    //                      eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWZyZXNoVG9rZW5JZCI6IjA0MTc5NjJlLTBiMWItNGMwMi05NjJiLWFiMTFhMjQzNjc5NCIsImlhdCI6MTc0NTM5MDM2MX0.4AbSLU_qfTw7aHT3glxutp_OTU4tli7I2oEe4gwESYg
    var authRefreshToken =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWZyZXNoVG9rZW5JZCI6IjA0MTc5NjJlLTBiMWItNGMwMi05NjJiLWFiMTFhMjQzNjc5NCIsImlhdCI6MTc0NTM5MDM2MX0.4AbSLU_qfTw7aHT3glxutp_OTU4tli7I2oEe4gwESYg";

    authAccessToken = await RefreshToken(authRefreshToken);

    header.Add("Cookie",
        $"auth-access-token={authAccessToken};auth-refresh-token={authRefreshToken}");
    var ws = new TSWsClient(url, header);
    var time = new Timer(async (obj) => { await ws.SendStringAsync("{\"method\":\"ping\"}"); }, null, -1, -1);
    ws.OnConnected += async () =>
    {
        var newPairs = "{\"action\":\"join\",\"room\":\"new_pairs\"}";
        await ws.SendStringAsync(newPairs);
        var updatePulse = "{\"action\":\"join\",\"room\":\"update_pulse\"}";
        await ws.SendStringAsync(updatePulse);
        time.Change(10, 10 * 1000);
    };
    var types = new List<string>();
    ws.OnMessageReceived += (msg) =>
    {
        var message = Encoding.UTF8.GetString(msg);
        //Console.WriteLine(message);
        if (message.StartsWith("{\"room\":\"new_pairs\""))
        {
            var pairs = JsonSerializer.Deserialize<AxiomTokenInfo.Message<AxiomTokenInfo.NewPairsTokenInfo>>(message,
                AxiomTokenInfo._jsonOptions);
            if (pairs.Content.Protocol == "LaunchLab")
            {
                Console.WriteLine(
                    $"{DateTime.Now:HH:mm:ss,fff} 新币:{pairs.Content.TokenAddress} {pairs.Content.TokenName}");
            }
        }
        else if (message.Contains("{\"room\":\"update_pulse\""))
        {
            var updatePulse =
                JsonSerializer.Deserialize<AxiomTokenInfo.Message<List<AxiomTokenInfo.UpdatePulseTokenInfo>>>(message,
                    AxiomTokenInfo._jsonOptions);
            foreach (var pairs in updatePulse.Content)
            {
                if (pairs.Protocol == "LaunchLab")
                {
                    Console.WriteLine($"{DateTime.Now:HH:mm:ss,fff} 更新{pairs.TokenAddress} {pairs.TokenName}");
                }
            }
        }
    };
    await ws.ConnectAsync();
}

async Task SolApi测试()
{
    //3gySL1xTSjXwG4vZCE262apP3xZnuLU2HubFLzXJ2YX4
    var account = Account.FromSecretKey("3cF5AaSAsCtYtNrJVW44hxEDYJEFmiXUvANZJ83R6SFsKGLGXyFL1Mcdd14QHFL9JJPCu5aCgtSTbqfVs9VbzF78");
    var RpcClient =
        ClientFactory.GetClient("https://devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3");
    var ws = ClientFactory.GetStreamingClient(url);
    var isDev = RpcClient.NodeAddress.Host.Contains("devnet");
    /*
    var list = await RpcClient.GetTokenAccountsByOwnerAsync("22QgNB6kA8g7wgGnjGkw61dw55GpQarfDtqij9uKNGax",
        tokenProgramId: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");

    var items = list.Result.Value;

    /*var item1=items.FirstOrDefault(it=>it.PublicKey=="2KVCCEmv768hDn7zeR7vLG2jwRdymjeDWLdjrV7bL9SK");
    var item2=items.FirstOrDefault(it=>it.PublicKey=="2VWZuCGB7N2GLviU6hQdW5KHNJ9xEtQk5PbbkP5vGsn3");
    item1.Account.Data.Program = "10";
    item2.Account.Data.Program = "100";#1#*/
    //4AgPNEkEAcBhDuiUGh8gtC9FrmvDQffY13Qcps3nbby5ZoHT924tdwDGAaZsY5J3sWimvasZcxC6ye3JuyoLM8c8
    //QyRArHcCq4VWHWBBDKhdQb39AR83fVrbe8ZRKtFgyuT7bP71HffRH4S2mnvgMts6gHRABN1saWokRLvUuc9wR2W

    //2VXQCKFdyBqFUybxJ7Dgnwvsma9HUyfD4SGNCRyyjmJBLqFoEsRsKLD9d6P4Fip4wuJtkYeY6f4CCokgCFkbqvzb
    //5Qi4fRkZWFKoRdA3fV82NAX4SAnwH8k7d9Nzi1ixgbLzHtntxK5fQPEvp19oRUuNWv8vKu3p6i7dWFyf4yJqRofN
    //cpmm
    async Task CpmmtranInfo()
    {
        var mint = "CAWXa1qdLhkphSJX6zhk9VG35sJHgqHtw7Y6cPKmnHfb";
        var isBuy = true;
        var tranInfo = await RpcClient.GetTransactionAsync(
            "4QWkvmt7U2ppjY7eRfjunH47h14AmTbpGrKP1Gsqhk1QvPU7SGbdLp736CNAETx5UW6sYwVvymQW2Q3W78HbQc8H");
        TransactionInfo transactionInfo = ((TransactionInfo)tranInfo.Result.Transaction);
        var accounts = transactionInfo.Message.AccountKeys;
        var curPublicKey = accounts[0];
        var owner = isDev
            ? "7rQ1QFNosMkUCuh7Z7fPbTHvh73b68sQYdirycEzJVuw"
            : "GpMZbSM2GgvTKHJirzeGfMFoaZ8UR2X7F4v8vHTvxFbL";
        var postBalances = tranInfo.Result.Meta.PostTokenBalances;
        var preBalances = tranInfo.Result.Meta.PreTokenBalances;

        var solPostBalance = postBalances.FirstOrDefault(it =>
            it.Owner == owner && it.Mint == "So11111111111111111111111111111111111111112");
        var solPreBalance = preBalances.FirstOrDefault(it =>
            it.Owner == owner && it.Mint == "So11111111111111111111111111111111111111112");

        var solAmount = isBuy switch
        {
            false => (solPostBalance?.UiTokenAmount?.AmountDecimal ?? 0) -
                     (solPreBalance?.UiTokenAmount?.AmountDecimal ?? 0),
            true => (solPreBalance?.UiTokenAmount?.AmountDecimal ?? 0) -
                    (solPostBalance?.UiTokenAmount?.AmountDecimal ?? 0),
        };
        Console.WriteLine(solAmount);
        var preTokenBalance =
            preBalances.FirstOrDefault(it => it.Owner == owner && it.Mint == mint);
        var postTokenBalance =
            postBalances.FirstOrDefault(it => it.Owner == owner && it.Mint == mint);
        var tokenAmount = isBuy switch
        {
            true => postTokenBalance.UiTokenAmount.AmountDecimal - (preTokenBalance?.UiTokenAmount?.AmountDecimal ??
                                                                    0),
            false => preTokenBalance.UiTokenAmount.AmountDecimal -
                     (postTokenBalance?.UiTokenAmount?.AmountDecimal ??
                      0),
        };
        Console.WriteLine(tokenAmount);
    }

    async Task PumpFunTranInfo()
    {
        var mint = "MbqRwdbAJRvkbtquYSy33eh9Fv3h8fWkRTZ477spump";
        var isBuy = true;
        var tranInfo = await RpcClient.GetTransactionAsync(
            "5ZwPrDWBagsF2rygR7EA9KQvCiN6PQ5RPnHz49KRZRyZxvfe7hyBuZf3H8S63DJQXG3f3F7LGKbtL7xDqKogKTNd");
        TransactionInfo transactionInfo = ((TransactionInfo)tranInfo.Result.Transaction);
        var accounts = transactionInfo.Message.AccountKeys;
        var curPublicKey = accounts[0];

        var pumpFunIdIndex = Array.IndexOf(accounts, PumpfunProgram.ProgramID);
        if (pumpFunIdIndex <= 0)
        {
            var reaonly = tranInfo.Result.Meta.LoadedAddresses.Readonly;
            var writable = tranInfo.Result.Meta.LoadedAddresses.Writable;
            //Array.Resize(ref accounts, accounts.Length + reaonly.Length+writable.Length);
            accounts = accounts.Concat(writable).Concat(reaonly).ToArray();
            pumpFunIdIndex = Array.IndexOf(accounts, PumpfunProgram.ProgramID);
        }

        var data = tranInfo.Result.Meta.InnerInstructions
            .SelectMany(inner => inner.Instructions) // 展平内层指令
            .Where(instr => instr.ProgramIdIndex == pumpFunIdIndex && instr.Accounts.Length == 1)
            .Select(instr => instr.Data).FirstOrDefault();
        var byteData = new ReadOnlySpan<byte>(Base58.Decode(data));
        var offset = 16;
        offset += 32;
        var solAmountUl = BitConverter.ToUInt64(byteData.Slice(offset, 8).ToArray(), 0);
        offset += 8;
        var amountUl = BitConverter.ToUInt64(byteData.Slice(offset, 8).ToArray(), 0);
        Console.WriteLine(solAmountUl);
        Console.WriteLine(amountUl);
    }

    async Task PumpFunRaydiumMigration()
    {
        var mint = "39azUYFWPz3VHgKCf3VChUwbpURdCHRxjWVowf5jUJjg";
        var apiKey = "e4d4820a-26ce-434a-8d90-c080bdd7c8a3";
        var url = $"{Constants.EnhancedWebsocketUrl}{apiKey}";

        using var ws = new EnhancedWebsocket(url);
        await ws.StartAsync();

        var config = new RpcTransactionsConfig
        {
            Filter = TransactionSubscribeFilter.Standard("39azUYFWPz3VHgKCf3VChUwbpURdCHRxjWVowf5jUJjg"),
            Options = TransactionSubscribeOptionsex.Default()
        };

        var result = await ws.TransactionSubscribeAsync(config);
        try
        {
            await foreach (var tx in result.NotificationChannel.Reader.ReadAllAsync())
            {
                Console.WriteLine(
                    $"Received transaction: Signature={tx.Signature}, Slot={tx.Slot}, Description={tx.Description}");
            }
        }
        finally
        {
            await result.Unsubscribe();
        }
    }

    async Task LaunchpadTest()
    {
        await streamingRpcClient.ConnectAsync();
        await streamingRpcClient.SubscribeProgramAsync(
            isDev ? "7soRSLviCKHCKzCbRuVpZDif76NWLVqFtbjt8LpyxWSq" : "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj",
            (state, value) =>
            {
                var publick = value.Value.PublicKey;
                var data = new ReadOnlySpan<byte>(Convert.FromBase64String(value.Value.Account.Data[0]));
                var offset = 8;
                var epoch = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var auth_bump = (int)data[offset];
                offset += 1;
                var status = (int)data[offset];
                offset += 1;
                var base_decimals = (int)data[offset];
                offset += 1;
                var quote_decimals = (int)data[offset];
                offset += 1;
                var migrate_type = (int)data[offset];
                offset += 1;
                var supply = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var total_base_sell = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var virtual_base = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var virtual_quote = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var real_base = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var real_quote = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var total_quote_fund_raising = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var quote_protocol_fee = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var platform_fee = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var migrate_fee = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                offset += 8 * 5;
                string global_config = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                string platform_config = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                string base_mint = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                string quote_mint = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                string base_vault = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                string quote_vault = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                string creator = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                Console.WriteLine($"{base_mint} {quote_mint} {publick}");
            }, Commitment.Confirmed, dataSize: 429);

        await streamingRpcClient.SubscribeProgramAsync("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", (state, value) =>
        {
            var pubkey = value.Value.PublicKey;
            var data = new ReadOnlySpan<byte>(Convert.FromBase64String(value.Value.Account.Data[0]));
            var offset = 0;
            var mint = Base58.Encode(data.Slice(offset, 32).ToArray()); //0 32
            offset += 32;
            var owner = Base58.Encode(data.Slice(offset, 32).ToArray()); //32 32
            offset += 32;
            ulong amount = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0); // 小端序64 8

            Console.WriteLine($"{pubkey} {mint} {amount}");
        }, dataSize: 165, memCmpList: new List<MemCmp>()
        {
            new MemCmp()
            {
                Offset = 32,
                Bytes = isDev
                    ? "DG6kZFFCqxdtWXw53Zc28hLs3MTr28Efkm2FrsNERNSQ"
                    : "WLHv2UAZm6z4KyaaELi5pjdbJh6RESMva1Rnn8pJVVh"
            }
        }, commitment: Commitment.Confirmed);
    }

    async Task AAbotParserTransaction()
    {
        //5QHQiFkgW5GAkn9RGbt9HTUh4cJ3vgrXk3CG32Sqvi3gxqsgMvxXp5yrTLfQX9vBa7GUwmhYMAu6SvoCxvcvh2aw
        //Launchpad buy
        //5eUooNiLxD7f7h5xFRsb3fZTm1x6krtQ95a5BWBN3mcXGfkUCdDQy8mKC12v2EBn4Md2wsavoQmHYWMGuWD4wteW
        //3QzKakbmWu89Tt6Syv8v9CkF8XGDcLCdDXDoLmTAbonk

        //RayAmm  sell
        //34fuFLAPeSki6auqeqjkyLU64j1VXHZ9x1owv6oqKvZ46WSUumN3Dmz5ZD5cExg1yMigrFyVtymhrNuSpdHkqC4Z
        //DAAq3yFRNjTEnfdTtnsGTDLkvEs4ckzYaTs39jYrGpJM

        //CPMM buy
        //4ChcqCjLswAGqxxKQuubU6f5djbsuHhqqUDjv9EdGsG92DRyg6HMzkGXERZQE9hUB9EMKkhPCUwEsTxuietfeWKP
        //5DrwoMaJiUVqhKGigGs7pkaYoLc1XUmtSiowKH7Gbonk

        //PumpSwap buy
        //5XRBCTbBBNZ4sPBC8SUnTGQ2Cb59jwwfDCCM93L8UXq63m9N2A9rWMbgZxysZPWYnJEMCs95G3TH14HeVA9FSivu
        //DQ7cBv3i5AF2Pu9U9nZrkcU1gDmqAg41dF5RH9JVpump

        //Pump sell
        //597diDzXZ6YY3TSzY8b79YtLnwNFicXQcR8A52VvCnDKFoqPGAWwBKTsQ25cbV9JCfCaGSP1aTp3Q7twjBjX2zhj
        //cYsse7aqbAmbZCFPNZ78cTDUz4fbZNtBHQWxSFwpump

        //DBC buy
        //28dEHstws2SUf81dVzRE8awMsk1qNdXHv4BfPeMpnZUuvUTMh69PLiVgzpHmEwA1rnPPFKmybquP3XiwotTrvUyq
        //DBC Sell
        //36wggFPsREotuqj7n98kFw8iugA2w3bx3HWZoNBSjsev2Ne2tFkNP4x7GZXRGcgUBLumubQEadG84B9WVaNYuxuJ
        //2gj9skmTc1sAVTXGTtG65DZX95ynTRyWN5yx3XuFpz6w

        //DLMM sell
        //2pS33Ans6RsR6v7dnGWVT1wTmKFF77shocWUCJus97GsxWkwvFgAt985vxtfW6Xpv4AV7q945YVzdWrTwzBgM5sT
        // buy
        //
        //ZRbG77CPg3nDteGnRrDwvbBNkygh9DvmP7n7Zm5d3Ks


        //5mdTQeHCRwsTRi44Kk1Wb5q1PjNPBeLExbNT6jhxVyMz
        //4W15cUWyP9G6Fg2NnZFJuNTudwNaYKh9nnX4YXbPcWPypXjNJx79W5cexjRsvRcFduMZJrsP2fHFew6DxMQVesaQ
        //59X7xVvkgR5SP6PrVC6JxiPUT8hwMNoyai2iATBYpwi474ixKaKW9hCCraTx5abAiMqfPm8XW6k31VPbp3TJmgdu


        //以下正式网
        //EvA88escD87zrzG7xo8WAM8jW6gJ5uQfeLL8Fj6DUZ2Q
        //32Eoa2QmYq5ecqCuVJtddm9E7cWo2V7FPoKiiMABknuAPwGXGLvBmjrrtQLQuQdCB3EL52jiMe9h2DCnwf8zZ2io
        //23o1Xns2zuCTAsSoUPto5wLZGkarBNiZkzAtEHpTM5YU
        //5oxWF1N6WDgDK3mPf3M7qgUiMAfChywtAgMH1KoRLDPHzvn4dawKSFYrw2KjQUpWMFJPCacgvLLbyn3Zg3bGudow
        //97PVGU2DzFqsAWaYU17ZBqGvQFmkqtdMywYBNPAfy8vy buy
        //23qVCa3p8SAoo3eiSk3zuXQF2zwwymwpPxzLfsKdMA5DSu4Be2AqM4NsRS45jwzhCKnSpB1zLWsBx6MxhugJQdNq
        var sign = "59X7xVvkgR5SP6PrVC6JxiPUT8hwMNoyai2iATBYpwi474ixKaKW9hCCraTx5abAiMqfPm8XW6k31VPbp3TJmgdu"; //


        var mainUrl = "https://devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3";
        var ws = "wss://devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3";
        var wss = "wss://atlas-devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3";
        var grpc = "https://solana-yellowstone-grpc.publicnode.com:443";
        var solapi = new SolanaApi(mainUrl, ws, wss, grpc, false, "", null);

        var (solAmount, tokenAmount) = await solapi.ParserTransaction(sign,
            "5mdTQeHCRwsTRi44Kk1Wb5q1PjNPBeLExbNT6jhxVyMz", DexType.MeteoraDYN, false);
        Console.WriteLine($"ok=>{solAmount} {tokenAmount}");
    }

    async Task BoopTest()
    {
        var data = new byte[]
        {
            6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
            145, 58, 140, 245, 133, 126, 255, 0, 169
        };
        var key = new PublicKey(data);
        await streamingRpcClient.ConnectAsync();
        await streamingRpcClient.SubscribeProgramAsync("boop8hVGQGqehUK2iVEMEnMrL5RbjywRzHKBmBE7ry4", (state, value) =>
        {
            var pubkey = value.Value.PublicKey;

            var offset = 8;
            var data = new ReadOnlySpan<byte>(Convert.FromBase64String(value.Value.Account.Data[0]));
            var creator = Base58.Encode(data.Slice(offset, 32).ToArray());
            offset += 32;
            var mint = Base58.Encode(data.Slice(offset, 32).ToArray());
            offset += 32;
            var virtual_sol_reserves = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
            offset += 8;
            var virtual_token_reserves = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
            offset += 8;
            var graduation_target = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
            offset += 8;
            var graduation_fee = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
            offset += 8;
            var sol_reserves = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
            offset += 8;
            var token_reserves = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
            offset += 8;
            var damping_term = (int)data[offset];
            offset++;
            var swap_fee_basis_points = data[offset];
            offset++;

            var scalingFactor = damping_term * (virtual_token_reserves / 1e9m);
            var tokensIssued = 1_000_000_000 * 1e9m - token_reserves / 1e9m;
            var baseDenominator = virtual_token_reserves / 1e9m - tokensIssued;
            var price = (decimal)((virtual_sol_reserves + sol_reserves) / 1e9m) / (decimal)(token_reserves / 1e9m);
            Console.WriteLine($"{mint} {pubkey} {price}");
        }, Commitment.Confirmed, dataSize: 125);
    }

    async Task BlockHashTest()
    {
        await streamingRpcClient.ConnectAsync();
        await streamingRpcClient.SubscribeSlotInfoAsync((state, value) =>
            {
                Console.WriteLine(JsonSerializer.Serialize(value));
            }
        );
    }

    async Task TokenAccountsTest()
    {
        var sign = "4CJFW8zFxPK1rjGvnFKW7yaf4ZGJxgmoRsjHo6yP4NAxesRq63YkW7WmqeRD9dXNksv7XGvf3TozbM7QNWr9D2Pp";
        var (solAmount, tokenAmount) = await new Tron.Abp.Multiplex.Solana
                .SolanaApi("https://devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3",
                    "wss://devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3",
                    "wss://atlas-devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3",
                    "https://solana-yellowstone-grpc.publicnode.com:443", false, "",
                    null)
            .ParserTransaction(sign, "jgHuZYwVr98cnqMNNNUhuimAnkXKsyMbAe4PqMrpump", "pumpfun", true);
        Console.WriteLine(sign);
        var mintResult = await RpcClient.GetTokenMintInfoAsync("J7rcqez959wwFszinh3XB98hbFccFj9CfUzXfKSxpump");
        var accounts = await RpcClient.GetProgramAccountsAsync("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
            Commitment.Confirmed,
            dataSize: 165, encoding: BinaryEncoding.JsonParsed, memCmpList: null);
        Console.WriteLine(accounts.Result.Count);
    }

    //await TokenAccountsTest();
    //await LaunchpadTest();
    //await PumpFunRaydiumMigration();
    /*PublicKey bondingCurveAddress = PDALookup.FindBondingPDA(new PublicKey(mint));
    var index = Array.IndexOf(accounts, bondingCurveAddress);
    var solAmount = isBuy switch
    {
        true => (tranInfo.Result.Meta.PostBalances[index] - tranInfo.Result.Meta.PreBalances[index]) / 1e9,
        false => (tranInfo.Result.Meta.PreBalances[index] - tranInfo.Result.Meta.PostBalances[index]) / 1e9,
    };*/

    //await PumpFunTranInfo();
    async Task clmmParserTransaction()
    {
        //clmm
        //var sign = "2PRqy6ZDLpPAP57jCYwHjaMrZEa7avKk8SQFpThdwMFZ9SmU5kp81zf3Buqpo66wqGFKXqi3wsCF85iB5btTzkzr";//卖
        //var sign = "3BziwSZLsY8wUR9PCJTDFJ4VHgdEYVxJu9i2QerPJMAQAPb3Hxepek8AsEZxdKMc2gMvF6TscySUs4ZPHK4GKkfy";//买
        //dev
        var sign = "KsoCF426yLBK8N8VQJXPxm6usNzqeQzVh9cDrA4WzVinLXpqs7bZz75ANz4HusXTZpH6jYQMytvVjexwjJx1Vsv";
        var req = await RpcClient.GetTransactionAsync(sign, Commitment.Confirmed);

        if (req.WasSuccessful)
        {
            var tranInfo = ((TransactionInfo)(req.Result.Transaction));
            var accountKeys = tranInfo.Message.AccountKeys;
            var programIndex = Array.IndexOf(accountKeys, "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
            var mintIndex = Array.IndexOf(accountKeys, "53u2Yr9k9qVCboQYxCbNkAMDqc5Vetzn7jnpJ5upnRai");
            var wsolIndex = Array.IndexOf(accountKeys, "So11111111111111111111111111111111111111112");
            var innerInstructions = req.Result.Meta.InnerInstructions;

            var tokenBalance =
                req.Result.Meta.PreTokenBalances.FirstOrDefault(it =>
                    it.Mint == "53u2Yr9k9qVCboQYxCbNkAMDqc5Vetzn7jnpJ5upnRai");
            if (tokenBalance == null)
            {
                tokenBalance =
                    req.Result.Meta.PostTokenBalances.FirstOrDefault(it =>
                        it.Mint == "53u2Yr9k9qVCboQYxCbNkAMDqc5Vetzn7jnpJ5upnRai");
            }

            var instructionMint = innerInstructions
                .Where(inner => inner?.Instructions != null)
                .SelectMany(inner => inner.Instructions)
                .FirstOrDefault(instr => instr != null &&
                                         instr.ProgramIdIndex == programIndex &&
                                         instr.StackHeight == 3 &&
                                         instr.Accounts != null &&
                                         instr.Accounts.Length == 4 &&
                                         instr.Accounts.Contains(mintIndex));

            var instructionWsol = innerInstructions
                .Where(inner => inner?.Instructions != null)
                .SelectMany(inner => inner.Instructions)
                .FirstOrDefault(instr => instr != null &&
                                         instr.ProgramIdIndex == programIndex &&
                                         instr.StackHeight == 3 &&
                                         instr.Accounts != null &&
                                         instr.Accounts.Length == 4 &&
                                         instr.Accounts.Contains(wsolIndex));


            if (instructionMint != null && instructionWsol != null)
            {
                var mintByteData = new ReadOnlySpan<byte>(Base58.Decode(instructionMint.Data));
                var tokenAmountUl = BitConverter.ToUInt64(mintByteData.Slice(1, 8).ToArray(), 0);
                var wsolByteData = new ReadOnlySpan<byte>(Base58.Decode(instructionWsol.Data));
                var solAmountUl = BitConverter.ToUInt64(wsolByteData.Slice(1, 8).ToArray(), 0);
                var tokenAmount = (decimal)tokenAmountUl / (decimal)Math.Pow(10, tokenBalance.UiTokenAmount.Decimals);
                var solAmount = (decimal)solAmountUl / 1e9m;
                Console.WriteLine(tokenAmount);
                Console.WriteLine(solAmount);
            }
        }

        Console.WriteLine();
    }

    async Task ParserTransaction()
    {
        //clmm
        //var sign = "2PRqy6ZDLpPAP57jCYwHjaMrZEa7avKk8SQFpThdwMFZ9SmU5kp81zf3Buqpo66wqGFKXqi3wsCF85iB5btTzkzr";//卖
        //var sign = "3BziwSZLsY8wUR9PCJTDFJ4VHgdEYVxJu9i2QerPJMAQAPb3Hxepek8AsEZxdKMc2gMvF6TscySUs4ZPHK4GKkfy";//买
        //dev
        var sign = "2AkucTEfvq8TA7rD6M3Hsq1qoQ7zkbuEkkggM516SzBrPVuuKBHFAgL2Y1ZY4GQnpCrMVRDWpqrkXuVyqL7JA3fo";
        var req = await RpcClient.GetTransactionAsync(sign, Commitment.Confirmed);

        if (req.WasSuccessful)
        {
            var tranInfo = ((TransactionInfo)(req.Result.Transaction));
            var accountKeys = tranInfo.Message.AccountKeys;
            var programIndex = Array.IndexOf(accountKeys, "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
            var mintIndex = Array.IndexOf(accountKeys, "3WouGWfMSMrf5PjUdRGzw71QwWoruSL8N15kWWmRb1t2");
            var wsolIndex = Array.IndexOf(accountKeys, "So11111111111111111111111111111111111111112");
            var innerInstructions = req.Result.Meta.InnerInstructions;

            var tokenBalance =
                req.Result.Meta.PreTokenBalances.FirstOrDefault(it =>
                    it.Mint == "3WouGWfMSMrf5PjUdRGzw71QwWoruSL8N15kWWmRb1t2");
            if (tokenBalance == null)
            {
                tokenBalance =
                    req.Result.Meta.PostTokenBalances.FirstOrDefault(it =>
                        it.Mint == "3WouGWfMSMrf5PjUdRGzw71QwWoruSL8N15kWWmRb1t2");
            }

            var instructionMint = innerInstructions
                .Where(inner => inner?.Instructions != null)
                .SelectMany(inner => inner.Instructions)
                .FirstOrDefault(instr => instr != null &&
                                         instr.ProgramIdIndex == programIndex &&
                                         instr.StackHeight == 3 &&
                                         instr.Accounts != null &&
                                         instr.Accounts.Length == 4 &&
                                         instr.Accounts.Contains(mintIndex));

            var instructionWsol = innerInstructions
                .Where(inner => inner?.Instructions != null)
                .SelectMany(inner => inner.Instructions)
                .FirstOrDefault(instr => instr != null &&
                                         instr.ProgramIdIndex == programIndex &&
                                         instr.StackHeight == 3 &&
                                         instr.Accounts != null &&
                                         instr.Accounts.Length == 4 &&
                                         instr.Accounts.Contains(wsolIndex));


            if (instructionMint != null && instructionWsol != null)
            {
                var mintByteData = new ReadOnlySpan<byte>(Base58.Decode(instructionMint.Data));
                var tokenAmountUl = BitConverter.ToUInt64(mintByteData.Slice(1, 8).ToArray(), 0);
                var wsolByteData = new ReadOnlySpan<byte>(Base58.Decode(instructionWsol.Data));
                var solAmountUl = BitConverter.ToUInt64(wsolByteData.Slice(1, 8).ToArray(), 0);
                var tokenAmount = (decimal)tokenAmountUl / (decimal)Math.Pow(10, tokenBalance.UiTokenAmount.Decimals);
                var solAmount = (decimal)solAmountUl / 1e9m;
                Console.WriteLine(tokenAmount);
                Console.WriteLine(solAmount);
            }
        }
    }

    //await ParserTransaction();

    async Task GetMintAccountInfo()
    {
        var mint = "DME1VmPZ23FExZrd3S9myAn8GNGX9U2YX9shjxVWhDVy";

        var response = await RpcClient.GetProgramAccountsAsync(TokenProgram.ProgramIdKey,
            Commitment.Confirmed, dataSize: 165,
            new List<MemCmp>()
            {
                new MemCmp()
                {
                    Offset = 0,
                    Bytes = mint
                }
            });
        Console.WriteLine(response.Result.Count);

        var responseLargest = await RpcClient.GetTokenLargestAccountsAsync(mint, Commitment.Confirmed);
        Console.WriteLine(responseLargest.Result.Value.Count);
    }

    async Task GetMintInfo()
    {
        var tokenMetdataId = new PublicKey("metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s");
        var mint = "DYc8TaPwdNBFGwBTdwHt6PnPNZR4pzR6wqatnqiepump"; //"755pCxbd98H1UGijh8wa2gzTuZfAfUAZutj53U2dbonk";//

        PublicKey.TryFindProgramAddress(
            new List<byte[]>()
            {
                Encoding.UTF8.GetBytes("metadata"),
                tokenMetdataId.KeyBytes,
                new PublicKey(mint).KeyBytes
            }, tokenMetdataId, out PublicKey metaAccount, out _);
        Console.WriteLine(metaAccount);
        var resp = await RpcClient.GetAccountInfoAsync(metaAccount, Commitment.Confirmed);
        if (resp.WasSuccessful)
        {
            ReadOnlySpan<byte> data = new ReadOnlySpan<byte>(Convert.FromBase64String(resp.Result.Value.Data[0]));
            var offset = 0;
            var key = data.GetU8(offset);
            offset += 1;
            var updateAuthority = data.GetPubKey(offset);
            offset += 32;
            var mintAddress = data.GetPubKey(offset);
            offset += 32;
            var nameLength = data.GetBorshString(offset, out var name);
            offset += nameLength;
            var symbolLength = data.GetBorshString(offset, out var symbol);
            offset += symbolLength;
            var uriLength = data.GetBorshString(offset, out var uri);
        }

        Console.WriteLine(resp);
    }

    //await GetMintInfo();
    //await BoopTest();

    async Task CreateTableAddress()
    {
        var rpcApi = new Tron.Abp.Multiplex.Solana
            .SolanaApi("https://devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3",
                "wss://devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3",
                "wss://atlas-devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3",
                "https://solana-yellowstone-grpc.publicnode.com:443", false, "",
                null);
        //var addresspump = await rpcApi.CreateLookupTableAddressPump(account);
        //Console.WriteLine("pump=>" + addresspump.ToString());

        //var addressraydium = await rpcApi.CreateLookupTableAddressRaydium(account);
        //Console.WriteLine("raydium=>" + addressraydium.ToString());

        var addressmeteora = await rpcApi.CreateLookupTableAddresMeteora(account);
        Console.WriteLine("meteora=>" + addressmeteora.ToString());
    }

    //await CreateTableAddress();

    async Task MeteoraDbcTest()
    {
        /*decimal GetPriceFromSqrtPrice(UInt128 bigSqrtPrice, int tokenADecimal, int tokenBDecimal)
        {

            var price=bigSqrtPrice*bigSqrtPrice;


            var result = price / UInt128.Parse("340282366920938463463374607431768211456");

            return 0m;
        }
        BigInteger ToBigInteger(UInt128 value)
        {
            byte[] bytes = new byte[16];
            for (int i = 0; i < 8; i++)
            {
                bytes[i] = (byte)(value >> (i * 8));
                bytes[i + 8] = (byte)(value >> ((i + 8) * 8));
            }
            return new BigInteger(bytes);
        }*/
        var programId = "dbcij3LWUppWqq96dh6gJWwBifmcGfLSB5D4DuSMaqN";
        var mint = "2gj9skmTc1sAVTXGTtG65DZX95ynTRyWN5yx3XuFpz6w";

        async Task 获取池子()
        {
            var requestResult = await RpcClient.GetProgramAccountsAsync(programId, Commitment.Confirmed, dataSize: 424,
                memCmpList: new List<MemCmp>()
                {
                    new MemCmp()
                    {
                        Bytes = mint,
                        Offset = 8 + 64 + 32 + 32
                    }
                });
            if (requestResult.WasHttpRequestSuccessful)
            {
            }
        }


        async Task Subsribe()
        {
            await streamingRpcClient.ConnectAsync();
            await streamingRpcClient.SubscribeProgramAsync(programId, (state, value) =>
            {
                var pubkey = value.Value.PublicKey;

                var accountData = new ReadOnlySpan<byte>(Convert.FromBase64String(value.Value.Account.Data[0]));
                var pool = Solnet.AABot.MeteoraDBC.DBCPool.Deserialize(accountData, 8);
                var sqrtPrice = ((Decimal)pool.SqrtPrice) / (Decimal)Math.Pow(2, 64);
                var price = sqrtPrice * sqrtPrice * (Decimal)(Math.Pow(10, 9 - 9));

                //var price = GetPriceFromSqrtPrice(pool.SqrtPrice,9,9);
                Console.WriteLine($"{pool.BaseMint} {pubkey} {price}");
            }, dataSize: 424, commitment: Commitment.Finalized);
        }

        await 获取池子();
    }

    //await MeteoraDbcTest();
    /*Dictionary<int, List<int>> DistributeWallets(List<int> ids)
    {
        var groupCount = 10;
        var totalWallets = ids.Count;
        int fullGroups = totalWallets / groupCount;
        int remainder = totalWallets % groupCount;
        int index = 0;
        int neededGroups = fullGroups + (remainder > 0 ? 1 : 0);
        var dict = new Dictionary<int, List<int>>();
        for (int i = 0; i < fullGroups && i < neededGroups - 1; i++)
        {
            dict[i] = (ids.Skip(index).Take(groupCount).ToList());
            index += 10;
        }

        if (remainder > 0 && dict.Keys.Count < neededGroups)
        {
            dict[neededGroups] = (ids.Skip(index).Take(remainder).ToList());
            index += remainder;
        }

        return dict;
    }


    var list = Enumerable.Range(21, 58).ToList();
    var dict = DistributeWallets(list);
    Console.WriteLine(dict.Count);
    foreach (var item in dict)
    {
        Console.WriteLine($"{item.Key}=>{string.Join(",", item.Value)}");
    }*/


    //await AAbotParserTransaction();


    async Task BurnTest()
    {
        var mainUrl = "https://devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3";
        var mainWs="wss://mainnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3";
        var mainWss="wss://atlas-mainnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3";
        var grpc = "https://solana-mainnet-grpc.publicnode.com:443";
        var solapi = new SolanaApi(mainUrl, mainWs, mainWss, grpc, false, "", null);
        RpcClient = ClientFactory.GetClient(mainUrl);
        var tokenAccounts = await solapi.GetTokenAccountByOwnerAsync(account.PublicKey);

        var tx = new List<TransactionInstruction>();
        
        //********************************************
        foreach (var tokenAccount in tokenAccounts)
        {
            tx = new List<TransactionInstruction>();
            var mint = new PublicKey(tokenAccount.Account.Data.Parsed.Info.Mint);
            var tc = new PublicKey(tokenAccount.PublicKey);
            //var stc=AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account.PublicKey, mint);
            Console.WriteLine($"{tokenAccount.PublicKey} {tokenAccount.Account.Data.Parsed.Info.Mint} {tokenAccount.Account.Data.Parsed.Info.TokenAmount.AmountUlong}");
            if (tokenAccount.Account.Data.Parsed.Info.TokenAmount.AmountUlong > 0)
            {
                tx.Add(TokenProgram.Burn(
                    tc,
                    mint,
                    tokenAccount.Account.Data.Parsed.Info.TokenAmount.AmountUlong,
                    account.PublicKey
                    ));
            }
            tx.Add(TokenProgram.CloseAccount(tc,account.PublicKey,account.PublicKey,TokenProgram.ProgramIdKey));
            if (tx.Count <= 0)
            {
                Console.WriteLine("没有要处理的代币");
                return;
            }
            var res = await RpcClient.GetLatestBlockHashAsync();
            LatestBlockHash latestBlockHash = res?.Result?.Value;
            if (latestBlockHash == null)
            {
                await Task.Delay(100);
                res = await RpcClient.GetLatestBlockHashAsync();
                latestBlockHash = res?.Result?.Value;
            }
            VersionedTransaction vtx = new VersionedTransaction
            {
                FeePayer = account,
                RecentBlockHash = latestBlockHash.Blockhash,
                Instructions = new(),
                AddressTableLookups = new()
            };
            vtx.Instructions.AddRange(tx);
            vtx.Sign(new List<Account>() { account });
            var tran = await RpcClient.SendTransactionAsync(vtx.Serialize(), commitment: Commitment.Confirmed);
            Console.WriteLine(tran.RawRpcResponse);
        }


    }

    await BurnTest();
}

async Task AABot测试()
{
    //photon 钱包密钥
    //4iEaCujmX1z7ManFXTUUdahaGpx1bSanAigTk3PLFQ14
    //39wVjJso3RdMAHAWSrr2iAQs1cEUifDhCCuC7BnqSSD2kkiLpSDoJga8CMBevjHB2CBkMVvLGNVSbGEYNJB1e7k2
    //2K4fSmuCQUp9oLrJS86vgnxPRmjTC5sJeh4f2e2nRXwX
    //5mZYtVnGp9X6FUQiGJdViUk923uYTtNnBrMQB2WAaHWTyrYceMXzjZ2Y4bnK6BfeFfKHps9Pen98jBpuUaQmhhb
    //3gySL1xTSjXwG4vZCE262apP3xZnuLU2HubFLzXJ2YX4
    //3cF5AaSAsCtYtNrJVW44hxEDYJEFmiXUvANZJ83R6SFsKGLGXyFL1Mcdd14QHFL9JJPCu5aCgtSTbqfVs9VbzF78
    var account =
        Account.FromSecretKey(
            "3cF5AaSAsCtYtNrJVW44hxEDYJEFmiXUvANZJ83R6SFsKGLGXyFL1Mcdd14QHFL9JJPCu5aCgtSTbqfVs9VbzF78");
    var RpcClient =
        ClientFactory.GetClient("https://mainnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3");
    var zeroSlotRpcClient =
        ClientFactory.GetClient("http://ams1.0slot.trade/?api-key=196a956de75f4564a61690c00cc26966");
    var IsDev = RpcClient.NodeAddress.Host.Contains("devnet");

    bool CheckRange(decimal value, decimal? min, decimal? max)
    {
        if (min.HasValue && value < min.Value) return false; // 小于最小值
        if (max.HasValue && value >= max.Value) return false; // 大于等于最大值
        return true; // 其他情况通过
    }

    async Task PumpFunTest()
    {
        //新代币 
        var mint = "acqTY82VrxYjowgbz1mUZ8aJPe5xQAJzZ4caszcpump"; //FQJt8AoqU6oAwWGfHhaB95RAhfBgiSuZdSyWsL4pump
        var price = 0.00000000000001m; //0.0000000280968836702577357192m;

        //
        IPumpFun pumpFun = new PumpFunImpl(RpcClient, account, null);
        //var (successful, sign, errorMsg) = await pumpFun.MultipleBuy(new List<Account>() { account1, account2 }, mint, 0.001m, 10);
        var (successful, sign, errorMsg) = await pumpFun.BuySell(mint, 0.01m, 10);


        Console.WriteLine(sign);
        //await Task.Delay(1000 * 5);
        //var (successful,sign,errorMsg)=await pumpFun.SellPercentage(mint,100,10);

        //await Task.Delay(500);
        //var (solAmount, tokenAmount) = await new Tron.Abp.Multiplex.Solana
        //    .SolanaApi("https://devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3", "wss://devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3", "wss://atlas-devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3", "https://solana-yellowstone-grpc.publicnode.com:443", null)
        //    .ParserTransaction(sign, "jgHuZYwVr98cnqMNNNUhuimAnkXKsyMbAe4PqMrpump", "pumpfun", true);
        //Console.WriteLine(sign);
    }

    async Task PumpFun0SoltTest()
    {
        var mint = "FuB4M8QS2RV65QJZ58dXekpLUjq2Sxq38rcsAUtRN7yU";
        IPumpFun pumpFun = new PumpFunImpl(RpcClient, zeroSlotRpcClient, account, null);
        var (successful, sign, errorMsg) = await pumpFun.SellPercentage(mint, 100, 10, 0.000005m, 0.001m);
        Console.WriteLine(sign);
    }
    //await PumpFunTest();

    async Task PumpFunTestToAccount()
    {
        var mint = "jgHuZYwVr98cnqMNNNUhuimAnkXKsyMbAe4PqMrpump";
        //account = Account.FromSecretKey("5mZYtVnGp9X6FUQiGJdViUk923uYTtNnBrMQB2WAaHWTyrYceMXzjZ2Y4bnK6BfeFfKHps9Pen98jBpuUaQmhhb");
        //var pumpFung = new PumpfunClient(RpcClient, account);

        IPumpFun pumpFun = new PumpFunImpl(RpcClient, account, null);
        var (success, sign, errorMsg) =
            await pumpFun.Buy(mint, 0.01m, 10);
        Console.WriteLine(sign);
    }

    //await PumpFunTestToAccount();
    async Task PumpSwapTest()
    {
        var mint = new PublicKey("6BNfFaox9iaF9sUDsfy1FMAz6qooaYfTJG6HbnKmpzvk");
        var wsol = new PublicKey("So11111111111111111111111111111111111111112");
        var price = 0.00000000000000001163m * 0.89m;
        //var _logger = (Microsoft.Extensions.Logging.ILogger)Log.Logger;
        IPumpSwap pumpSwap = new PumpAmmSwap(RpcClient, account, null);

        var coinValut =
            PumpSwapPDALookup.CoinCreatorVaultAuthority(new PublicKey("9kcXZGrodAD7vpzXfQW2bmxTJB6pcMoDje8jVEvGCPqz"));
        var coinValutAta = PumpSwapPDALookup.CoinCreatorVaultAta(coinValut);

        var accounts = new
        {
            pool = new PublicKey("3UL13WDZyCj4yE4sViCsHMNp3jFK8dGFi1vHWLZXGTBA"),
            baseMint = new PublicKey("6BNfFaox9iaF9sUDsfy1FMAz6qooaYfTJG6HbnKmpzvk"),
            quoteMint = new PublicKey("So11111111111111111111111111111111111111112"),
            poolBaseTokenAccount = new PublicKey("9XQPrD6oRB95qaTM5vrYvCDWT3dX7VcQJ9UdFhgRkcDS"),
            poolQuoteTokenAccount = new PublicKey("5rm5Ga38mdLHzpnXgZ6N4hfmVeyJiEShFHd3XdQRwfAr"),
            coinVault = coinValut,
            coinValutAta = coinValutAta,
        };
        var accountAddresses = new string[]
        {
            accounts.pool.Key,
            accounts.baseMint.Key,
            accounts.quoteMint.Key,
            accounts.poolBaseTokenAccount.Key,
            accounts.poolQuoteTokenAccount.Key,
            accounts.coinValutAta,
            accounts.coinVault.Key,
        };


        //rBKSe4iteZ5itTd9C2awpy4DL9GT6c5R4iDZzrUpump
        //9kcXZGrodAD7vpzXfQW2bmxTJB6pcMoDje8jVEvGCPqz 


        var (successful, sign, errorMsg) = await pumpSwap.Buy(mint, 0.01m, 10, accountAddresses: null);
        Console.WriteLine(sign);
        //await Task.Delay(1000 * 60);
        (successful, sign, errorMsg) = await pumpSwap.SellPercentage(mint, 100, 10, accountAddresses: null);
        Console.WriteLine(sign);
        (successful, sign, errorMsg) = await pumpSwap.BuySell(mint, 0.01m, 10);
        Console.WriteLine(sign);
    }

    async Task RaydiumAmmTest()
    {
        //9cJtgzyetN8yp46MkKQMa41gWu2xQvWq5Y6axkxnKPG8
        var mint = new PublicKey("6bgYFBCP5v6aYhq3kuCHJGrm3kP3XVaH8iSjAWssuoX8");

        /*var res=await RpcClient.GetProgramAccountsAsync(RaydiumAmmProgram.RAYDIUM_V4_PROGRAM_ID_Dev,
            Commitment.Confirmed, dataSize: 752, memCmpList: new List<MemCmp>
            {
                new MemCmp()
                {
                    Bytes = mint.Key, Offset = 400
                }
            });
        List<AmmInfo> resultingAccounts = new List<AmmInfo>(res.Result.Count);
        resultingAccounts.AddRange(res.Result.Select(result =>
            AmmInfo.Deserialize(Convert.FromBase64String(result.Account.Data[0]))));*/
        //var _logger = (Microsoft.Extensions.Logging.ILogger)Log.Logger;
        IRaydiumAMM raydiumAmm = new RaydiumAmmImpl(RpcClient, account, null);
        /*
         * const poolkey = {
             "id": "A73Z4EHWUaSrvL9AjFc22akNjenho2V2bYVafZNtSC5K",
             "programId": "HWy1jotHpo6UqeQxx49dpYYdQB8wj9Qk9MdxwjLvDHB8",
             "authority": "DbQqP6ehDYmeYjcBaMRuA8tAJY1EjDUz9DpwSLjaQqfC",
             "openOrders": "B3BmKapyDNvV3WmApMNjHUHXzX5wnaN7YPJimTMinfJy",
             "targetOrders": "2aqbKcqQWdCKEvxYo1KCwrNwnwaywbgr95xDnh2qmp4i",
             "baseVault": "HpSxNr18RLMUHaiGHY6Br9FZdCqRE3k4ManroRPVBCPB",
             "quoteVault": "9B8sy3mQaB93c7JaYsisijdvowcNdXEX8hNJ2VsQe2SM",
             "marketProgramId": "EoTcMgcDRTJVZDMZWBoU6rhYHZfkNTVEAfz3uUJRcYGj",
             "marketId": "D5iPRhi6sEjbpanrbxGVvxp3voNR5fZ1jtMGWDX2qBbB",
             "marketAuthority": "5oVTm27Rhrc3nAWzKer8sQdzeBoNvobzUnafzyKR4RDz",
             "marketBaseVault": "3ZJ13gwqa3pwu2TujrCxTcgiumkHZ7MQxkHg7ivdgsCn",
             "marketQuoteVault": "B6u8irkP6ANzPzTk8G32mrJY4NcuybFxYcq1V6j85hDs",
             "marketBids": "9mEd5yP4JyWwoMedR5xAfztoWWxHc7rTyNTNHHmrYQ1U",
             "marketAsks": "99xXpkeajWWeuCzD1fETpZ64DjEiGJ5FeqJpbZXqiUMB",
             "marketEventQueue": "6vAGNp6mHRRTB4FeqfBGympWJALW9m1rnzFeg7jUzmKV",
           };
         */
        //***********/1e9m
        //138527212906568325/1e9
        var price = (***********m / 138527212906568325m) * 0.89m;
        Console.WriteLine(price);
        var accountAddresses = new string[]
        {
            /*00*/"A73Z4EHWUaSrvL9AjFc22akNjenho2V2bYVafZNtSC5K",
            /*01*/"DbQqP6ehDYmeYjcBaMRuA8tAJY1EjDUz9DpwSLjaQqfC",
            /*02*/"B3BmKapyDNvV3WmApMNjHUHXzX5wnaN7YPJimTMinfJy",
            /*03*/"8eFhYH1gvtvv8bt4P4a8G2vk6RBHdvsp7ZafcpbbpqRj",
            /*04*/"HpSxNr18RLMUHaiGHY6Br9FZdCqRE3k4ManroRPVBCPB",
            /*05*/"9B8sy3mQaB93c7JaYsisijdvowcNdXEX8hNJ2VsQe2SM",
            /*06*/"EoTcMgcDRTJVZDMZWBoU6rhYHZfkNTVEAfz3uUJRcYGj",
            /*07*/"D5iPRhi6sEjbpanrbxGVvxp3voNR5fZ1jtMGWDX2qBbB",
            /*08*/"9mEd5yP4JyWwoMedR5xAfztoWWxHc7rTyNTNHHmrYQ1U",
            /*09*/"99xXpkeajWWeuCzD1fETpZ64DjEiGJ5FeqJpbZXqiUMB",
            /*10*/"6vAGNp6mHRRTB4FeqfBGympWJALW9m1rnzFeg7jUzmKV",
            /*11*/"3ZJ13gwqa3pwu2TujrCxTcgiumkHZ7MQxkHg7ivdgsCn",
            /*12*/"B6u8irkP6ANzPzTk8G32mrJY4NcuybFxYcq1V6j85hDs",
            /*13*/"1",
        };
        //var (successful, sign, errorMsg) = await raydiumAmm.SwapInAsync(
        //    mint, 0.01m, 10, accountAddresses: accountAddresses);
        //Console.WriteLine(sign);
        //await Task.Delay(1000 * 10);
        //var (successful, sign, errorMsg) = await raydiumAmm.SwapOutByPercentAsync(mint, 100, 10, accountAddresses: accountAddresses);
        //Console.WriteLine(sign);

        var (successful, sign, errorMsg) = await raydiumAmm.BuySell(mint, 0.01m, 10);
        Console.WriteLine(sign);
    }

    async Task R()
    {
        var (solAmount, tokenAmount) = await new Tron.Abp.Multiplex.Solana
                .SolanaApi("https://mainnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3",
                    "wss://devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3",
                    "wss://atlas-devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3",
                    "https://solana-yellowstone-grpc.publicnode.com:443", false, "", null)
            .ParserTransaction(
                "3eee7j2P38iCJdsoAirn5m6pwfRWMXHSr98aiAyPZsoRR4Gyc43Vgkp6t6Yhvv3pkws5SoYYXLH5EJVvceRHpGXC",
                "********************************************", "pumpfun-amm", false);
    }

    async Task RaydiumCpmmTest()
    {
        //var mint = new PublicKey("9rB7iA1Fs6Yv6aeVKj45CtaS3S5ENycQUoz5D6NPSNvZ"); //滑点 10 不过 代币
        //var mint = new PublicKey("8BBHH696gVv1S1nNd9zcsKUzvv9PTejoksstKiuRB7JE");
        var mint = new PublicKey("CAWXa1qdLhkphSJX6zhk9VG35sJHgqHtw7Y6cPKmnHfb");
        //var _logger = (Microsoft.Extensions.Logging.ILogger)Log.Logger;
        IRaydiumCpmm raydiumCpmm = new RaydiumCpmmImpl(RpcClient, account, null);

        var (successful, sign, errorMsg) = await raydiumCpmm.SwapInAsync(mint, 0.01m, 100);
        Console.WriteLine(sign);
        await Task.Delay(1000 * 10);
        (successful, sign, errorMsg) = await raydiumCpmm.SwapOutByPercentAsync(mint, 100, 100);
        Console.WriteLine(sign);
        (successful, sign, errorMsg) = await raydiumCpmm.BuySell(mint, 0.01m, 100);
        Console.WriteLine(sign);
    }

    async Task LaunchpadCreateMintTest()
    {
        var launchpad = new LaunchpadClient(RpcClient, account);
        var mint = Account.FromSecretKey(
            "3acCGQc2zbqqaLUs8iTXaBecjoXqSGK7BCkS5FAz78NvrVDWL12MVEtnU2g86Ct1ycZZ5wsFrfNrDkUA9zVhzXUa");
        var name = "A Beautiful Girl";
        var symbol = "AABG";
        var uri = "https://ipfs.io/ipfs/bafkreidkpno66z5enlhpnqs6bviexfupw47tclwydyg3u5tzguodzlk4ia";
        var res = await launchpad.CreateMint(mint, name, symbol, uri);
    }

    async Task LaunchpadTest()
    {
        var mint = Account.FromSecretKey(
            "3acCGQc2zbqqaLUs8iTXaBecjoXqSGK7BCkS5FAz78NvrVDWL12MVEtnU2g86Ct1ycZZ5wsFrfNrDkUA9zVhzXUa");
        var mintAddress = mint.PublicKey.Key;
        Solnet.AABot.Launchpad.IRayLaunchpad launchpad = new RayLaunchpadImpl(RpcClient, account, null);
        var (successful, sign, errorMsg) = await launchpad.SwapInAsync(mintAddress, 0.01m, 10);
        Console.WriteLine(sign);
        await Task.Delay(1000 * 5);
        (successful, sign, errorMsg) = await launchpad.SwapOutByPercentAsync(mintAddress, 100, 10);
        Console.WriteLine(sign);
        (successful, sign, errorMsg) = await launchpad.BuySell(mintAddress, 0.01m, 10);
        Console.WriteLine(sign);
    }

    async Task RaydiumClmmTest()
    {
        //BYDeiTV3VAfPqxME2FAphWTGkABBT2bWH2mcFAyMsDw8 成功
        Solnet.AABot.RaydiumClmm.IRaydiumClmm raydiumClmm = new RaydiumClmmImpl(RpcClient, account);
        //测试 代币 53u2Yr9k9qVCboQYxCbNkAMDqc5Vetzn7jnpJ5upnRai HbCwtyfnR9KqW1JM6VGaenpyQzPQ9ZytvSTPg3JUcM6C
        var mint = "53u2Yr9k9qVCboQYxCbNkAMDqc5Vetzn7jnpJ5upnRai"; // "HbCwtyfnR9KqW1JM6VGaenpyQzPQ9ZytvSTPg3JUcM6C";
        /*var programId = IsDev
            ? RaydiumClmmProgram.RAYDIUM_Clmm_PROGRAM_ID_Dev
            : RaydiumClmmProgram.RAYDIUM_Clmm_PROGRAM_ID;
        var ammConfig=IsDev ? RaydiumClmmProgram.AmmConfig_Dev:RaydiumClmmProgram.AmmConfig;
        var poolId = ClmmPda.GetClmmPoolId(programId, new PublicKey(mint),ammConfig).Address;
        var tickarray_bitmap_extension = ClmmPda.GetClmmExBitmap(programId, poolId).Address;
        Console.WriteLine(poolId);
        Console.WriteLine(tickarray_bitmap_extension);*/
        var (successful, sign, errorMsg) = await raydiumClmm.SwapInAsync(mint, 0.01m, 10);
        Console.WriteLine(sign);
        await Task.Delay(5 * 1000);
        /*(successful, sign, errorMsg) = await raydiumClmm.SwapOutByPercentAsync(mint, 100, 10);
        Console.WriteLine(sign);*/
        (successful, sign, errorMsg) = await raydiumClmm.BuySell(mint, 0.01m, 10);
        Console.WriteLine(sign);
    }


    async Task MeteoraDBCTest()
    {
        IMeteoraDBC meteoraDbc = new MeteoraDBCImpl(RpcClient, account, null);
        var mint = "2gj9skmTc1sAVTXGTtG65DZX95ynTRyWN5yx3XuFpz6w";

        var (successful, sign, errorMsg) = (false, string.Empty, string.Empty);

        (successful, sign, errorMsg) = await meteoraDbc.SwapInAsync(mint, 0.01m, 10);
        Console.WriteLine(sign);
        await Task.Delay(5 * 1000);
        (successful, sign, errorMsg) = await meteoraDbc.SwapOutByPercentAsync(mint, 100, 10);
        Console.WriteLine(sign);
        (successful, sign, errorMsg) = await meteoraDbc.BuySell(mint, 0.01m, 10);
        Console.WriteLine(sign);
    }

    async Task MeteoraDlmmTest()
    {
        IMeteoraDlmm meteoraDlmm = new MeteoraDlmmImpl(RpcClient, account, null);
        //var mint = "ZRbG77CPg3nDteGnRrDwvbBNkygh9DvmP7n7Zm5d3Ks";//可成功交易
        var mint = "FK3dBRFT32Qt9UhsebGZZEJzHQ7PaTU3arMdiZc6W6kZ";
        var (successful, sign, errorMsg) = await meteoraDlmm.SwapInAsync(mint, 0.01m, 20);
        Console.WriteLine(sign);
        await Task.Delay(5 * 1000);
        (successful, sign, errorMsg) = await meteoraDlmm.SwapOutByPercentAsync(mint, 100, 20);
        Console.WriteLine(sign);
        (successful, sign, errorMsg) = await meteoraDlmm.BuySell(mint, 0.01m, 20);
        Console.WriteLine(sign);
    }

    async Task MeteoraDynTest()
    {
        IMeteoraDyn meteoraDyn = new MeteoraDynImpl(RpcClient, account, null);
        var mint = "5mdTQeHCRwsTRi44Kk1Wb5q1PjNPBeLExbNT6jhxVyMz";
        var (successful, sign, errorMsg) = await meteoraDyn.SwapInAsync(mint, 0.01m, 100);
        Console.WriteLine(sign);
        await Task.Delay(5 * 1000);
        (successful, sign, errorMsg) = await meteoraDyn.SwapOutByPercentAsync(mint, 100, 100);
        Console.WriteLine(sign);
        (successful, sign, errorMsg) = await meteoraDyn.BuySell(mint, 0.01m, 100);
        Console.WriteLine(sign);
    }

    //await MeteoraDynTest();
    //await RaydiumClmmTest();
    //await LaunchpadTest();
    //await RaydiumCpmmTest();
    //await R();
    //await PumpFunTest();
    //await RaydiumAmmTest();
    //await PumpSwapTest();
    await PumpFun0SoltTest();
}

async Task RayCPMM测试()
{
    var cpmmId = new PublicKey("CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C");
    var mint0 = new PublicKey("58rDDRFs7WDHv1VUwPgEawD5ScQ9MJv8YFxRZrM2HQpF");
    var wsol = new PublicKey("So11111111111111111111111111111111111111112");
    ushort ammConfigIndex = 0; // u16 等价于 C# 的 ushort

    // 将 ushort 转换为大端字节数组 (to_be_bytes)
    byte[] indexBytes = BitConverter.GetBytes(ammConfigIndex);
    if (BitConverter.IsLittleEndian)
    {
        Array.Reverse(indexBytes); // 转换为大端序
    }

    PublicKey.TryFindProgramAddress(
        [
            Encoding.UTF8.GetBytes("amm_config"),
            indexBytes,
        ],
        cpmmId, out PublicKey amm_config_key, out _);
    Console.WriteLine(amm_config_key.Key);
    PublicKey.TryFindProgramAddress(
        [
            Encoding.UTF8.GetBytes("pool"),
            amm_config_key.KeyBytes,
            wsol.KeyBytes,
            mint0.KeyBytes
        ],
        cpmmId, out PublicKey pool_account_key, out _);
    Console.WriteLine(pool_account_key.Key);
    PublicKey.TryFindProgramAddress(
        [
            Encoding.UTF8.GetBytes("vault_and_lp_mint_auth_seed"),
        ],
        cpmmId, out PublicKey authority, out _);
    Console.WriteLine(authority.Key);
    PublicKey.TryFindProgramAddress(
        [
            Encoding.UTF8.GetBytes("pool_vault"),
            pool_account_key.KeyBytes,
            wsol.KeyBytes,
        ],
        cpmmId, out PublicKey token_0_vault, out _);
    Console.WriteLine(token_0_vault.Key);
    PublicKey.TryFindProgramAddress(
        [
            Encoding.UTF8.GetBytes("pool_vault"),
            pool_account_key.KeyBytes,
            mint0.KeyBytes,
        ],
        cpmmId, out PublicKey token_1_vault, out _);
    Console.WriteLine(token_1_vault.Key);
    PublicKey.TryFindProgramAddress(
        [
            Encoding.UTF8.GetBytes("observation"),
            pool_account_key.KeyBytes,
        ],
        cpmmId, out PublicKey observation_key, out _);


    Console.WriteLine(observation_key.Key);
}

async Task Helius测试()
{
    var key = new byte[]
    {
        140,
        151,
        37,
        143,
        78,
        36,
        137,
        241,
        187,
        61,
        16,
        41,
        20,
        142,
        13,
        131,
        11,
        90,
        19,
        153,
        218,
        255,
        16,
        132,
        4,
        142,
        123,
        216,
        219,
        233,
        248,
        89
    };
    var pubkey = new PublicKey(key); //ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL
    var streamingClient =
        Tran.Abp.Ws.Core.HeliusClientFactory.GetStreamingClient(
            "wss://mainnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3");
    await streamingClient.ConnectAsync();
    await streamingClient.SubscribeProgramAsync(pubkey,
        (state, value) => { Console.WriteLine(value.Value.PublicKey); }, dataSize: 165
        ,
        commitment: Commitment.Confirmed);

    //await streamingClient.DisconnectAsync();
    Console.WriteLine();
}


async Task Raydiumclmm测试()
{
    var client =
        ClientFactory.GetClient("https://mainnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3");
    var sign = "3kgYxjt6hso2EUVpii89vAR8tYccxBkUggj4pakbFtWoPb2etcwwUxdj3MezZK6gYPThPdQnPoKmXiRdj2wEaXNM";
    //var result1 = await client.GetTransactionAsync(sign, Commitment.Confirmed);
    //dev clmm id       devi51mZmdwUJGU9hjN27vEz64Gps7uUefqxg27EAtH  DyaMTwvPTRJf6TaTrZtTDMwSQJ67RGrmRq6Y2TK6d979
    //dev amm config    CQYbhr6amxUER4p5SC44C63R4qw4NFc9Z4Db9vF4tZwG 
    //dev clmm mint HbCwtyfnR9KqW1JM6VGaenpyQzPQ9ZytvSTPg3JUcM6C

    var mint0 = new PublicKey("HXAu6YGb9WnU9LSjck45R6v6f7NLZooiqonRMa6Vu1e6");
    var mint1 = new PublicKey("So11111111111111111111111111111111111111112");
    var clmmId = new PublicKey("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK");
    var pool_state_key = new PublicKey("9SgWVQcQT8thfaBoSkbjqkcRW2jzAkp9WJNqiaNuQbmF");
    var pool_config_key = new PublicKey("9iFER3bpjf1PTTCQCfTRu17EJgvsxo9pVyA9QWwEuX4x");

    AmmConfig pool_config_state;
    RaydiumClmmPoolAccounts pool_state;
    PublicKey.TryFindProgramAddress(new List<byte[]>()
        {
            Encoding.UTF8.GetBytes("pool_tick_array_bitmap_extension"),
            pool_state_key.KeyBytes
        },
        clmmId,
        out PublicKey tickarray_bitmap_extension,
        out _);

    var res = await client.GetMultipleAccountsAsync(new List<string>()
    {
        pool_state_key, pool_config_key, tickarray_bitmap_extension
    }, Commitment.Confirmed);

    if (res.WasSuccessful && res.Result != null && res.Result.Value != null)
    {
        pool_state = RaydiumClmmPoolAccounts.Deserialize(Convert.FromBase64String(res.Result.Value[0].Data[0]));
        pool_config_state = AmmConfig.Deserialize(Convert.FromBase64String(res.Result.Value[1].Data[0]));
    }
    /*
    var ammConfigRes =
        await client.GetAccountInfoAsync(pool_config_key.Key, Commitment.Confirmed);
    var zero_for_one = true;//表标 wsol=>token
    if (ammConfigRes.WasSuccessful && ammConfigRes.Result != null && ammConfigRes.Result.Value != null)
    {
        //var ammConfig=ammConfigRes.Result.Value;
        var ammConfigData = new ReadOnlySpan<byte>(Convert.FromBase64String( ammConfigRes.Result.Value.Data[0]));
        pool_config_state = AmmConfig.Deserialize(ammConfigData);

    }*/

    //pool_tick_array_bitmap_extension
    //3hAa8EzhZA3o3n88KLJzE3aQdmNugcJgNuL3BHyqd8C1

    Console.WriteLine(tickarray_bitmap_extension.Key);


    var poolInfoRequestResult = await client.GetProgramAccountsAsync("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK",
        dataSize: 1544, commitment: Commitment.Confirmed, memCmpList: new List<MemCmp>
        {
            new MemCmp()
            {
                Bytes = "PriNtiE7V98rC4Vzvns696BFjDxwGDuC2a8qinnjEYj", Offset = 8 + 1 + 32 + 32 + 32
            }
        });
    if (poolInfoRequestResult.WasSuccessful && poolInfoRequestResult.Result != null &&
        poolInfoRequestResult.Result.Count > 0)
    {
        var poolInfo =
            RaydiumClmmPoolAccounts.Deserialize(
                Convert.FromBase64String(poolInfoRequestResult.Result[0]?.Account.Data[0]));
    }

    var pool = await client.GetAccountInfoAsync("9NwM2ZmsV2gXMEqmkeFJ8VH8QCEd8CTuZbzz8H2KWhEL");
    var poolData = new ReadOnlySpan<byte>(Convert.FromBase64String(pool.Result.Value.Data[0]));
    var offset = 253;
    var sqrtPriceX64Bytes = (poolData.Slice(offset, 16).ToArray());
    string hexString = string.Join(" ", sqrtPriceX64Bytes.Select(b => b.ToString("X2")));
    Console.WriteLine(hexString);
    var sqrtPriceX64Low = BitConverter.ToUInt64(sqrtPriceX64Bytes, 0); // 前 8 个字节
    var sqrtPriceX64High = BitConverter.ToUInt64(sqrtPriceX64Bytes, 8); // 后 8 个字节
    var sqrtPriceX64 = BitConverter.ToUInt64(poolData.Slice(offset, 16).ToArray(), 0);
    var sqrtPriceX64Full = (BigInteger)sqrtPriceX64High << 64 | sqrtPriceX64Low;
    decimal sqrtPriceX64Decimal = sqrtPriceX64High + sqrtPriceX64 / (decimal)Math.Pow(2, 64);
    decimal price1 = 1 / (sqrtPriceX64Decimal * sqrtPriceX64Decimal * 1e3m);
    var account1 = await client.GetTokenAccountInfoAsync("6P4tvbzRY6Bh3MiWDHuLqyHywovsRwRpfskPvyeSoHsz");
    var amount1 = account1.Result.Value.Data.Parsed.Info.TokenAmount.AmountDecimal;
    var account2 = await client.GetTokenAccountInfoAsync("6mK4Pxs6GhwnessH7CvPivqDYauiHZmAdbEFDpXFk9zt");
    var amount2 = account2.Result.Value.Data.Parsed.Info.TokenAmount.AmountDecimal;
    var price2 = amount1 / amount2;
    Console.WriteLine($"{price1},{price2}");
}

async Task PumpSwap测试()
{
    var account =
        Account.FromSecretKey(
            "3cF5AaSAsCtYtNrJVW44hxEDYJEFmiXUvANZJ83R6SFsKGLGXyFL1Mcdd14QHFL9JJPCu5aCgtSTbqfVs9VbzF78");
    //pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA Hpc4Z7TjmZw4aUrEnZSy2DDR3z7BxrwtvF2TEXvVpump 
    //675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8 5YxuYx49UJBdrF8zMLUZDk5vqcyxez9EafGHErKpump
    var RpcClient =
        ClientFactory.GetClient("https://devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3");
    /*var list = new List<MemCmp> { new MemCmp { Bytes = "232ce9fVFsyT6B7o71XcyBHSU5Q5iFvU5FLhoYFKLFg6",Offset = 8+1+2+32} };
    var res = await RpcClient.GetProgramAccountsAsync("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA",
        Commitment.Confirmed, memCmpList: list,dataSize:211);

    IPumpSwap pumpSwap = new PumpAmmSwap(RpcClient, account);




    var sing = await pumpSwap.SellPercentage(mint, 100, 10);*/
    //var mint = "232ce9fVFsyT6B7o71XcyBHSU5Q5iFvU5FLhoYFKLFg6";
    var mint = "jgHuZYwVr98cnqMNNNUhuimAnkXKsyMbAe4PqMrpump";
    var _logger = (Microsoft.Extensions.Logging.ILogger)Log.Logger;
    IPumpFun pumpFun = new PumpFunImpl(RpcClient, account, _logger);
    //var sign=await pumpFun.Buy(mint,0.01m,10);
    //Console.WriteLine(sign);
    var sign = await pumpFun.SellPercentage(mint, 100, 10);
    Console.WriteLine(sign);
    return;
    var streamingClient =
        ClientFactory.GetStreamingClient("wss://mainnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3");

    //var pump =new Solnet.Pumpfun.PumpfunClient(RpcClient,account);
    //var sign =await pump.Buy(mint, 0.01m, 10);
    var dataUrl = "https://ipfs.io/ipfs/QmVaSahN8xcyqgWJwFiiJj7H1VTVJMw32fDnHGiJQRD6kk";
    var mintAccount =
        Account.FromSecretKey(
            "5z78qS6WuYS6aoAg582DDNPx6D1EmABrtpKUbuHqjoSBJYMMjRZR6NrvFwYY1BA9hF5himbUH5qqYe4Z1nGKPPT4");
    await streamingClient.ConnectAsync();
    await streamingClient.SubscribeProgramAsync("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK",
        (state, value) =>
        {
            var publicKey = value.Value.PublicKey;
            var data = new ReadOnlySpan<byte>(Convert.FromBase64String(value.Value.Account.Data[0]));
            var offset = 8 + 1;
            var ammConfig = Base58.Encode(data.Slice(offset, 32).ToArray());
            offset += 32;
            var owner = Base58.Encode(data.Slice(offset, 32).ToArray());
            offset += 32;
            var tokenMint0 = Base58.Encode(data.Slice(offset, 32).ToArray());
            offset += 32;
            var tokenMint1 = Base58.Encode(data.Slice(offset, 32).ToArray());
            offset += 32;
            var tokenVault0 = Base58.Encode(data.Slice(offset, 32).ToArray());
            offset += 32;
            var tokenVault1 = Base58.Encode(data.Slice(offset, 32).ToArray());
            offset += 32;
            var observationKey = Base58.Encode(data.Slice(offset, 32).ToArray());
            offset += 32;
            var mintDecimals0 = BitConverter.ToUInt64(data.Slice(offset, 1).ToArray(), 0);
            offset += 1;
            var mintDecimals1 = BitConverter.ToUInt64(data.Slice(offset, 1).ToArray(), 0);
            offset += 1;
            var tickSpacing = BitConverter.ToUInt64(data.Slice(offset, 2).ToArray(), 0);
            offset += 2;
            var liquidity = BitConverter.ToUInt64(data.Slice(offset, 16).ToArray(), 0);
            offset += 16;
            //var sqrtPriceX64 = BitConverter.ToUInt64(data.Slice(offset, 16).ToArray(), 0);
            var sqrtPriceX64Bytes = (data.Slice(offset, 16).ToArray());
            var sqrtPriceX64Low = BitConverter.ToUInt64(sqrtPriceX64Bytes, 0); // 前 8 个字节
            var sqrtPriceX64High = BitConverter.ToUInt64(sqrtPriceX64Bytes, 8); // 后 8 个字节
            decimal sqrtPriceX64Decimal =
                (decimal)sqrtPriceX64High + (decimal)sqrtPriceX64Low / (decimal)Math.Pow(2, 64);
            decimal price = sqrtPriceX64Decimal * sqrtPriceX64Decimal * 1e3m;
            offset += 16;
            var tickCurrent = BitConverter.ToInt32(data.Slice(offset, 4).ToArray(), 0);
            Console.WriteLine(publicKey + "=>" + JsonSerializer.Serialize(value.Value));
        }, dataSize: 1544, commitment: Commitment.Confirmed);

    await Task.Delay(1000 * 60);
    await streamingClient.DisconnectAsync();
    await Task.Delay(1000);
    await streamingClient.ConnectAsync();
    return;
    //var dd = Convert.FromBase64String("f17da5ce06814f2fa1a4edfa8106fbb19bab87b4369638af84a7dd3bcad5c52a");

    //var sign = await pump.CreateMintPump("AICHI COIN", "AIC", dataUrl, mintAccount.PrivateKey.Key, null);
    var accInfo = await RpcClient.GetMultipleAccountsAsync(new List<string>()
    {
        "4vDmqnKLN2jdPGR2DMf5L6C93AG4XbHdfRAXJuironK8", "5mDDjsgR9HQGFjHGy1cZ7fNYMzqkZ9hBeAJbjkcTZgCt",
    });

    if (accInfo.WasSuccessful)
    {
        var amounts = new decimal[2];
        foreach (var info in accInfo.Result.Value)
        {
            var accountData = new ReadOnlySpan<byte>(Convert.FromBase64String(info.Data[0]));
            var offset = 0;
            var acc_mint = Base58.Encode(accountData.Slice(offset, 32).ToArray()); //0 32
            offset += 32;
            var acc_owner = Base58.Encode(accountData.Slice(offset, 32).ToArray()); //32 32
            offset += 32;
            ulong acc_amount = BitConverter.ToUInt64(accountData.Slice(offset, 8).ToArray(), 0); // 小端序64 
            if (acc_mint == "So11111111111111111111111111111111111111112") amounts[0] = acc_amount / 1e9m;
            else amounts[1] = acc_amount / 1e6m;
        }

        var price = amounts[0] / amounts[1];
    }

    var mems = new List<MemCmp>()
        { new MemCmp() { Offset = 0, Bytes = "BVjakiqygiuKmSu4y7SpMEk2ZWrnyBdtC7fA6EqUpump" } };
    var pools = await RpcClient.GetProgramAccountsAsync("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        Commitment.Confirmed, memCmpList: mems);

    var result1 =
        await RpcClient.GetTransactionAsync(
            "3pcmhZVMiNtetF1p22YfuyPxMZNEuVX24RBcXDtm73x6nCG9UiNqea2pGkAymV1E5WuPN8HPW3MHUV5acwKE9yuD");

    var result2 =
        await RpcClient.GetTransactionAsync(
            "4vAdovPgt3m5urjM4VDCbGaEo4Upw3BW1Ca4VTa3bHJL8KXeNDeNM3qjsGGiHC31t28z3okgYxjsU47vzhUVsEFY");
    var result3 =
        await RpcClient.GetTransactionAsync(
            "HNbXjbAFgzZ3zSqMCXygHUNZ98ustLpeqS1jxE1QVjPWzy9MHk44ptiS7BatENUGgBmyfGdQ9GmDK5nZhpUsgMa");
}

async Task Work测试()
{
    var queue = QueueManager.CreateQueue<int>(async (i) =>
    {
        Console.WriteLine(i);
        await Task.CompletedTask;
    }, 10);

    for (int i = 0; i < 1000; i++)
    {
        queue.Enqueue(i);
    }

    await queue.StopAsync();
}


async Task SolanaWsPrice()
{
    streamingRpcClient =
        ClientFactory.GetStreamingClient("wss://mainnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3");
    await streamingRpcClient.ConnectAsync();
    /*var pump = await streamingRpcClient.SubscribeProgramAsync("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
        (state, value) =>
        {
            //Console.WriteLine(value.Value.PublicKey);
            var pubkey = value.Value.PublicKey;
            var accountData = new ReadOnlySpan<byte>(Convert.FromBase64String(value.Value.Account.Data[0]));
            var offset = 8;
            var virtualTokenReserves = BitConverter.ToUInt64(accountData.Slice(offset, 8).ToArray(), 0);
            offset += 8;
            var virtualSolReserves = BitConverter.ToUInt64(accountData.Slice(offset, 8).ToArray(), 0);
            offset += 8;
            var realTokenReserves = BitConverter.ToUInt64(accountData.Slice(offset, 8).ToArray(), 0);
            offset += 8;
            var realSolReserves = BitConverter.ToUInt64(accountData.Slice(offset, 8).ToArray(), 0);
            offset += 8;
            var tokenTotalSupply = BitConverter.ToUInt64(accountData.Slice(offset, 8).ToArray(), 0);
            offset += 8;
            var complete = accountData[offset] == 1;
            var tokenPrice = (decimal)(virtualSolReserves / 1e9) / (decimal)(virtualTokenReserves / 1e6);
            Colorful.Console.WriteLine($"{pubkey} {tokenPrice}");
        }, dataSize: 49);

    var raydium = await streamingRpcClient.SubscribeProgramAsync("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        (state, value) =>
        {
            var pubkey = value.Value.PublicKey;
            var accountData = new ReadOnlySpan<byte>(Convert.FromBase64String(value.Value.Account.Data[0]));
            var offset = 0;
            var mint =Base58.Encode(accountData.Slice(offset, 32).ToArray()); //0 32
            offset += 32;
            var owner = Base58.Encode(accountData.Slice(offset, 32).ToArray()); //32 32
            offset += 32;
            ulong amount =BitConverter.ToUInt64( accountData.Slice(offset, 8).ToArray(),0); // 小端序64 8
            offset += 8;
            var delegateOption =BitConverter.ToUInt32( accountData.Slice(offset, 4).ToArray(),0); //72 4
            offset += 4;
            var delegatePubkey = delegateOption == 1 ?  Base58.Encode(accountData.Slice(offset, 32).ToArray()) : null; //76 32
            offset += 32;
            //var state =(int)accountData[offset]; //108 1
            offset += 1;
            var isNativeOption = BitConverter.ToUInt32( accountData.Slice(offset, 4).ToArray(),0); //109 4
            offset += 4;
            var isNative = BitConverter.ToUInt64( accountData.Slice(offset, 8).ToArray(),0); //113 8
            offset += 8;
            var delegatedAmount = BitConverter.ToUInt64( accountData.Slice(offset, 8).ToArray(),0); //121 8
            offset += 8;
            var closeAuthorityOption =BitConverter.ToUInt32( accountData.Slice(offset, 4).ToArray(),0); //129 4
            offset += 4;
            var closeAuthority = closeAuthorityOption == 1 ? Base58.Encode(accountData.Slice(offset, 32).ToArray()) : null;
            Colorful.Console.WriteLine($"[raydium]{pubkey} {mint} {amount}");
        }, dataSize: 165, memCmpList: new List<MemCmp>()
        {
            new MemCmp() { Offset = 32, Bytes = "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1" }
        });*/
    var pool = await streamingRpcClient.SubscribeProgramAsync("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8",
        (state, value) =>
        {
            var pubkey = value.Value.PublicKey;
            var accountData = new ReadOnlySpan<byte>(Convert.FromBase64String(value.Value.Account.Data[0]));
            var offset = 32;
            ulong baseDecimal = BitConverter.ToUInt64(accountData.Slice(offset, 8).ToArray(), 0);
            offset += 8;
            ulong quoteDecimal = BitConverter.ToUInt64(accountData.Slice(offset, 8).ToArray(), 0);
            offset = 336;
            string baseVault = Base58.Encode(accountData.Slice(offset, 32).ToArray());
            offset += 32;
            string quoteVault = Base58.Encode(accountData.Slice(offset, 32).ToArray());
            offset += 32;
            string baseMint = Base58.Encode(accountData.Slice(offset, 32).ToArray());
            offset += 32;
            string quoteMint = Base58.Encode(accountData.Slice(offset, 32).ToArray());

            Colorful.Console.WriteLine($"{pubkey} {baseVault} {quoteVault} {baseMint} {quoteMint}");
        }, dataSize: 752, commitment: Commitment.Confirmed);
}


async Task PumpWebSocket测试TouchWs()
{
    NameValueCollection header = new NameValueCollection();
    // 使用 Add 方法添加键值对
    //header.Add("Content-Type", "application/json");
    header.Add("Origin", "https://pump.fun");
    //header.Add("Connection", "Upgrade");
    //header.Add("Sec-Websocket-Version", "13");
    header.Add("Sec-Websocket-Extensions",
        "permessage-deflate; client_no_context_takeover; server_no_context_takeover;client_max_window_bits");
    header.Add("User-Agent", "Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0");
    SolanaConsoleApp.IWebSocketClient _webSocketClient =
        new SolanaConsoleApp.WebSocketClient("wss://prod-v2.nats.realtime.pump.fun/", header);
    bool isRunning = false;
    _webSocketClient.OnConnected += async () => { };
    _webSocketClient.OnDisconnected += (e) => { };
    _webSocketClient.OnMessageReceived += async (e) =>
    {
        //
        var msg = Encoding.UTF8.GetString(e);
        Console.WriteLine(msg);
        if (msg.StartsWith("INFO"))
        {
            //CONNECT {"no_responders":true,"protocol":1,"verbose":false,"pedantic":false,"user":"subscriber","pass":"lW5a9y20NceF6AE9","lang":"nats.ws","version":"1.29.2","headers":true}
            // 
            var connect =
                "CONNECT {\"no_responders\":true,\"protocol\":1,\"verbose\":false,\"pedantic\":false,\"user\":\"subscriber\",\"pass\":\"lW5a9y20NceF6AE9\",\"lang\":\"nats.ws\",\"version\":\"1.29.2\",\"headers\":true}\r\n";
            await _webSocketClient.SendStringAsync(connect);
            var ping = "PING\r\n";
            await _webSocketClient.SendStringAsync(ping);
        }

        if (msg.StartsWith("PING"))
        {
            var pong = $"PONG\r\n";
            await _webSocketClient.SendStringAsync(pong);
        }

        if (msg.StartsWith("PONG"))
        {
            if (!isRunning)
            {
                var sub = "SUB newCoinCreated.prod 1\r\n";
                await _webSocketClient.SendStringAsync(sub);
                isRunning = true;
            }
        }

        //解析消息
        if (msg.StartsWith("MSG"))
        {
            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
            var arry = msg.Split("\r\n");
            if (arry.Length >= 2)
            {
                Console.WriteLine(arry[1]);
            }
        }
    };

    await _webSocketClient.ConnectAsync();
}

async Task 合约测试()
{
    var account =
        Account.FromSecretKey(
            "3cF5AaSAsCtYtNrJVW44hxEDYJEFmiXUvANZJ83R6SFsKGLGXyFL1Mcdd14QHFL9JJPCu5aCgtSTbqfVs9VbzF78");
    rpcClient = ClientFactory.GetClient("https://mainnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3");
    var mint = "2HZdRs4Cxkg1WbiPxPZcramcEPo22dzeemMAYZhZpump";
    var list = new List<MemCmp>
    {
        new MemCmp { Bytes = RaydiumAmmProgram.WSOL, Offset = 8 * 34 + 16 * 4 + 32 * 2 },
        new MemCmp { Bytes = mint, Offset = 8 * 34 + 16 * 4 + 32 * 3 }
    };
    var pools = await rpcClient.GetProgramAccountsAsync("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8",
        Commitment.Confirmed, 752, list);
    if (pools.WasSuccessful && pools.Result != null && pools.Result.Count() > 0)
    {
        List<AmmInfo> resultingAccounts = new List<AmmInfo>(pools.Result.Count);
        resultingAccounts.AddRange(pools.Result.Select(result =>
            AmmInfo.Deserialize(Convert.FromBase64String(result.Account.Data[0]))));
    }

    var poolkey = new Dictionary<string, object>
    {
        { "id", "GW6J1Uv3pwcCRH1TwBJGhcW24LLEzknag6N7PfvHEoai" },
        { "baseMint", "4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU" },
        { "quoteMint", "So11111111111111111111111111111111111111112" },
        { "lpMint", "G4CtdBhsXHAiuZ8a7iWduQg3iecrUaUrxVCVcbAN6tXW" },
        { "baseDecimals", 6 },
        { "quoteDecimals", 9 },
        { "lpDecimals", 6 },
        { "version", 4 },
        { "programId", "HWy1jotHpo6UqeQxx49dpYYdQB8wj9Qk9MdxwjLvDHB8" },
        { "authority", "DbQqP6ehDYmeYjcBaMRuA8tAJY1EjDUz9DpwSLjaQqfC" },
        { "openOrders", "AVzQbN3UJ3VBFPPHaPfAbvzyr9R54ihARKguuCf52ZVA" },
        { "targetOrders", "8eFhYH1gvtvv8bt4P4a8G2vk6RBHdvsp7ZafcpbbpqRj" },
        { "baseVault", "82tCeVoMAoJwcgBerKq6BRSXxij3KsF1FYtwC37ydqQi" },
        { "quoteVault", "GcYhDfBuWpaWLVjYZfxeoVLQe3wahR6D4SpgFqJvWdZw" },
        { "marketVersion", 3 },
        { "marketProgramId", "EoTcMgcDRTJVZDMZWBoU6rhYHZfkNTVEAfz3uUJRcYGj" },
        { "marketId", "BCEJbQoNgLTut7MU7UgQC2acodH2uMoLTRQGEEg7tGj4" },
        { "marketAuthority", "9tf7YVQNN83uqFnVCetSq3duTKtngu2ow42kjh8QAaWj" },
        { "marketBaseVault", "EQMUwvWFwexgosWiGnFW95ukjC4CchPcUMirm2bwDfAz" },
        { "marketQuoteVault", "6R8rHH2oJxAstRfBVkMawohmomwuaT6Mr9YogGK8sLgS" },
        { "marketBids", "5jqqXMjRmBD8EvfL5q5drZYLeN66XqZMRqPQvLxTc59i" },
        { "marketAsks", "81SHsU8YzvHviQgiMsRdibkKysuPMoj9nMXdQbPsLAQC" },
        { "marketEventQueue", "A1bFG8Xx1BxU1yRya9jaAwTdFoQJsTKNgH3Pq2jku6KE" },
        { "withdrawQueue", "11111111111111111111111111111111" },
        { "lpVault", "11111111111111111111111111111111" },
        { "lookupTableAccount", "11111111111111111111111111111111" }
    };
    string[] accounts = new[]
    {
        poolkey["id"].ToString(),
        poolkey["authority"].ToString(),
        poolkey["openOrders"].ToString(),
        poolkey["targetOrders"].ToString(),
        poolkey["baseVault"].ToString(),
        poolkey["quoteVault"].ToString(),
        poolkey["marketProgramId"].ToString(),
        poolkey["marketId"].ToString(),
        poolkey["marketBids"].ToString(),
        poolkey["marketAsks"].ToString(),
        poolkey["marketEventQueue"].ToString(),
        poolkey["marketBaseVault"].ToString(),
        poolkey["marketQuoteVault"].ToString(),
        poolkey["marketAuthority"].ToString(),
        poolkey["baseDecimals"].ToString(), //精度
    };
    var _logger = new SerilogLoggerFactory(Log.Logger).CreateLogger(nameof(Program));
    ;
    IRaydiumAMM raydiumAmm = new RaydiumAmmImpl(rpcClient, account, _logger);
    var sign = await raydiumAmm.SwapInAsync(mint, 1m, 100, 0, 0, accounts);
    Console.WriteLine(sign);
    sign = await raydiumAmm.SwapOutByPercentAsync(mint, 10, 100, 0, 0, accounts);
    Console.WriteLine(sign);
    PublicKey associatedUser =
        AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account, new PublicKey(mint));
    var tokenbalance = await rpcClient.GetTokenAccountBalanceAsync(associatedUser);
    var tokenAmount = tokenbalance.Result.Value.AmountDecimal;
    Console.WriteLine(tokenAmount);
    sign = await raydiumAmm.SwapOutAsync(mint, tokenAmount, 100, 0, 0, accounts);
    Console.WriteLine(sign);

    /*IPumpFun pumpFun = new PumpFunImpl(rpcClient, account);
    var pump=new Solnet.Pumpfun.PumpfunClient(rpcClient, account);

    var sign=await pump.BuyToPublicKey(mint, 0.01m, 10, "2K4fSmuCQUp9oLrJS86vgnxPRmjTC5sJeh4f2e2nRXwX");
    Console.WriteLine(sign);*/
    /*
    var sign = await pumpFun.Buy(mint,0.01m,10,0,0);
    Console.WriteLine(sign);
    sign= await pumpFun.Sell(mint,********,10,0,0);
    Console.WriteLine(sign);
    sign = await pumpFun.SellPercentage(mint,50,10,0,0);
    Console.WriteLine(sign);
    */
}

async Task Ws测试()
{
    var url = "wss://mainnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3";
    var ws = new WSocketClientHelp(url);
    ws.OnClose += (sender, eventArgs) => { Console.WriteLine($"{DateTime.Now:HH:mm:ss,fff}断开"); };
    ws.OnError += (sender, eventArgs) => { Console.WriteLine($"{DateTime.Now:HH:mm:ss,fff}error"); };
    ws.OnMessage += (sender, eventArgs) => { Console.WriteLine(eventArgs); };
    ws.OnOpen += (sender, eventArgs) => { Console.WriteLine($"{DateTime.Now:HH:mm:ss,fff}打开"); };
    ws.Open();
    ws.Send("654321");
    await Task.Delay(1000 * 60 * 5);
    ws.Send("123456");
}

async Task Jup解析()
{
    //QMqFu4fYGGeUEysFnenhAvR83g86EDDNxzUskfkWKYCBPWe1hqgD6jgKAXr6aYoEQb3VKARJKQDQuouqjDXAd5Yeju3giwhCNm13uzqzLKVgTyTpKSfUo53eQJn4MJZH2vTLJxzp4NyBwZ4YDtv3PtTRoY5mq3sPTKuMh43uhE5V4DV
    var str =
        "QMqFu4fYGGeUEysFnenhAvR83g86EDDNxzUskfkWKYCBPWe1hqgD6jgKAXr6aYoEQb3VKARJKQDQuouqjDXAd5Yeju3giwhCNm13uzqzLKVgTyTpKSfUo53eQJn4MJZH2vTLJxzp4NyBwZ4YDtv3PtTRoY5mq3sPTKuMh43uhE5V4DV";
    var data = Base58.Decode(str);
    var sdata = new ReadOnlySpan<byte>(data);
    var offset = 16;
    var amm = Base58.Encode(sdata.Slice(offset, 32).ToArray());
    offset += 32;
    var inputMint = Base58.Encode(sdata.Slice(offset, 32).ToArray());
    offset += 32;
    var inputAmount = BitConverter.ToUInt64(sdata.Slice(offset, 8).ToArray(), 0);
    offset += 8;
    var outputMint = Base58.Encode(sdata.Slice(offset, 32).ToArray());
    offset += 32;
    var outputAmount = BitConverter.ToUInt64(sdata.Slice(offset, 8).ToArray(), 0);
    Console.WriteLine(amm);
}

async Task WssPrice测试()
{
    var list = new List<MemCmp> { new MemCmp { Bytes = AmmInfo.ACCOUNT_DISCRIMINATOR_B58, Offset = 0 } };
    await streamingRpcClient.ConnectAsync();
    await streamingRpcClient.SubscribeProgramAsync("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8", (state, value) =>
    {
        var data = value.Value.Account;
        Console.WriteLine(JsonSerializer.Serialize(data));
    }, Commitment.Processed, 752, null);
}

async Task Price测试()
{
    // 代理配置
    var proxyAddress = "http://127.0.0.1:33210"; // 替换为你的代理地址和端口，例如 "http://127.0.0.1:8080"
    var proxy = new WebProxy
    {
        Address = new Uri(proxyAddress),
        BypassProxyOnLocal = false, // 是否绕过本地地址
    };

    // 配置 HttpClientHandler
    var handler = new HttpClientHandler
    {
        Proxy = proxy,
        UseProxy = true, // 启用代理
    };

    // 创建 HttpClient
    var httpClient = new System.Net.Http.HttpClient(handler)
    {
        Timeout = TimeSpan.FromSeconds(30) // 设置超时
    };

    // 配置 gRPC 通道
    var channelOptions = new GrpcChannelOptions
    {
        //HttpClient = httpClient,
        MaxReceiveMessageSize = 64 * 1024 * 1024, // 64MB，匹配 Yellowstone 的需求
    };
    //来自 @nbbbb_eth(牛牛哥) @twogolee(Michael)
    using var channel = GrpcChannel.ForAddress("https://solana-yellowstone-grpc.publicnode.com:443", channelOptions);

    var client = new Geyser.GeyserClient(channel);
    var pingRequest = new SubscribeRequest
    {
        Ping = new SubscribeRequestPing
        {
            Id = 1
        }
    };
    var request = new SubscribeRequest
    {
        Commitment = CommitmentLevel.Confirmed
    };
    using var stream = client.Subscribe();
    var cancellationToken = new CancellationToken();

    request.Accounts.Add("ray", new SubscribeRequestFilterAccounts()
    {
        Owner = { "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA" },
        Filters =
        {
            new SubscribeRequestFilterAccountsFilter()
            {
                Datasize = 165,
                Memcmp = new SubscribeRequestFilterAccountsFilterMemcmp()
                {
                    Offset = 32, Base58 = "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"
                }
            }
        },
        NonemptyTxnSignature = true
    });

    request.Accounts.Add("amm", new SubscribeRequestFilterAccounts()
    {
        Owner = { "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8" },
        Filters =
        {
            new SubscribeRequestFilterAccountsFilter()
            {
                Datasize = 752,
                /*Memcmp = new SubscribeRequestFilterAccountsFilterMemcmp()
                {
                    Offset = 0, Base58 =AmmInfo.ACCOUNT_DISCRIMINATOR_B58
                }*/
            }
        },
        NonemptyTxnSignature = true,
    });

    request.Accounts.Add("pump", new SubscribeRequestFilterAccounts()
    {
        Owner = { "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P" },
        Filters =
        {
            new SubscribeRequestFilterAccountsFilter() { Datasize = 49 }
        },
        NonemptyTxnSignature = true
    });

    await stream.RequestStream.WriteAsync(request);
    Task responseTask = Task.Run(async () =>
    {
        //消息回复
        while (await stream.ResponseStream.MoveNext(cancellationToken))
        {
            var data = stream.ResponseStream.Current;
            if (data.Account != null)
            {
                // SPL_ACCOUNT_LAYOUT
                // PUMP_LAYOUT
                var accountData = data.Account.Account;
                var pubkey = Base58.Encode(accountData.Pubkey.ToByteArray());
                var type = data.Filters[0];
                if (type == "pump")
                {
                    var virtualTokenReserves = accountData.Data.Span.GetU64(8 * 1);
                    var virtualSolReserves = accountData.Data.Span.GetU64(8 * 2);
                    var realTokenReserves = accountData.Data.Span.GetU64(8 * 3);
                    var realSolReserves = accountData.Data.Span.GetU64(8 * 4);
                    var tokenTotalSupply = accountData.Data.Span.GetU64(8 * 5);
                    var complete = accountData.Data.Span.GetBool(8 * 6);

                    var tokenPrice = (decimal)(virtualSolReserves / 1e9) / (decimal)(virtualTokenReserves / 1e6);
                    //Console.WriteLine($"{type} {pubkey} {tokenPrice}");
                }
                else if (type == "ray")
                {
                    var offset = 0;
                    var mint = accountData.Data.Span.GetPubKey(0); //0 32
                    offset += 32;
                    var owner = accountData.Data.Span.GetPubKey(offset); //32 32
                    offset += 32;
                    ulong amount = accountData.Data.Span.GetU64(offset); // 小端序64 8
                    offset += 8;
                    var delegateOption = accountData.Data.Span.GetU32(offset); //72 4
                    offset += 4;
                    var delegatePubkey = delegateOption == 1 ? accountData.Data.Span.GetPubKey(offset) : null; //76 32
                    offset += 32;
                    var state = accountData.Data.Span.GetU8(offset); //108 1
                    offset += 1;
                    var isNativeOption = accountData.Data.Span.GetU32(offset); //109 4
                    offset += 4;
                    var isNative = accountData.Data.Span.GetU64(offset); //113 8
                    offset += 8;
                    var delegatedAmount = accountData.Data.Span.GetU64(offset); //121 8
                    offset += 8;
                    var closeAuthorityOption = accountData.Data.Span.GetU32(offset); //129 4
                    offset += 4;
                    var closeAuthority = closeAuthorityOption == 1 ? accountData.Data.Span.GetPubKey(offset) : null;
                    /*
                     * sol的amount 除以 token的 amount就等于单价
                     */
                    //Console.WriteLine($"{type} {pubkey} {mint} {amount}");
                }
                else if (type == "amm")
                {
                }
            }
        }
    });

    Timer timer = new(async (state) =>
    {
        //定时ping
        await stream.RequestStream.WriteAsync(pingRequest);
    }, null, 5000, 5000);

    await Task.WhenAny(responseTask, Task.Run(() => Console.ReadKey()));
}

async Task 木星ws测试()
{
    var uri = "wss://trench-stream.jup.ag/ws";
    //var websocket = new WebSocket4Net.WebSocket(uri);
    //websocket.MessageReceived += (sender, eventArgs) =>
    //{
    //    var s = eventArgs.Message;
    //    if (!string.IsNullOrWhiteSpace(s))
    //    {
    //        var result = JsonSerializer.Deserialize<JupAgStreamResult>(s);
    //        if (result == null || result.Data == null || result.Data.Count <= 0) return;
    //        for (int i = 0; i < result.Data.Count; i++)
    //        {
    //            var item = result.Data[i];
    //            Console.WriteLine($"[{item.Type}] {item.Pool.Id} {item.Pool.Type} {item.Pool.BaseAsset.Name}");
    //        }
    //    }
    //};
    //websocket.DataReceived += (sender, eventArgs) =>
    //{

    //};
    //websocket.Opened += (sender, eventArgs) =>
    //{
    //    var sub = "{\"type\":\"subscribe:recent\"}";
    //    websocket.Send(sub);
    //};
    //websocket.Error += (sender, eventArgs) =>
    //{
    //    Console.WriteLine(eventArgs.Exception.Message);
    //};
    //await websocket.OpenAsync();

    //return;
    var wSocketClient = new WSocketClientHelp(uri.ToString());
    wSocketClient.IsUseProxy = true;
    wSocketClient.ProxyAddress = "socks5://127.0.0.1:33210";
    wSocketClient.OnMessage += async (sender, s) =>
    {
        if (!string.IsNullOrWhiteSpace(s))
        {
            var result = JsonSerializer.Deserialize<JupAgStreamResult>(s);
            if (result == null || result.Data == null || result.Data.Count <= 0) return;
            for (int i = 0; i < result.Data.Count; i++)
            {
                var item = result.Data[i];
                Console.WriteLine($"[{item.Type}] {item.Pool.Id} {item.Pool.Type} {item.Pool.BaseAsset.Name}");
            }
        }
    };
    wSocketClient.OnOpen += async (sender, eventArgs) =>
    {
        var sub = "{\"type\":\"subscribe:recent\"}";
        //{"type":"subscribe:pool","pools":[]}
        //{"type":"subscribe:txns","assets":[]}
        wSocketClient.Send(sub);
    };
    wSocketClient.OnError += (sender, exception) => { Console.WriteLine(exception.Message); };
    wSocketClient.Open();
}

async Task Nats测试()
{
    var opts = new NatsOpts
    {
        AuthOpts = NatsAuthOpts.Default with
        {
            Username = "subscriber",
            Password = "lW5a9y20NceF6AE9",
        },
        Url = "wss://prod-v2.nats.realtime.pump.fun:443"
    };
    using var cts = new CancellationTokenSource();
    await using var nats = new NatsClient(opts);
    await foreach (var msg in nats.SubscribeAsync<string>(subject: "MSG.*", cancellationToken: cts.Token))
    {
        Console.WriteLine($"Received: {msg.Subject}: {msg.Data}");
    }

    await nats.ConnectAsync();
}

async Task PumpWebSocket测试()
{
    /* 高级交易信息
     * wss://prod-advanced.nats.realtime.pump.fun/
     * CONNECT {"no_responders":true,"protocol":1,"verbose":false,"pedantic":false,"user":"subscriber","pass":"OktDhmZ2D3CtYUiM","lang":"nats.ws","version":"1.29.2","headers":true}
     * SUB advancedTrade 1
     * 创建新代币信息
     * wss://prod-v2.nats.realtime.pump.fun/
     * CONNECT {"no_responders":true,"protocol":1,"verbose":false,"pedantic":false,"user":"subscriber","pass":"lW5a9y20NceF6AE9","lang":"nats.ws","version":"1.29.2","headers":true}
     * SUB newCoinCreated.prod 1
     */
    async Task NewCoinCreated()
    {
        var json =
            "CONNECT {\"no_responders\":true,\"protocol\":1,\"verbose\":false,\"pedantic\":false,\"user\":\"subscriber\",\"pass\":\"lW5a9y20NceF6AE9\",\"lang\":\"nats.ws\",\"version\":\"1.29.2\",\"headers\":true}\r\n";
        var data = Encoding.UTF8.GetBytes(json);
        var uri = "wss://prod-v2.nats.realtime.pump.fun/";
        var wSocketClient = new WSocketClientHelp(uri.ToString());
        wSocketClient.OnMessage += (sender, s) =>
        {
            Console.WriteLine(s);
            if (s.StartsWith("PING"))
            {
                var pong = $"PONG\r\n";
                wSocketClient.Send(Encoding.UTF8.GetBytes(pong));
            }

            if (s.StartsWith("PONG"))
            {
                var sub = "SUB newCoinCreated.prod 1\r\n";
                wSocketClient.Send(Encoding.UTF8.GetBytes(sub));
            }

            //解析消息
            if (s.StartsWith("MSG"))
            {
            }
        };
        wSocketClient.OnOpen += async (sender, eventArgs) =>
        {
            wSocketClient.Send(data);
            await Task.Delay(100);
            var pong = $"PING\r\n";
            wSocketClient.Send(Encoding.UTF8.GetBytes(pong));
        };
        wSocketClient.Open();
    }

    async Task Advanced()
    {
        var connect =
            "CONNECT {\"no_responders\":true,\"protocol\":1,\"verbose\":false,\"pedantic\":false,\"user\":\"subscriber\",\"pass\":\"OktDhmZ2D3CtYUiM\",\"lang\":\"nats.ws\",\"version\":\"1.29.2\",\"headers\":true}\r\n";
        var uri = "wss://prod-advanced.nats.realtime.pump.fun/";
        var wSocketClient = new WSocketClientHelp(uri.ToString());
        wSocketClient.IsUseProxy = true;
        wSocketClient.ProxyAddress = "http://127.0.0.1:33210";
        wSocketClient.OnMessage += async (sender, s) =>
        {
            Console.WriteLine(s);
            if (s.StartsWith("INFO"))
            {
                var pong = $"PING\r\n";
                wSocketClient.Send(Encoding.UTF8.GetBytes(pong));
            }

            if (s.StartsWith("PING"))
            {
                var pong = $"PONG\r\n";
                wSocketClient.Send(Encoding.UTF8.GetBytes(pong));
            }

            if (s.StartsWith("PONG"))
            {
                var sub = "SUB advancedTrade 1\r\n";
                wSocketClient.Send(Encoding.UTF8.GetBytes(sub));
            }

            if (s.StartsWith("MSG"))
            {
                var json = s.Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries)[1];
            }
        };
        wSocketClient.OnOpen += async (sender, eventArgs) => { wSocketClient.Send(Encoding.UTF8.GetBytes(connect)); };
        wSocketClient.Open();
    }

    await Advanced();
}

void WSocketClient_OnMessage(object sender, string data)
{
    throw new NotImplementedException();
}

async Task 消息解析()
{
    var strId = ObfuscatorFactory.NewInstance.Obfuscate(20);
    var id = ObfuscatorFactory.NewInstance.Deobfuscate(strId);
    var account = await rpcClient.GetAccountInfoAsync("BKL6wWy1fD4oiAaS3ksVJPkQVopw8yrYLH7BCm9Epump",
        Commitment.Confirmed,
        BinaryEncoding.JsonParsed);

    var secKey = new byte[]
    {
        159, 208, 228, 61, 59, 77, 204, 70, 55, 32, 81, 197, 175, 239, 2, 187, 14, 250, 18, 238, 170, 57, 205, 17, 131,
        107, 28, 59, 108, 161, 252, 75, 95, 33, 64, 164, 55, 172, 234, 78, 169, 140, 106, 93, 38, 173, 46, 90, 64, 228,
        13, 193, 107, 249, 93, 35, 225, 248, 186, 57, 232, 56, 116, 26
    };

    PrivateKey privateKey = new PrivateKey(secKey);
    var acs = Solnet.Wallet.Account.FromSecretKey(privateKey);
    var data = new byte[]
    {
        3, 55, 45, 243, 53, 123, 39, 2, 0
    };
    var d = ByteString.CopyFrom(data);

    var pubkey = d.Span.GetPubKey(16);

    var accountStr =
        "[[8,67,180,112,105,124,223,1,113,200,188,180,140,247,193,22,38,7,205,27,127,163,173,22,166,113,97,168,10,173,220,224],[193,107,124,63,189,99,0,221,232,223,13,186,48,75,23,197,205,214,76,244,37,180,237,42,180,216,147,195,5,97,202,82],[245,14,208,250,164,221,29,64,13,159,212,117,131,187,108,202,189,56,197,234,24,51,198,52,175,156,86,77,119,56,73,221],[173,17,230,164,252,41,68,164,250,130,81,190,248,21,66,110,27,251,40,198,182,100,102,119,96,124,106,217,245,102,166,70],[223,111,87,106,189,34,235,203,116,69,197,64,187,227,47,65,196,174,3,208,35,34,86,190,97,63,84,232,84,58,214,119],[173,248,137,93,217,126,92,249,104,114,163,190,178,239,6,91,249,224,179,218,135,85,2,165,11,84,211,163,102,84,178,34],[3,6,70,111,229,33,23,50,255,236,173,186,114,195,155,231,188,140,229,187,197,247,18,107,44,67,155,58,64,0,0,0],[4,121,213,91,242,49,192,110,238,116,197,110,206,104,21,7,253,177,178,222,163,244,142,81,2,177,205,162,86,188,19,143],[6,221,246,225,215,101,161,147,217,203,225,70,206,235,121,172,28,180,133,237,95,91,55,145,58,140,245,133,126,255,0,169],[6,155,136,87,254,171,129,132,251,104,127,99,70,24,192,53,218,196,57,220,26,235,59,85,152,160,240,0,0,0,0,1],[180,63,250,39,245,215,246,74,116,192,155,31,41,88,121,222,75,9,171,54,223,201,221,81,75,50,26,167,179,140,229,232],[1,86,224,246,147,102,90,207,68,219,21,104,191,23,91,170,81,137,203,151,245,210,255,59,101,93,43,182,253,109,24,176],[58,134,94,105,238,15,84,128,202,188,246,99,87,228,220,47,24,213,141,69,193,234,116,137,251,55,35,217,121,60,114,166],[99,163,210,208,102,199,43,197,109,228,55,159,231,160,205,246,184,72,40,215,125,4,215,179,53,102,222,115,227,141,52,79],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[140,151,37,143,78,36,137,241,187,61,16,41,20,142,13,131,11,90,19,153,218,255,16,132,4,142,123,216,219,233,248,89],[172,241,54,235,1,252,28,78,136,61,35,200,181,132,74,181,154,55,246,106,221,87,197,233,172,59,83,224,89,211,92,100]]";
    var list = Newtonsoft.Json.JsonConvert
        .DeserializeObject<List<byte[]>>(accountStr); //JsonSerializer.Deserialize<List<byte[]>>(accountStr);
    var accounts = list.Select(it => Base58.Encode(ByteString.CopyFrom(it).ToByteArray())).ToList();
    Console.WriteLine(Newtonsoft.Json.JsonConvert.SerializeObject(accounts));
}

async Task gRpc测试()
{
    using var channel = GrpcChannel.ForAddress("https://solana-yellowstone-grpc.publicnode.com:443");
    //Confirmed
    var client = new Geyser.GeyserClient(channel);

    var pingRequest = new SubscribeRequest
    {
        Ping = new SubscribeRequestPing
        {
            Id = 1
        }
    };
    var request = new SubscribeRequest
    {
        Commitment = CommitmentLevel.Confirmed
    };
    var cts = new CancellationTokenSource();

    using var stream = client.Subscribe();

    async Task Pump()
    {
        //pump 新盘
        request.Transactions.Add("pumpfun", new SubscribeRequestFilterTransactions()
        {
            Vote = false,
            Failed = false,
            AccountInclude = { "TSLvdd1pWpHVjahSpsvCXUbgwsL3JAcvokwaKt1eokM" },
            AccountExclude = { },
            AccountRequired = { }
        });


        await stream.RequestStream.WriteAsync(request);
        Task responseTask = Task.Run(async () =>
        {
            while (await stream.ResponseStream.MoveNext(cts.Token))
            {
                var data = stream.ResponseStream.Current;
                if (data.Transaction != null)
                {
                    // 解析交易
                    var txnSignature =
                        Base58.Encode(data.Transaction.Transaction.Transaction.Signatures[0].ToByteArray());
                    //Console.WriteLine(JsonSerializer.Serialize(data.Transaction));
                    // 交易涉及的账户
                    var accountKeys =
                        data.Transaction.Transaction.Transaction.Message.AccountKeys.Select(k =>
                            Base58.Encode(k.ToByteArray()));
                    //Mint
                    var Mint = data.Transaction.Transaction.Meta.PostTokenBalances[0].Mint;
                    var Owner = data.Transaction.Transaction.Meta.PostTokenBalances[0].Owner;
                    Console.WriteLine($"=======================================================");
                    Console.WriteLine($"[{DateTime.Now.ToLongTimeString()}] {Mint}");
                    Console.WriteLine($"[{DateTime.Now.ToLongTimeString()}] {Owner}");
                    Console.WriteLine($"[{DateTime.Now.ToLongTimeString()}] https://solscan.io/tx/{txnSignature}");
                }
            }
        });
    }

    async Task 账号监听()
    {
        request.Accounts.Add("pump", new SubscribeRequestFilterAccounts()
        {
            //8kkhCMoDaz6bcP1KeeLxFBeDNJeFgmHbYYR3HmZQWTr8=>AzeHpPRhrZC1Q3L2V5MLFqD6ybtdKJYdAcpNwA8bpump
            Account = { "8kkhCMoDaz6bcP1KeeLxFBeDNJeFgmHbYYR3HmZQWTr8" },
        });
        request.Accounts.Add("ray", new SubscribeRequestFilterAccounts()
        {
            //5jSLVN58WfvV99cb6haGZRP6visNPAodgmYSy4sQEt9s=>4GUvefbnhw5YDQm43sCa6DQDuRJd1ywMwDS39YAEGM3V
            Account = { "5jSLVN58WfvV99cb6haGZRP6visNPAodgmYSy4sQEt9s" }
        });

        await stream.RequestStream.WriteAsync(request);
        //await Task.Run(async () =>
        //{
        //    await Task.Delay(3000);
        //    request.Accounts.Remove("ray");
        //    await stream.RequestStream.WriteAsync(request);
        //});
        await Task.Run(async () =>
        {
            while (await stream.ResponseStream.MoveNext(cts.Token))
            {
                var data = stream.ResponseStream.Current;
                //Console.WriteLine(JsonSerializer.Serialize(data));

                if (data.Account != null)
                {
                    var account = data.Account.Account;
                    var pubkey = Base58.Encode(account.Pubkey.ToByteArray());
                    if (data.Filters[0] == "pump")
                    {
                        var result = Solnet.Pumpfun.Accounts.BondingCurve.Deserialize(account.Data.ToByteArray());
                        var price = (decimal)(result.virtualSolReserves / result.virtualTokenReserves);
                        Console.WriteLine($"Pumpfun {pubkey} {result.complete} {price}");
                    }
                    else if (data.Filters[0] == "ray")
                    {
                        var result = Solnet.Raydium.AmmInfo.Deserialize(account.Data.ToByteArray());
                        var price = result.LpAmount;
                        Console.WriteLine($"Raydium {pubkey} {price}");
                    }
                }
            }
        });
    }

    async Task PingTask()
    {
        while (!cts.IsCancellationRequested)
        {
            await Task.Delay(5000);
            await stream.RequestStream.WriteAsync(pingRequest);
        }
    }

    Task pingTask = Task.Run(async () =>
    {
        while (!cts.IsCancellationRequested)
        {
            await Task.Delay(5000);
            await stream.RequestStream.WriteAsync(pingRequest);
        }
    }, cts.Token);
    await 账号监听();
    await Task.WhenAll(pingTask);
}

async Task Pump订阅价格()
{
    await streamingRpcClient.ConnectAsync();
    //C6CFSBji49gBuRakEojvhWPEsyyLw69WLAg2frhHpump
    //FPMADpruiPYZcNrrFtCbPE9RdUvHMHpj9tHrFCZbtVXg
    await streamingRpcClient.SubscribeAccountInfoAsync("JLJEK5kKqWoEmhqZhjN4Wt4Xw9HRTs8jbaNuf3fqTWo",
        (state, value) => { Console.WriteLine(JsonSerializer.Serialize(value)); });
}

async Task Raydium订阅价格()
{
    await streamingRpcClient.ConnectAsync();
    await streamingRpcClient.SubscribeAccountInfoAsync("DC4Ym9hSN9WJqcxz1ggnqmnmMNjaY8HTLom5YDfz4R1J",
        (state, value) => { Console.WriteLine(JsonSerializer.Serialize(value)); });
}

async Task 获取余额()
{
    while (true)
    {
        //     var balanceCmd = await Cli.Wrap("solana").WithArguments(args => args
        //         .Add("balance")
        //         .Add("4ETf86tK7b4W72f27kNLJLgRWi9UfJjgH4koHGUXMFtn")
        //     ).ExecuteBufferedAsync();
        // //0.07691 SOL
        //     var result = balanceCmd.StandardOutput.Split(new string[] { " " }, StringSplitOptions.None)[0];
        //     Console.WriteLine(result);

        var balanceResult = await rpcClient.GetBalanceAsync("4ETf86tK7b4W72f27kNLJLgRWi9UfJjgH4koHGUXMFtn");
        if (!balanceResult.WasSuccessful)
        {
            await Task.Delay(1000);
            continue;
        }


        var amount = (decimal)(balanceResult.Result.Value / 1e9);
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss,fff}] {amount}");
        // if (amount >= 5)
        // {
        //     try
        //     {
        //         var airdropCmd = await Cli.Wrap("solana").WithArguments(args => args.Add("airdrop").Add("1"))
        //             .ExecuteBufferedAsync();
        //         Console.WriteLine(airdropCmd.StandardOutput);
        //         Console.WriteLine(airdropCmd.StandardError);
        //     }
        //     catch (Exception e)
        //     {
        //         System.Console.WriteLine(e.Message);
        //     }
        // }

        await Task.Delay(1000);
    }
}


async Task Example()
{
    var client = new GeyserRpcClient("wss://atlas-devnet.helius-rpc.com/?api-key=e4d4820a-26ce-434a-8d90-c080bdd7c8a3");

    try
    {
        await client.InitAsync();

        await client.OnLogs("subId1", "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8",
            async (result) => { Console.WriteLine($"收到交易: {result}"); });

        // 等待一段时间...
        //await Task.Delay(5000);

        // 清理
        client.RemoveOnLogs("subId1");
        await client.DisposeAsync();
    }
    catch (Exception ex)
    {
        Console.WriteLine($"发生错误: {ex.Message}");
    }
}