using System.Text.Json;

namespace SolanaConsoleApp.Core;

/// <summary>
/// 处理账户和交易订阅的核心类，支持订阅、通知和取消订阅的异步处理。
/// </summary>
public class MessageProcessor
{
    /// <summary>
    /// 存储订阅关系的字典，键是订阅ID，值是包含请求ID和订阅类型的元组。
    /// </summary>
    private readonly Dictionary<long, Tuple<string, string>> _subscriptions = new();

    // 异步回调属性
    /// <summary>
    /// 订阅成功时的回调，接收订阅响应对象。
    /// </summary>
    public Func<SubscribeResponse, Task>? OnSubscribeResponseAsync { get; set; }

    /// <summary>
    /// 取消订阅成功时的回调，接收取消订阅响应对象。
    /// </summary>
    public Func<UnsubscribeResponse, Task>? OnUnsubscribeResponseAsync { get; set; }

    /// <summary>
    /// 账户通知消息的回调，接收账户通知对象。
    /// </summary>
    public Func<AccountNotification, Task>? OnAccountNotificationAsync { get; set; }

    /// <summary>
    /// 交易通知消息的回调，接收交易通知对象。
    /// </summary>
    public Func<TransactionNotification, Task>? OnTransactionNotificationAsync { get; set; }

    /// <summary>
    /// 未知消息类型的回调，接收原始JSON字符串。
    /// </summary>
    public Func<string, Task>? OnUnknownMessageAsync { get; set; }

    /// <summary>
    /// 处理接收到的JSON消息，根据消息类型分发到对应的异步处理方法。
    /// </summary>
    /// <param name="json">接收到的JSON字符串消息，不能为空。</param>
    /// <returns>异步任务，表示处理完成。</returns>
    public async Task ProcessMessageAsync(string json)
    {
        if (string.IsNullOrEmpty(json))
        {
            Console.WriteLine("Received empty message");
            return;
        }

        try
        {
            // 解析JSON为JsonDocument以确定消息类型
            using JsonDocument doc = JsonDocument.Parse(json);
            MessageType type = DetermineMessageType(doc.RootElement);

            switch (type)
            {
                case MessageType.SubscribeResponse:
                    await HandleSubscribeResponseAsync(json);
                    break;
                case MessageType.UnsubscribeResponse:
                    await HandleUnsubscribeResponseAsync(json);
                    break;
                case MessageType.AccountNotification:
                    await HandleAccountNotificationAsync(json);
                    break;
                case MessageType.TransactionNotification:
                    await HandleTransactionNotificationAsync(json);
                    break;
                default:
                    await HandleUnknownMessageAsync(json);
                    break;
            }
        }
        catch (JsonException ex)
        {
            Console.WriteLine($"JSON parsing error: {ex.Message}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Unexpected error: {ex.Message}");
        }
    }

    /// <summary>
    /// 确定JSON消息的类型。
    /// </summary>
    /// <param name="root">JSON消息的根元素。</param>
    /// <returns>消息类型枚举值。</returns>
    private static MessageType DetermineMessageType(JsonElement root)
    {
        // 检查是否为通知消息
        if (root.TryGetProperty("method", out JsonElement methodElement))
        {
            string method = methodElement.GetString();
            if (method == "accountNotification") return MessageType.AccountNotification;
            if (method == "transactionNotification") return MessageType.TransactionNotification;
        }

        // 检查是否为响应消息
        if (root.TryGetProperty("result", out JsonElement resultElement) &&
            root.TryGetProperty("id", out _))
        {
            if (resultElement.ValueKind == JsonValueKind.Number)
                return MessageType.SubscribeResponse; // 订阅响应，result为数字
            if (resultElement.ValueKind == JsonValueKind.True || resultElement.ValueKind == JsonValueKind.False)
                return MessageType.UnsubscribeResponse; // 取消订阅响应，result为布尔值
        }

        return MessageType.Unknown;
    }

    /// <summary>
    /// 处理订阅成功响应，存储订阅关系并触发回调。
    /// </summary>
    /// <param name="json">订阅响应的JSON字符串。</param>
    /// <returns>异步任务，表示处理完成。</returns>
    private async Task HandleSubscribeResponseAsync(string json)
    {
        var response = JsonSerializer.Deserialize<SubscribeResponse>(json);
        // 初始订阅类型设为"pending"，将在通知时更新
        _subscriptions[response.Result] = Tuple.Create(response.Id, "pending");
        await OnSubscribeResponseAsync?.Invoke(response);
    }

    /// <summary>
    /// 处理取消订阅响应，移除订阅关系并触发回调。
    /// </summary>
    /// <param name="json">取消订阅响应的JSON字符串。</param>
    /// <returns>异步任务，表示处理完成。</returns>
    private async Task HandleUnsubscribeResponseAsync(string json)
    {
        var response = JsonSerializer.Deserialize<UnsubscribeResponse>(json);
        var subscription = _subscriptions.FirstOrDefault(x => x.Value.Item1 == response.Id);
        if (subscription.Key != 0)
        {
            _subscriptions.Remove(subscription.Key); // 移除订阅记录
        }

        await OnUnsubscribeResponseAsync?.Invoke(response);
    }

    /// <summary>
    /// 处理账户通知消息，更新订阅类型并触发回调。
    /// </summary>
    /// <param name="json">账户通知的JSON字符串。</param>
    /// <returns>异步任务，表示处理完成。</returns>
    private async Task HandleAccountNotificationAsync(string json)
    {
        var notification = JsonSerializer.Deserialize<AccountNotification>(json);
        if (_subscriptions.ContainsKey(notification.Params.Subscription))
        {
            // 更新订阅类型为"account"
            _subscriptions[notification.Params.Subscription] =
                Tuple.Create(_subscriptions[notification.Params.Subscription].Item1, "account");
            await OnAccountNotificationAsync?.Invoke(notification);
        }
    }

    /// <summary>
    /// 处理交易通知消息，更新订阅类型并触发回调。
    /// </summary>
    /// <param name="json">交易通知的JSON字符串。</param>
    /// <returns>异步任务，表示处理完成。</returns>
    private async Task HandleTransactionNotificationAsync(string json)
    {
        var notification = JsonSerializer.Deserialize<TransactionNotification>(json);
        if (_subscriptions.ContainsKey(notification.Params.Subscription))
        {
            // 更新订阅类型为"transaction"
            _subscriptions[notification.Params.Subscription] =
                Tuple.Create(_subscriptions[notification.Params.Subscription].Item1, "transaction");
            await OnTransactionNotificationAsync?.Invoke(notification);
        }
    }

    /// <summary>
    /// 处理未知类型的消息，触发未知消息回调。
    /// </summary>
    /// <param name="json">未知类型的JSON字符串。</param>
    /// <returns>异步任务，表示处理完成。</returns>
    private async Task HandleUnknownMessageAsync(string json)
    {
        await OnUnknownMessageAsync?.Invoke(json);
    }

    /// <summary>
    /// 创建账户订阅请求的JSON字符串。
    /// </summary>
    /// <param name="requestId">请求的唯一ID。</param>
    /// <param name="account">要订阅的账户地址。</param>
    /// <returns>序列化后的订阅请求JSON字符串。</returns>
    public string CreateAccountSubscribeRequest(string requestId, string account)
    {
        var request = new AccountSubscribeRequest
        {
            Id = requestId,
            Params = new object[]
            {
                account,
                new SubscribeParams()
                {
                    Commitment = "confirmed",
                    Encoding = "base64",
                }
            }
        };
        return JsonSerializer.Serialize(request);
    }

    /// <summary>
    /// 创建交易订阅请求的JSON字符串。
    /// </summary>
    /// <param name="requestId">请求的唯一ID。</param>
    /// <param name="accountInclude">要包含的账户地址数组。</param>
    /// <returns>序列化后的订阅请求JSON字符串。</returns>
    public string CreateTransactionSubscribeRequest(string requestId, string accountInclude)
    {
        var request = new TransactionSubscribeRequest
        {
            Id = requestId,
            Params = new object[]
            {
                new TransactionFilter
                {
                    Vote = false,
                    Failed = false,
                    AccountInclude = new string[] { accountInclude }
                },
                new TransactionSubscriptionConfig
                {
                    Commitment = "processed",
                    Encoding = "jsonParsed",
                    TransactionDetails = "full",
                    ShowRewards = true,
                    MaxSupportedTransactionVersion = 0
                }
            }
        };
        return JsonSerializer.Serialize(request);
    }

    /// <summary>
    /// 创建账户订阅的取消请求JSON字符串。
    /// </summary>
    /// <param name="subscriptionId">要取消的订阅ID。</param>
    /// <returns>序列化后的取消订阅请求JSON字符串，如果订阅不存在则返回null。</returns>
    public string CreateAccountUnsubscribeRequest(long subscriptionId)
    {
        if (!_subscriptions.TryGetValue(subscriptionId, out var subInfo))
            return null;

        var request = new UnsubscribeRequest
        {
            Id = subInfo.Item1,
            Method = "accountUnsubscribe",
            Params = new[] { subscriptionId }
        };
        return JsonSerializer.Serialize(request);
    }

    /// <summary>
    /// 创建交易订阅的取消请求JSON字符串。
    /// </summary>
    /// <param name="subscriptionId">要取消的订阅ID。</param>
    /// <returns>序列化后的取消订阅请求JSON字符串，如果订阅不存在则返回null。</returns>
    public string CreateTransactionUnsubscribeRequest(long subscriptionId)
    {
        if (!_subscriptions.TryGetValue(subscriptionId, out var subInfo))
            return null;

        var request = new UnsubscribeRequest
        {
            Id = subInfo.Item1,
            Method = "transactionUnsubscribe",
            Params = new[] { subscriptionId }
        };
        return JsonSerializer.Serialize(request);
    }
}