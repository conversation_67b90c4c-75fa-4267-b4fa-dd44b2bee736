using System.Text.Json;
using System.Text.Json.Serialization;

namespace SolanaConsoleApp.Core;

public class TransactionVersionConverter : JsonConverter<int>
{
    public override int Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.Number)
        {
            return reader.GetInt32();
        }
        else if (reader.TokenType == JsonTokenType.String)
        {
            string value = reader.GetString();
            if (value == "legacy")
            {
                return 1; // 将 "legacy" 转换为 0
            }
            if (int.TryParse(value, out int result))
            {
                return result; // 如果是其他数字字符串，尝试转换为 int
            }
            throw new JsonException($"Unable to convert string '{value}' to int for TransactionData.Version.");
        }
        throw new JsonException($"Unexpected token type {reader.TokenType} for TransactionData.Version.");
    }

    public override void Write(Utf8JsonWriter writer, int value, JsonSerializerOptions options)
    {
        if (value == 1)
        {
            writer.WriteStringValue("legacy"); // 将 0 写为 "legacy"
        }
        else
        {
            writer.WriteNumberValue(value); // 其他值按数字写入
        }
    }
}