using System;
using System.Collections.Generic;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

public static class DateTimeHelper
{
    public static string GetDateTime()
    {
        var now = DateTime.Now;
        return $"{now.Year}-{now.Month}-{now.Day} {now.Hour}:{now.Minute}:{now.Second}.{now.Millisecond}";
    }
}

public class GeyserRpcClient
{
    private ClientWebSocket _rpcClient;
    private Dictionary<string, Func<object, Task>> _logsCallback;
    private Dictionary<string, string> _subId;
    private readonly string _wsUrl;
    private readonly CancellationTokenSource _cts;
    private const int BUFFER_SIZE = 4096;

    public GeyserRpcClient(string wsUrl)
    {
        _wsUrl = wsUrl;
        _logsCallback = new Dictionary<string, Func<object, Task>>();
        _subId = new Dictionary<string, string>();
        _cts = new CancellationTokenSource();
        _rpcClient = new ClientWebSocket();
    }

    private async Task<ClientWebSocket> WsOpenAsync()
    {
        try
        {
            await _rpcClient.ConnectAsync(new Uri(_wsUrl), _cts.Token);
            Console.WriteLine("WebSocket is open");

            // 启动ping定时器
            _ = StartPingTimer();

            // 启动消息接收循环
            _ = ReceiveLoop();

            return _rpcClient;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"连接错误: {ex.Message}");
            throw;
        }
    }

    private async Task StartPingTimer()
    {
        while (!_cts.Token.IsCancellationRequested)
        {
            if (_rpcClient.State == WebSocketState.Open)
            {
                await _rpcClient.SendAsync(
                    new ArraySegment<byte>(new byte[0]),
                    WebSocketMessageType.Binary,
                    true,
                    _cts.Token);
            }
            await Task.Delay(3000); // 30秒发送一次ping
        }
    }

    private async Task ReceiveLoop()
    {
        var buffer = new byte[BUFFER_SIZE];
        var messageBuilder = new StringBuilder();

        while (!_cts.Token.IsCancellationRequested)
        {
            try
            {
                var result = await _rpcClient.ReceiveAsync(
                    new ArraySegment<byte>(buffer),
                    _cts.Token);

                if (result.MessageType == WebSocketMessageType.Close)
                {
                    await _rpcClient.CloseAsync(WebSocketCloseStatus.NormalClosure,
                        string.Empty,
                        _cts.Token);
                    break;
                }

                messageBuilder.Append(Encoding.UTF8.GetString(buffer, 0, result.Count));

                if (result.EndOfMessage)
                {
                    var message = messageBuilder.ToString();
                    messageBuilder.Clear();

                    await HandleMessage(message);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"接收消息错误: {ex.Message}");
                break;
            }
        }
    }

    private async Task HandleMessage(string messageStr)
    {
        try
        {
            var messageObj = JsonSerializer.Deserialize<JsonElement>(messageStr);

            if (messageObj.TryGetProperty("id", out var id) &&
                messageObj.TryGetProperty("result", out var result))
            {
                Console.WriteLine(messageObj);
                var idStr = id.GetString();
                if (_logsCallback.ContainsKey(idStr))
                {
                    _subId[result.GetString()] = idStr;
                }
            }

            if (messageObj.TryGetProperty("params", out var parameters))
            {
                if (parameters.TryGetProperty("result", out var paramResult) &&
                    paramResult.TryGetProperty("transaction", out _))
                {
                    var subscription = parameters.GetProperty("subscription").GetString();
                    if (_subId.TryGetValue(subscription, out var subId) &&
                        _logsCallback.TryGetValue(subId, out var callback))
                    {
                        await callback(paramResult);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"解析JSON失败: {ex.Message}");
        }
    }

    public async Task InitAsync()
    {
        try
        {
            await WsOpenAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"初始化失败: {ex.Message}");
            throw;
        }
    }

    public void RemoveOnLogs(string subId)
    {
        try
        {
            _logsCallback.Remove(subId);
            var keysToRemove = _subId.Where(kvp => kvp.Value == subId)
                                    .Select(kvp => kvp.Key)
                                    .ToList();
            foreach (var key in keysToRemove)
            {
                _subId.Remove(key);
            }

            _rpcClient.CloseAsync(WebSocketCloseStatus.NormalClosure,
                string.Empty,
                _cts.Token).Wait();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"移除日志订阅失败: {ex.Message}");
        }
    }

    public async Task OnLogs(string subId, string accountAddress, Func<object, Task> callback)
    {
        var request = new
        {
            jsonrpc = "2.0",
            id = subId,
            method = "transactionSubscribe",
            @params = new object[]
            {
                new { accountInclude = new[] { accountAddress } },
                new
                {
                    commitment = "processed",
                    encoding = "jsonParsed",
                    transactionDetails = "full",
                    showRewards = true,
                    maxSupportedTransactionVersion = 1
                }
            }
        };

        var requestJson = JsonSerializer.Serialize(request);
        var bytes = Encoding.UTF8.GetBytes(requestJson);
        await _rpcClient.SendAsync(
            new ArraySegment<byte>(bytes),
            WebSocketMessageType.Text,
            true,
            _cts.Token);

        _logsCallback[subId] = callback;
    }

    public async Task DisposeAsync()
    {
        _cts.Cancel();
        if (_rpcClient.State == WebSocketState.Open)
        {
            await _rpcClient.CloseAsync(WebSocketCloseStatus.NormalClosure,
                string.Empty,
                CancellationToken.None);
        }
        _rpcClient.Dispose();
        _cts.Dispose();
    }
}