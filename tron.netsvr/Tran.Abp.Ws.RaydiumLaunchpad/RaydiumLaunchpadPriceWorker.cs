using System.Net.WebSockets;
using FreeRedis;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Solnet.Rpc;
using Solnet.Rpc.Models;
using Solnet.Rpc.Types;
using Tron.Abp.Multiplex.Contracts;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Threading;

namespace Tran.Abp.Ws.RaydiumLaunchpad;

public class RaydiumLaunchpadPriceWorker : AsyncPeriodicBackgroundWorkerBase
{
    private string wsurl = "";
    private IRedisClient _redisClient;
    private IStreamingRpcClient _streamingRpcClient;
    private bool isDev = false;
    private DateTime _lastMessageTime; // 标记最后收到消息的时间
    public ILogger<RaydiumLaunchpadPriceWorker> Logger { get; set; }

    public RaydiumLaunchpadPriceWorker(IOptions<SolanaOptions> solanaOptions, IRedisClient redisClient,
        AbpAsyncTimer timer, IServiceScopeFactory serviceScopeFactory) : base(timer,
        serviceScopeFactory)
    {
        Timer.Period = 10 *60* 1000;
        _redisClient = redisClient;
        Logger = NullLogger<RaydiumLaunchpadPriceWorker>.Instance;
        wsurl = solanaOptions.Value.MainWsUri;
        _streamingRpcClient = Tran.Abp.Ws.Core.HeliusClientFactory.GetStreamingClient(wsurl, Logger);
        isDev = _streamingRpcClient.NodeAddress.Host.Contains("devnet");
        _streamingRpcClient.ConnectionStateChangedEvent += (sender, state) =>
        {
            if (state == WebSocketState.Open) _lastMessageTime = DateTime.Now;
        };
    }

    private async Task InitSubscribeStream()
    {
        var launchpad = await _streamingRpcClient.SubscribeProgramAsync("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
           async (state, value) =>
            {
                var pubkey = value.Value.PublicKey;
                var data = new ReadOnlySpan<byte>(Convert.FromBase64String(value.Value.Account.Data[0]));
                var offset = 0;
                var mint = Base58.Encode(data.Slice(offset, 32).ToArray()); //0 32
                offset += 32;
                var owner = Base58.Encode(data.Slice(offset, 32).ToArray()); //32 32
                offset += 32;
                ulong amount = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0); // 小端序64 8
                Logger.LogDebug($"[Launcher Price]{pubkey} {mint} {amount}");
                await _redisClient.HSetAsync(RedisKey.SolanaTokenLauncherPrice, pubkey, amount);
            }, dataSize: 165, memCmpList: new List<MemCmp>()
            {
                new MemCmp()
                {
                    Offset = 32,
                    Bytes = isDev
                        ? "DG6kZFFCqxdtWXw53Zc28hLs3MTr28Efkm2FrsNERNSQ"
                        : "WLHv2UAZm6z4KyaaELi5pjdbJh6RESMva1Rnn8pJVVh"
                }
            }, commitment: Commitment.Confirmed);
    }

    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {
        var timeSinceLastMessage = DateTime.Now - _lastMessageTime;
        Logger.LogWarning($"ws 断开 1s 后连接 {_lastMessageTime}=>{DateTime.Now}=>{timeSinceLastMessage.TotalSeconds}");
        if (timeSinceLastMessage.TotalSeconds >= 60)
        {
            Logger.LogWarning($"ws 断开 1s 后连接");
            //await _streamingRpcClient.DisconnectAsync();
            //await Task.Delay(3000);
            //await _streamingRpcClient.ConnectAsync();
            //await InitSubscribeStream();
        }
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        await _streamingRpcClient.ConnectAsync();
        await InitSubscribeStream();
        _lastMessageTime = DateTime.Now;
        Logger.LogDebug("获取 launchpad price 服务=>启动");
        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        //await _webSocketClient.DisconnectAsync();
        await _streamingRpcClient.DisconnectAsync();
        _streamingRpcClient.Dispose();
        Logger.LogDebug("获取 launchpad price 服务=>停止");
        await base.StopAsync(cancellationToken);
    }
}