using System.Net.WebSockets;
using System.Numerics;
using FreeRedis;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Solnet.Rpc;
using Solnet.Rpc.Types;
using Tron.Abp.Multiplex.Contracts;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Threading;

namespace Tran.Abp.Ws.RaydiumLaunchpad;

public class RaydiumLaunchpadPoolWorker : AsyncPeriodicBackgroundWorkerBase
{
    private string wsurl = "";
    private IRedisClient _redisClient;
    private IStreamingRpcClient _streamingRpcClient;
    public ILogger<RaydiumLaunchpadPoolWorker> Logger { get; set; }
    private bool isDev = false;
    private DateTime _lastMessageTime; // 标记最后收到消息的时间

    public RaydiumLaunchpadPoolWorker(IOptions<SolanaOptions> solanaOptions, IRedisClient redisClient,
        AbpAsyncTimer timer, IServiceScopeFactory serviceScopeFactory) : base(timer,
        serviceScopeFactory)
    {
        Timer.Period = 10 * 60 * 1000;
        _redisClient = redisClient;
        Logger = NullLogger<RaydiumLaunchpadPoolWorker>.Instance;
        wsurl = solanaOptions.Value.MainWsUri;
        _streamingRpcClient = Tran.Abp.Ws.Core.HeliusClientFactory.GetStreamingClient(wsurl, Logger);
        isDev = _streamingRpcClient.NodeAddress.Host.Contains("devnet");
        _streamingRpcClient.ConnectionStateChangedEvent += (sender, state) =>
        {
            if (state == WebSocketState.Open) _lastMessageTime = DateTime.Now;
        };
    }


    private async Task InitSubscribeStream()
    {
        var launchpad = await _streamingRpcClient.SubscribeProgramAsync(
            isDev ? "7soRSLviCKHCKzCbRuVpZDif76NWLVqFtbjt8LpyxWSq" : "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj",
            async (state, value) =>
            {
                var pubkey = value.Value.PublicKey;
                var data = new ReadOnlySpan<byte>(Convert.FromBase64String(value.Value.Account.Data[0]));
                var offset = 8;
                var epoch = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var auth_bump = (int)data[offset];
                offset += 1;
                var status = (int)data[offset];
                offset += 1;
                var base_decimals = (int)data[offset];
                offset += 1;
                var quote_decimals = (int)data[offset];
                offset += 1;
                var migrate_type = (int)data[offset];
                offset += 1;
                var supply = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var total_base_sell = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var virtual_base = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var virtual_quote = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var real_base = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var real_quote = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var total_quote_fund_raising = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var quote_protocol_fee = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var platform_fee = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                var migrate_fee = BitConverter.ToUInt64(data.Slice(offset, 8).ToArray(), 0);
                offset += 8;
                offset += 8 * 5;
                string global_config = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                string platform_config = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                string base_mint = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                string quote_mint = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                string base_vault = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                string quote_vault = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                string creator = Base58.Encode(data.Slice(offset, 32).ToArray());
                offset += 32;
                //Console.WriteLine($"{base_mint} {quote_mint} {publick}");
                var key = base_mint != "So11111111111111111111111111111111111111112" ? base_mint : quote_mint;
                Logger.LogDebug($"[raydium launchpad pool] {key} {pubkey}");

                if (await _redisClient.HExistsAsync(RedisKey.SolanaLauncherPool, key))
                {
                    var dict = await _redisClient
                        .HGetAsync<Dictionary<string, Tron.Abp.Multiplex.Contracts.LaunchpadPool>>(
                            RedisKey.SolanaLauncherPool, key);
                    dict[pubkey] =
                        new Tron.Abp.Multiplex.Contracts.LaunchpadPool(pubkey, base_decimals,
                            quote_decimals, migrate_type, supply, total_base_sell, virtual_base, virtual_quote,
                            real_base, real_quote, total_quote_fund_raising, quote_protocol_fee, platform_fee,
                            migrate_fee, global_config, platform_config, base_mint, quote_mint, base_vault,
                            quote_vault);
                    await _redisClient.HSetAsync(RedisKey.SolanaLauncherPool, key, dict);
                }
                else
                {
                    var dict = new Dictionary<string, Tron.Abp.Multiplex.Contracts.LaunchpadPool>();
                    dict[pubkey] = new Tron.Abp.Multiplex.Contracts.LaunchpadPool(pubkey, base_decimals,
                        quote_decimals, migrate_type, supply, total_base_sell, virtual_base, virtual_quote,
                        real_base, real_quote, total_quote_fund_raising, quote_protocol_fee, platform_fee,
                        migrate_fee, global_config, platform_config, base_mint, quote_mint, base_vault,
                        quote_vault);
                    ;
                    await _redisClient.HSetAsync(RedisKey.SolanaLauncherPool, key, dict);
                }
                
                
                //恒定乘积曲线
                Func<decimal, decimal, decimal, decimal, int, int, decimal> getProductPoolPrice = (virtualA, virtualB, realA,
                    realB, decimalA, decimalB) =>
                {
                    var numerator = virtualB + realB;//sol
                    var denominator = virtualA - realA;//mint
                    return (decimal)(numerator / denominator) * (decimal)Math.Pow(10, decimalA - decimalB);
                };
                //固定价格曲线
                Func<decimal, decimal, int, int, decimal> getFixedPrice = (virtualA, virtualB, decimalA, decimalB) =>
                {
                    return (decimal)(virtualB / virtualA) * (decimal)Math.Pow(10, decimalA - decimalB);
                };
                //线性价格曲线
                Func<decimal, decimal, int, int, decimal> getLinearPrice = (virtualA, realA, decimalA, decimalB) =>
                {
                    var numerator = new BigInteger(virtualA * realA);
                    return (decimal)(numerator / new BigInteger(18446744073709551616m)) *
                           (decimal)Math.Pow(10, decimalA - decimalB);
                };

                var mint = key;
                //计算单价  
                var price = 0 switch
                {
                    //恒定乘积曲线
                    0 => getProductPoolPrice(virtual_base,virtual_quote,real_base,real_quote,base_decimals,quote_decimals),
                    //固定价格曲线
                    1 => getFixedPrice(virtual_base,virtual_quote,base_decimals,quote_decimals), 
                    //线性价格曲线
                    2 => getLinearPrice(virtual_base,real_base,base_decimals,quote_decimals), 
                    _ => getProductPoolPrice(virtual_base,virtual_quote,real_base,real_quote,base_decimals,quote_decimals),
                };
                Logger.LogDebug($"[Launcher Price] {mint} {price}");
                //存储单价
                if (await _redisClient.HExistsAsync(RedisKey.SolanaTokenLauncherPrice, mint))
                {
                    var dict = await _redisClient.HGetAsync<Dictionary<string, decimal>>(
                        RedisKey.SolanaTokenLauncherPrice, mint);
                    dict[pubkey] = price;
                    await _redisClient.HSetAsync(RedisKey.SolanaTokenLauncherPrice, mint, dict);
                }
                else
                {
                    var dict = new Dictionary<string, decimal>();
                    dict[pubkey] = price;
                    await _redisClient.HSetAsync(RedisKey.SolanaTokenLauncherPrice, mint, dict);
                }
            }, Commitment.Confirmed, dataSize: 429);
    }

    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {
        var timeSinceLastMessage = DateTime.Now - _lastMessageTime;
        Logger.LogWarning($"ws 断开 1s 后连接 {_lastMessageTime}=>{DateTime.Now}=>{timeSinceLastMessage.TotalSeconds}");
        if (timeSinceLastMessage.TotalSeconds >= 60)
        {
            Logger.LogWarning($"ws 断开 1s 后连接");
            //await _streamingRpcClient.DisconnectAsync();
            //await Task.Delay(3000);
            //await _streamingRpcClient.ConnectAsync();
            //await InitSubscribeStream();
        }
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        //await _webSocketClient.ConnectAsync();
        await _streamingRpcClient.ConnectAsync();
        await InitSubscribeStream();
        _lastMessageTime = DateTime.Now;
        Logger.LogDebug("获取  launchpad pool 服务=>启动");
        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        //await _webSocketClient.DisconnectAsync();
        await _streamingRpcClient.DisconnectAsync();
        _streamingRpcClient.Dispose();
        Logger.LogDebug("获取 launchpad Pool 服务=>停止");
        await base.StopAsync(cancellationToken);
    }
}