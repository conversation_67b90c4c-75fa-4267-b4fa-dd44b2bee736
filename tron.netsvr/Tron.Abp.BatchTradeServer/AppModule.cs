using Volo.Abp;
using Volo.Abp.Autofac;
using Volo.Abp.Modularity;

namespace Tron.Abp.BatchTradeServer;

[DependsOn(typeof(AbpAutofacModule),
    typeof(AbpJainaEventBusModule),
    typeof(CachingFreeRedisModule),
    typeof(AbpDomainModule),
    typeof(AbpMultiplexModule),
    typeof(AbpBackgroundWorkersModule))]
public class AppModule: AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        base.ConfigureServices(context);

        context.Services.AddEventBus();
        var configuration = context.Services.GetConfiguration();

        var solanaOption = configuration.GetSection("Solana").Get<SolanaOptions>();
        Configure<SolanaOptions>(options =>
        {
            options.MainWsrRpc = solanaOption.MainWsrRpc;
            options.MainWsUri= solanaOption.MainWsUri;
            options.MaingRpc = solanaOption.MaingRpc;
            options.MainUri = solanaOption.MainUri;
        });
    }
    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        base.OnApplicationInitialization(context);
        context.AddBackgroundWorkerAsync<BatchTradeWorker>();
        context.AddBackgroundWorkerAsync<BatchTradeSubTaskWorker>();
        context.AddBackgroundWorkerAsync<BindTradeWorker>();
    }
}