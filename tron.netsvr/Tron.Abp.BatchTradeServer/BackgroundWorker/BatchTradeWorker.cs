using System.Collections.Concurrent;
using FreeRedis.Internal;
using Tron.Abp.BatchTradeServer.EventHandlers;
using Tron.Abp.Core;
using Tron.Abp.SqlSugar;

namespace Tron.Abp.BatchTradeServer.BackgroundWorker;

/// <summary>
/// 批量交易
/// </summary>
public class BatchTradeWorker : BackgroundWorkerBase
{
    public ILogger<BatchTradeWorker> Logger { get; set; }
    private IRedisClient _redisClient;
    private SubscribeStreamObject streamCopyTradeTask;
    private IEventPublisher _eventPublisher;
    private ISolanaApi _solanaApi;

    private readonly SqlSugarRepository<UserBatchTrade> _userBatchTradeRepository;
    private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;

    private ConcurrentDictionary<int, WorkQueue<Model.BTTask>> dictionary;

    public BatchTradeWorker(IRedisClient redisClient, IEventPublisher eventPublisher, <PERSON>ola<PERSON><PERSON><PERSON> solanaApi,
        SqlSugarRepository<UserBatchTrade> userBatchTradeRepository,
        SqlSugarRepository<UserAllTraderLog> userAllTraderLogRepository)
    {
        _redisClient = redisClient;
        _eventPublisher = eventPublisher;
        _solanaApi = solanaApi;
        _userBatchTradeRepository = userBatchTradeRepository;
        _userAllTraderLogRepository = userAllTraderLogRepository;
        Logger = NullLogger<BatchTradeWorker>.Instance;

        dictionary = new ConcurrentDictionary<int, WorkQueue<Model.BTTask>>();
    }

    private decimal GetRandomAmount(decimal min, decimal max, decimal step)
    {
        var min_val = (int)Math.Truncate(min * (int)Math.Pow(10, (int)step));
        var max_val = (int)Math.Truncate(max * (int)Math.Pow(10, (int)step));
        var val = (decimal)(RandomHelper.GetRandom(min_val, max_val) / Math.Pow(10, (int)step));
        return val;
    }

    private decimal GetRandomPercent(decimal min, decimal max)
    {
        var min_val = (int)Math.Truncate(min * (int)Math.Pow(10, 2));
        var max_val = (int)Math.Truncate(max * (int)Math.Pow(10, 2));
        var val = (decimal)(RandomHelper.GetRandom(min_val, max_val) / Math.Pow(10, 2));
        return val;
    }

    private async Task<decimal> GetBalance(string pubkey)
    {
        var result = await _solanaApi.GetBalanceAsync(pubkey);

        return result;
    }

    private decimal GetSolAmount(int Type, decimal curBalance, decimal gas, decimal mev, int slippage)
    {
        var balance = curBalance * (1 - slippage / 100m); //扣除滑点
        var price = Type switch
        {
            1 => balance - gas,
            2 => balance - mev,
            3 => balance - gas - mev,
            _ => balance - gas,
        };
        return price;
    }

    private async Task BatchTradeBuySellTask(Model.BTTask item)
    {
        //var tokenInfo = await _solanaApi.GetMint(item.TokenAddress);
        var dexType = item.DexType; //tokenInfo.Type;
        var poolId = item.PoolId;//await _solanaApi.GetMintPoolId(item.TokenAddress);
        var account = Solnet.Wallet.Account.FromSecretKey(item.SecretKey);
        var solAmount = 0m;
        var tokenPercent = 100m;
        //买
        if (item.IsBuy)
        {
            var curGetBalance = await GetBalance(item.PubKey);
            solAmount = item.AmountData.Type switch
            {
                //全部
                0 => GetSolAmount(item.TradeModel, curGetBalance, item.Gas, item.Mev, item.Slippage), // 保留3位小数
                1 => GetRandomAmount(item.AmountData.AmountRange[0] ?? 0m, item.AmountData.AmountRange[1] ?? 1m,
                    item.AmountData.AmountRange[2] ?? 3m),
                2 => curGetBalance * GetRandomPercent(item.AmountData.PercentRange[0] ?? 10m,
                    item.AmountData.PercentRange[1] ?? 100m) / 100m,
                3 => item.AmountData.Amount ?? 0.01m,
                _ => 0.01m,
            };
        }
        //卖
        else
        {
            tokenPercent = item.AmountData.Type switch
            {
                0 => 100,
                2 => GetRandomPercent(item.AmountData.PercentRange[0] ?? 10, item.AmountData.PercentRange[1] ?? 100),
                _ => 100,
            };
        }

        if (item.IsBuy && solAmount <= 0)
        {
            Logger.LogDebug($"批量任务 {item.BTTId} {item.TokenAddress} 钱包:{item.PubKey} 余额为0 不交易");
            goto Next;
        }

        var now = DateTime.Now;
        //写log
        var logModel = new UserAllTraderLog()
        {
            TaskId = item.BTTId,
            TraderType = TraderType.Trader_03,
            UId = item.UId,
            Chain = item.Chain,
            TokenName = item.TokenName,
            TokenSymbol = item.TokenSymbol,
            TokenAddress = item.TokenAddress,
            TokenIcon = item.TokenIcon,
            IsBuy = item.IsBuy,
            SolAmount = solAmount,
            TokenAmount = 0,
            Gas = item.Gas,
            Mev = item.Mev,
            MyWalletAddress = item.PubKey,
            Status = "进行中",
            CreateTime = now,
        };
        logModel.Id = await _userAllTraderLogRepository.CopyNew().AsInsertable(logModel).ExecuteReturnIdentityAsync();
        Logger.LogDebug($"批量交易 {item.BTTId} {item.TokenAddress} 钱包:{item.PubKey} 开始交易");
        var (successful, signature, errormsg) = item.IsBuy switch
        {
            true => await _solanaApi.Buy(dexType, account, item.TokenAddress, solAmount,
                item.Slippage, item.Gas, item.Mev, poolId,isMev: item.IsMev, mevList: item.MevType),
            false => await _solanaApi.Sell(dexType, account, item.TokenAddress, tokenPercent, item.Gas, item.Mev,
                item.Slippage, poolId,isMev: item.IsMev, mevList: item.MevType),
        };
        Logger.LogDebug(
            $"批量交易 {item.BTTId} {item.TokenAddress} 钱包:{item.PubKey} 交易返回:{successful}=>{signature} {errormsg}");

        if (successful && !string.IsNullOrWhiteSpace(signature))
        {
            await Task.Delay(500);
            //获取交易后的余额
            var (_solAmount, tokenAmount) =
                await _solanaApi.ParserTransaction(signature, item.TokenAddress, dexType, item.IsBuy);
            await _userAllTraderLogRepository.CopyNew().AsUpdateable()
                .SetColumns(it => it.CompleteTime == DateTime.Now)
                .SetColumns(it => it.TokenAmount == tokenAmount)
                .SetColumns(it => it.SolAmount == _solAmount)
                .SetColumns(it => it.Status == "批量交易-成功")
                .SetColumns(it => it.Signature == signature)
                .Where(it => it.Id == logModel.Id)
                .ExecuteCommandAsync();
            //获取交易 信息 sol tokenAmount
            var sendData =
                new BatchTradeSubTaskUpdateAmountEvent(logModel.Id, item.BTTId, signature, item.TokenAddress,
                    item.IsBuy, dexType);
            sendData.SubTask = item;
            sendData.PoolId = poolId;
            sendData.SolAmount = solAmount;
            sendData.TokenPercent = tokenPercent;
            await _eventPublisher.PublishAsync(EventConsts.BatchTradeUpdateAmountEvent, sendData);
        }
        else
        {
            await _userAllTraderLogRepository.CopyNew().AsUpdateable()
                .SetColumns(it => it.CompleteTime == DateTime.Now)
                .SetColumns(it => it.Status == "批量交易-失败")
                .Where(it => it.Id == logModel.Id)
                .ExecuteCommandAsync();
        }

        Next:
        if (dictionary.TryGetValue(item.BTTId, out var workQueue))
        {
            var mark = $"剩余:{workQueue.Count}";
            Logger.LogDebug($"批量交易 {item.BTTId} 任务 {item.TokenAddress} 买:{item.IsBuy} 正在运行 剩余:{workQueue.Count}");
            await _userBatchTradeRepository.CopyNew().AsUpdateable()
                .SetColumns(it => it.Mark == mark)
                .Where(it => it.Id == item.BTTId)
                .ExecuteCommandAsync();
            if (workQueue.Count <= 0)
            {
                Logger.LogDebug($"批量交易 {item.BTTId} 任务 {item.TokenAddress} 买:{item.IsBuy} 任务完成");
                //任务 全部执行完成 更新状态
                await _userBatchTradeRepository.CopyNew().AsUpdateable()
                    .SetColumns(it => it.Status == 2)
                    .SetColumns(it => it.Mark == mark)
                    .SetColumns(it => it.CompleteTime == DateTime.Now)
                    .Where(it => it.Id == item.BTTId)
                    .ExecuteCommandAsync();
                dictionary.Remove(item.BTTId, out _);
            }
        }
    }

    private async void OnRedisSubscribeStreamMessage(Dictionary<string, string> obj)
    {
        var id = int.Parse(obj["Id"]);
        var batchTrade = await _userBatchTradeRepository.AsQueryable().Where(it => it.Id == id).FirstAsync();
        if (batchTrade == null) return;
        Logger.LogDebug($"收到 批量交易 {id} 钱包:{batchTrade.Wallets.Count} {batchTrade.TokenAddress} 买入:{batchTrade.IsBuy}");
        WorkQueue<Model.BTTask> workQueue;
        if (dictionary.ContainsKey(id))
        {
            Logger.LogDebug($"批量交易 {id} {batchTrade.TokenAddress} 任务 正在运行");
            return;
        }

        var dexType = "";
        var poolId = "";
        if (obj.TryGetValue("DexType", out var value))
        {
            dexType = value;
        }
        else
        {
            var tokenInfo = await _solanaApi.GetMint(batchTrade.TokenAddress);
            dexType = tokenInfo.Type;
            poolId = await _solanaApi.GetMintPoolId(batchTrade.TokenAddress);
        }

        
        
        workQueue = QueueManager.CreateQueue<Model.BTTask>(BatchTradeBuySellTask, 1);
        dictionary[id] = workQueue;
        foreach (var wallet in batchTrade.Wallets)
        {
            var btt = new Model.BTTask()
            {
                BTTId = id,
                UId = batchTrade.UId,
                WalletId = wallet.WalletId,
                PubKey = wallet.PubKey,
                SecretKey = wallet.SecretKey,
                Chain = batchTrade.Chain,
                TokenName = batchTrade.TokenName,
                TokenSymbol = batchTrade.TokenSymbol,
                TokenAddress = batchTrade.TokenAddress,
                TokenIcon = batchTrade.TokenIcon,
                IsBuy = batchTrade.IsBuy,
                TradeModel = batchTrade.TradeModel, //1 2 3
                Gas = batchTrade.Gas ?? 0.000005m,
                Mev = batchTrade.Mev ?? 0,
                Slippage = batchTrade.Slippage ?? 10,
                ExpiredAuto = batchTrade.ExpiredAuto,
                ExpiredTime = batchTrade?.ExpiredTime ?? 120,
                StoplimitData = batchTrade.StoplimitData,
                AmountData = batchTrade.AmountData,
                DexType = dexType,
                PoolId = poolId,
                IsMev = batchTrade.IsMev,
                MevType = batchTrade.MevType,
            };
            workQueue.Enqueue(btt);
        }
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        Logger.LogDebug($"批量交易服务 启动");
        streamCopyTradeTask =
            _redisClient.SubscribeStream(StreamKey.BatchTradeStream, onMessage: OnRedisSubscribeStreamMessage);

        await base.StartAsync(cancellationToken);
    }


    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        Logger.LogDebug($"批量交易服务 停止");
        if (dictionary.Any())
        {
            foreach (var workQueue in dictionary)
            {
                await workQueue.Value.StopAsync();
            }
        }

        streamCopyTradeTask.Dispose();
        await base.StopAsync(cancellationToken);
    }
}