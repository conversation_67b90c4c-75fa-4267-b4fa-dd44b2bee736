using System.Collections.Concurrent;
using FreeRedis.Internal;
using Solnet.Raydium.Types;
using Tron.Abp.BatchTradeServer.EventHandlers;
using Tron.Abp.BatchTradeServer.Model;
using Tron.Abp.Core;
using Tron.Abp.SqlSugar;

namespace Tron.Abp.BatchTradeServer.BackgroundWorker;

public class BatchBuyToUserWorker : BackgroundWorkerBase
{
    public ILogger<BatchBuyToUserWorker> Logger { get; set; }
    private IRedisClient _redisClient;
    private SubscribeStreamObject streamCopyTradeTask;
    private ISolanaApi _solanaApi;
    private ConcurrentDictionary<int, WorkQueue<BatchBuyToUserSubTask>> dictionary = new();
    private IEventPublisher _eventPublisher;
    private readonly SqlSugarRepository<UserOtherTrade> _userOtherTradeRepository;
    private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;

    public BatchBuyToUserWorker(SqlSugarRepository<UserOtherTrade> userOtherTradeRepository, IRedisClient redisClient,
        ISolanaApi solanaApi, SqlSugarRepository<UserAllTraderLog> userAllTraderLogRepository,
        IEventPublisher eventPublisher)
    {
        _userOtherTradeRepository = userOtherTradeRepository;
        _redisClient = redisClient;
        _solanaApi = solanaApi;
        _userAllTraderLogRepository = userAllTraderLogRepository;
        _eventPublisher = eventPublisher;
        Logger = NullLogger<BatchBuyToUserWorker>.Instance;
    }

    private int GenerateRandomInt(int? min, int? max)
    {
        // 情况 1：min 和 max 都为 null
        if (!min.HasValue && !max.HasValue)
        {
            throw new ArgumentException("min 和 max 不能同时为 null");
        }

        // 情况 2：min 为 null，max 非 null
        if (!min.HasValue && max.HasValue)
        {
            return RandomHelper.GetRandom(0, max.Value + 1); // [0, max]
        }

        // 情况 3：min 非 null，max 为 null
        if (min.HasValue && !max.HasValue)
        {
            return min.Value; // 返回 min
        }

        // 情况 4：min 和 max 都非 null
        int actualMin = Math.Min(min.Value, max.Value);
        int actualMax = Math.Max(min.Value, max.Value);

        if (actualMin == actualMax)
        {
            return actualMin; // min == max，直接返回
        }

        return RandomHelper.GetRandom(actualMin, actualMax);
    }

    public decimal GenerateRandomDecimal(decimal? min, decimal? max, int num = 3)
    {
        // 情况 1：min 和 max 都为 null
        if (!min.HasValue && !max.HasValue)
        {
            throw new ArgumentException("min 和 max 不能同时为 null");
        }

        // 情况 2：min 为 null，max 非 null
        if (!min.HasValue && max.HasValue)
        {
            //return RandomHelper.GenerateRandomDecimalInRange(0, max.Value);
            min = 0m;
        }

        // 情况 3：min 非 null，max 为 null
        if (min.HasValue && !max.HasValue)
        {
            return min.Value; // 返回 min
        }

        // 情况 4：min 和 max 都非 null
        decimal actualMin = Math.Min(min.Value, max.Value);
        decimal actualMax = Math.Max(min.Value, max.Value);

        if (actualMin == actualMax)
        {
            return actualMin; // min == max，直接返回
        }

        if (num >= 3)
        {
            num = RandomHelper.GetDecimalPlaces(min.Value);
        }

        var decimals = (int)Math.Pow(10, num);
        return RandomHelper.GenerateRandomDecimalInRange(actualMin, actualMax, decimals);
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        Logger.LogDebug($"批量 批量给指定用户购买 服务 启动");
        streamCopyTradeTask =
            _redisClient.SubscribeStream(StreamKey.BatchTradeToUsersStream,
                onMessage: OnRedisSubscribeStreamMessage);
        await base.StartAsync(cancellationToken);
    }

    private async void OnRedisSubscribeStreamMessage(Dictionary<string, string> obj)
    {
        var id = 0;
        if (obj.TryGetValue("id", out var idValue) && !string.IsNullOrWhiteSpace(idValue))
        {
            id = int.Parse(idValue);
        }

        var task = await _userOtherTradeRepository.AsQueryable().Where(it => it.Id == id).FirstAsync();
        if (task == null) return;

        var uid = task.UId;

        Logger.LogDebug($"批量任务 给其他用户购买{task.TokenAddress} 来源用户数:{task.Wallets.Count} 目标用户数:{task.ToUsers.Count}");

        var dexType = await _solanaApi.GetMintDexType(task.TokenAddress);


        var size = 3;
        var workQueue = QueueManager.CreateQueue<BatchBuyToUserSubTask>(BatchBuyToUserTask, size);
        dictionary[uid] = workQueue;

        for (int i = 0; i < task.ToUsers.Count; i++)
        {
            var toUser = task.ToUsers[i];
            var amount = GenerateRandomDecimal(task.AmountData[0], task.AmountData[1], (int)(task.AmountData[2] ?? 3));
            var secretKey = task.Wallets[i % task.Wallets.Count];
            var taskItem = new BatchBuyToUserSubTask
            {
                Chain = task.Chain,
                Uid = task.UId,
                TaskId = task.Id,
                Pubkey = secretKey.PubKey,
                SecretKey = secretKey.SecretKey,
                TokenAddress = task.TokenAddress,
                Amount = amount,
                Destination = toUser,
                Slippage = task.Slippage ?? 0,
                Gas = task.Gas ?? 0.000005m,
                Mev = task.Mev ?? 0,
                IsMev = task.IsMev,
                MevList = task.MevType,
                DexType = dexType,
            };
            workQueue.Enqueue(taskItem);
        }
    }

    private async Task BatchBuyToUserTask(BatchBuyToUserSubTask item)
    {
        var now = DateTime.Now;
        //写log
        var logModel = new UserAllTraderLog()
        {
            TaskId = item.TaskId,
            TraderType = TraderType.Trader_03,
            UId = item.Uid,
            Chain = item.Chain,
            TokenName = item.TokenName,
            TokenSymbol = item.TokenSymbol,
            TokenAddress = item.TokenAddress,
            TokenIcon = item.TokenIcon,
            IsBuy = true,
            SolAmount = item.Amount,
            TokenAmount = 0,
            Gas = item.Gas,
            Mev = item.Mev,
            MyWalletAddress = item.Pubkey,
            Status = "进行中",
            CreateTime = now,
        };
        logModel.Id = await _userAllTraderLogRepository.CopyNew().AsInsertable(logModel).ExecuteReturnIdentityAsync();

        Logger.LogDebug($"给用户{item.Pubkey}=>{item.Destination}购买{item.TokenAddress} {item.Amount}开始...");
        var account = Solnet.Wallet.Account.FromSecretKey(item.SecretKey);
        var (successful, signature, errormsg) = await _solanaApi.BuyToPublick(item.DexType, account, item.TokenAddress,
            item.Amount,
            item.Slippage, item.Gas, item.Mev, item.Destination);
        Logger.LogDebug(
            $"给用户{item.Pubkey}=>{item.Destination}购买{item.TokenAddress} {item.Amount}交易结果=>{successful}=>{signature}=>{errormsg}");


        if (successful && !string.IsNullOrWhiteSpace(signature))
        {
            await Task.Delay(500);
            //获取交易后的余额
            var (_solAmount, tokenAmount) =
                await _solanaApi.ParserTransaction(signature, item.TokenAddress, item.DexType, true);
            await _userAllTraderLogRepository.CopyNew().AsUpdateable()
                .SetColumns(it => it.CompleteTime == DateTime.Now)
                .SetColumns(it => it.TokenAmount == tokenAmount)
                .SetColumns(it => it.SolAmount == _solAmount)
                .SetColumns(it => it.Status == "伪装交易-成功")
                .SetColumns(it => it.Signature == signature)
                .Where(it => it.Id == logModel.Id)
                .ExecuteCommandAsync();
            //获取交易 信息 sol tokenAmount
            var sendData =
                new BatchTradeSubTaskUpdateAmountNotSubTaskItem(logModel.Id, item.TaskId, signature, item.TokenAddress,
                    true, item.DexType);
            await _eventPublisher.PublishAsync(EventConsts.BatchTradeUpdateAmountNotSubTaskEvent, sendData);
        }
        else
        {
            await _userAllTraderLogRepository.CopyNew().AsUpdateable()
                .SetColumns(it => it.CompleteTime == DateTime.Now)
                .SetColumns(it => it.Status == "伪装交易-失败")
                .Where(it => it.Id == logModel.Id)
                .ExecuteCommandAsync();
        }

        await Task.Delay(GenerateRandomInt(300, 800));
    }


    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        streamCopyTradeTask.Dispose();
        streamCopyTradeTask.Dispose();
        if (dictionary.Any())
        {
            foreach (var workQueue in dictionary)
            {
                await workQueue.Value.StopAsync();
            }
        }

        Logger.LogDebug($"批量 批量给指定用户购买 服务 停止");
        await base.StopAsync(cancellationToken);
    }
}