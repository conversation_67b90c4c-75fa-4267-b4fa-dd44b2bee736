using System.Collections.Concurrent;
using FreeRedis.Internal;
using Solnet.Programs;
using Solnet.Rpc.Models;
using Solnet.Rpc.Types;
using Solnet.Wallet;
using Tron.Abp.Core;
using Tron.Abp.SqlSugar;

namespace Tron.Abp.BatchTradeServer.BackgroundWorker;

public class BatchBurnAndCloseAccountWorker : BackgroundWorkerBase
{
    public ILogger<BatchBurnAndCloseAccountWorker> Logger { get; set; }
    private IRedisClient _redisClient;
    private SubscribeStreamObject streamCopyTradeTask;
    private ISolanaApi _solanaApi;
    private ConcurrentDictionary<int, WorkQueue<string>> dictionary=new();
    private readonly SqlSugarRepository<UserWallet> _userWalletRepository;
    private readonly SqlSugarRepository<UserMyWallet> _userMyWalletRepository;

    public BatchBurnAndCloseAccountWorker(ISolanaApi solanaApi, IRedisClient redisClient,
        SqlSugarRepository<UserWallet> userWalletRepository, SqlSugarRepository<UserMyWallet> userMyWalletRepository)
    {
        _solanaApi = solanaApi;
        _redisClient = redisClient;
        _userWalletRepository = userWalletRepository;
        _userMyWalletRepository = userMyWalletRepository;
        Logger = NullLogger<BatchBurnAndCloseAccountWorker>.Instance;
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        streamCopyTradeTask =
            _redisClient.SubscribeStream(StreamKey.SolanaBurnAndCloseAccountStream,
                onMessage: OnRedisSubscribeStreamMessage);

        Logger.LogDebug($"批量燃烧并回收账号 服务 启动");
        await base.StartAsync(cancellationToken);
    }

    private async void OnRedisSubscribeStreamMessage(Dictionary<string, string> obj)
    {
        var uid = 0; //用户ID
        if (obj.TryGetValue("Uid".ToLower(), out var uidValue))
        {
            uid = int.Parse(uidValue);
        }

        var gids = new List<int>(); //钱包分组
        if (obj.TryGetValue("Gid", out var gidValue))
        {
            gids = gidValue.Split(',').Select(it => int.Parse(it)).ToList();
        }

        var myWids = new List<int>(); //我的钱包ID
        if (obj.TryGetValue("Wid", out var myWidValue))
        {
            myWids = myWidValue.Split(',').Select(it => int.Parse(it)).ToList();
        }

        var pubKeys = new List<string>(); //钱包地址
        if (obj.TryGetValue("PubKeys", out var pubKeysValue))
        {
            pubKeys.AddRange(pubKeysValue.Split(','));
        }

        //私钥分组
        var secretKeys = new List<string>();

        if (gids.Count > 0)
        {
            var list = await _userWalletRepository.AsQueryable().Where(it => it.UId == uid && gids.Contains(it.GId))
                .ToListAsync();
            secretKeys.AddRange(list.Select(it => it.SecretKey));
        }

        if (myWids.Count > 0)
        {
            var list = await _userWalletRepository.AsQueryable().Where(it => it.UId == uid && myWids.Contains(it.Id))
                .ToListAsync();
            secretKeys.AddRange(list.Select(it => it.SecretKey));
        }

        if (pubKeys.Count > 0)
        {
            var list = await _userMyWalletRepository.AsQueryable()
                .Where(it => it.UId == uid && pubKeys.Contains(it.PubKey)).ToListAsync();
            secretKeys.AddRange(list.Select(it => it.SecretKey));
        }

        Logger.LogDebug($"收到 批量燃烧并回收账号 任务 {uid} => {secretKeys.Count}");
        var workQueue = QueueManager.CreateQueue<string>(BatchBurnAndCloseAccountTask, 3);
        dictionary[uid] = workQueue;
        foreach (var secretKey in secretKeys)
        {
            workQueue.Enqueue(secretKey);
        }
    }

    private async Task BatchBurnAndCloseAccountTask(string arg)
    {
        var account = Solnet.Wallet.Account.FromSecretKey(arg);
        var tokenAccounts = await _solanaApi.GetTokenAccountByOwnerAsync(account.PublicKey);
        foreach (var tokenAccount in tokenAccounts)
        {
            var tx = new List<TransactionInstruction>();
            var mint = new PublicKey(tokenAccount.Account.Data.Parsed.Info.Mint);
            if(mint==SolanaConstants.NATIVE_SOL_MINT_PROGRAM_ID) continue;
            var mintInfo = await _solanaApi.GetMintBaseAsset(mint);
            if (mintInfo != null)
            {
                try
                {
                    var result=await _solanaApi.Sell(mintInfo.Type,account,mint,100,0,0);
                    Logger.LogDebug($"批量燃烧并回收账号 交易失败 {mint} {account.PublicKey} =>{result.Successful} {result.ErrorMsg}");
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    //throw;
                }

            }
            var tc = new PublicKey(tokenAccount.PublicKey);
            if (tokenAccount.Account.Data.Parsed.Info.TokenAmount.AmountUlong > 0)
            {
                tx.Add(TokenProgram.Burn(tc, mint, tokenAccount.Account.Data.Parsed.Info.TokenAmount.AmountUlong,
                    account.PublicKey));
            }

            tx.Add(TokenProgram.CloseAccount(tc, account.PublicKey, account.PublicKey, TokenProgram.ProgramIdKey));

            var (successful, sign, errormsg) = await _solanaApi.TradeSend(tx, account);
            Logger.LogDebug($"批量燃烧并回收账号 交易返回 {mint} {account.PublicKey} =>{successful}=>{sign}=>{errormsg}");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        streamCopyTradeTask.Dispose();
        Logger.LogDebug($"批量燃烧并回收账号 服务 停止");
        await base.StopAsync(cancellationToken);
    }
}