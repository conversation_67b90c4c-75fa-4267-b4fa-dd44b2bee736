using Tran.Abp.Ws.RaydiumAmm;
using Log = Serilog.Log;

var template = "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}{Exception}";
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Debug()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .MinimumLevel.Override("System", LogEventLevel.Error)
    .Enrich.FromLogContext()
    .WriteTo.Async(c => c.File($"{Environment.CurrentDirectory}/logs/.txt", 
        shared: true,// 支持多个进程共享写入同一文件
        rollingInterval: RollingInterval.Hour, outputTemplate: template))
    .WriteTo.Async(c => c.Console())
    .CreateLogger();
    
    
    
var builder = Host.CreateApplicationBuilder(args);

builder.Configuration.AddAppSettingsSecretsJson();
builder.Logging.ClearProviders().AddSerilog();
builder.ConfigureContainer(builder.Services.AddAutofacServiceProviderFactory());
//注入服务
//builder.Services.AddHostedService<MyHostService>();
//注入abp
await builder.Services.AddApplicationAsync<AppModule>();

var host = builder.Build();

await host.InitializeAsync();

await host.RunAsync();