<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:rubyer="clr-namespace:Rubyer">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="pack://application:,,,/Rubyer;component/Themes/Resources/Default.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="rubyer:NotificationContainer">
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="HorizontalContentAlignment" Value="Right" />
        <Setter Property="VerticalContentAlignment" Value="Bottom" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="rubyer:NotificationContainer">
                    <Grid>
                        <ContentPresenter x:Name="contentPresenter"
                                          Focusable="False"
                                          RecognizesAccessKey="True"
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        <StackPanel x:Name="PART_StackPanel"
                                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>