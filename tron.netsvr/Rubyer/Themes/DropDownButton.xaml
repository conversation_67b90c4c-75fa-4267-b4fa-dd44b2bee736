<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:rubyer="clr-namespace:Rubyer">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="pack://application:,,,/Rubyer;component/Themes/ContextMenu.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="RubyerDropDownMenu"
           BasedOn="{StaticResource RubyerContextMenu}"
           TargetType="{x:Type ContextMenu}">
        <Setter Property="Foreground" Value="{DynamicResource DefaultForeground}" />
        <Setter Property="Placement" Value="Bottom" />
        <Setter Property="HorizontalOffset" Value="-5" />
        <Setter Property="IsOpen" Value="{Binding PlacementTarget.IsDropDownOpen, RelativeSource={RelativeSource Mode=Self}}" />
    </Style>

    <!--  下拉按钮  -->
    <Style x:Key="RubyerDropDownButton" TargetType="{x:Type rubyer:DropDownButton}">
        <Setter Property="Background" Value="{DynamicResource Primary}" />
        <Setter Property="Foreground" Value="{DynamicResource WhiteForeground}" />
        <Setter Property="FocusVisualStyle" Value="{StaticResource FocusVisual}" />
        <Setter Property="BorderBrush" Value="{DynamicResource Border}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Padding" Value="10 5" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="IsShowSeparator" Value="True" />
        <Setter Property="rubyer:ControlHelper.CornerRadius" Value="{DynamicResource AllControlCornerRadius}" />
        <Setter Property="rubyer:ControlHelper.MaskOpacity" Value="0.6" />
        <Setter Property="rubyer:ControlHelper.FocusedBrush" Value="{DynamicResource MaskDark}" />
        <Setter Property="rubyer:ButtonHelper.ShowShadow" Value="False" />
        <Setter Property="DropDownMenuStyle" Value="{StaticResource RubyerDropDownMenu}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type rubyer:DropDownButton}">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{Binding Path=(rubyer:ControlHelper.CornerRadius), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <Border x:Name="effectBorder"
                                    Grid.ColumnSpan="3"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="{Binding Path=(rubyer:ControlHelper.CornerRadius), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                    Effect="{DynamicResource RightBottomEffect}"
                                    Visibility="Collapsed" />

                            <rubyer:ControlMask Background="{Binding Path=(rubyer:ControlHelper.FocusedBrush), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                                CornerRadius="{Binding Path=(rubyer:ControlHelper.CornerRadius), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                                IsActive="{TemplateBinding IsPressed}"
                                                MaskOpacity="{Binding Path=(rubyer:ControlHelper.MaskOpacity), RelativeSource={RelativeSource Mode=TemplatedParent}}" />

                            <ContentPresenter x:Name="contentPresenter"
                                              Margin="{TemplateBinding Padding}"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Focusable="False"
                                              RecognizesAccessKey="True"
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />

                            <Border Grid.Column="1"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="1 0 0 0"
                                    Visibility="{TemplateBinding IsShowSeparator, Converter={StaticResource BooleanToVisibleConverter}}" />

                            <ToggleButton Grid.Column="2"
                                          Padding="5"
                                          rubyer:ControlHelper.FocusedBrush="{Binding Path=(rubyer:ControlHelper.FocusedBrush), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                          rubyer:ControlHelper.MaskOpacity="{Binding Path=(rubyer:ControlHelper.MaskOpacity), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                          Background="Transparent"
                                          ContextMenu="{TemplateBinding DropDownMenu}"
                                          Foreground="{TemplateBinding Foreground}"
                                          IsChecked="{Binding IsDropDownOpen, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                          IsEnabled="{Binding DropDownMenu, RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={StaticResource NotNullConverter}}">
                                <rubyer:Icon x:Name="icon"
                                             Foreground="{TemplateBinding Foreground}"
                                             Type="ArrowDownSLine" />
                            </ToggleButton>
                        </Grid>
                    </Border>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsPressed" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="effectBorder"
                                                         Storyboard.TargetProperty="Effect.ShadowDepth"
                                                         To="0"
                                                         Duration="0:0:0.15">
                                            <DoubleAnimation.EasingFunction>
                                                <CubicEase EasingMode="EaseOut" />
                                            </DoubleAnimation.EasingFunction>
                                        </DoubleAnimation>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="effectBorder"
                                                         Storyboard.TargetProperty="Effect.ShadowDepth"
                                                         Duration="0:0:0.25">
                                            <DoubleAnimation.EasingFunction>
                                                <CubicEase EasingMode="EaseIn" />
                                            </DoubleAnimation.EasingFunction>
                                        </DoubleAnimation>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Opacity" Value="{DynamicResource UnenableOpcity}" />
                        </Trigger>
                        <Trigger Property="rubyer:ButtonHelper.ShowShadow" Value="True">
                            <Setter TargetName="effectBorder" Property="Visibility" Value="Visible" />
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding DependencyObjectType.SystemType, RelativeSource={RelativeSource Self}}" Value="{x:Type Button}" />
                                <Condition Binding="{Binding IsDefault, RelativeSource={RelativeSource Self}}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="effectBorder" Property="Visibility" Value="Visible" />
                            <Setter TargetName="effectBorder" Property="Effect" Value="{DynamicResource AllDirectionEffect3}" />
                        </MultiDataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>