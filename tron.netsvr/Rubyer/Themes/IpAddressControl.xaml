<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:rubyer="clr-namespace:Rubyer">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="pack://application:,,,/Rubyer;component/Themes/TextBox.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="IpAddressTextBox"
           BasedOn="{StaticResource RubyerTextBox}"
           TargetType="TextBox">
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="rubyer:InputBoxHelper.InternalSpacing" Value="0" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="MaxLength" Value="3" />
        <Setter Property="InputMethod.InputScope" Value="Digits" />
        <Setter Property="Padding" Value="0" />
    </Style>

    <Style x:Key="RubyerIpAddressControl" TargetType="{x:Type rubyer:IpAddressControl}">
        <Setter Property="Background" Value="{DynamicResource ContainerBackground}" />
        <Setter Property="BorderBrush" Value="{DynamicResource Border}" />
        <Setter Property="BorderThickness" Value="{DynamicResource DefaultBorderThickness}" />
        <Setter Property="Foreground" Value="{DynamicResource DefaultForeground}" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Stretch" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Padding" Value="5" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="rubyer:ControlHelper.CornerRadius" Value="{DynamicResource AllControlCornerRadius}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type rubyer:IpAddressControl}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{Binding Path=(rubyer:ControlHelper.CornerRadius), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                        <Grid Margin="{TemplateBinding Padding}" rubyer:GridHelper.ColumnDefinitions="*,Auto,*,Auto,*,Auto,*">
                            <TextBox x:Name="PART_Octet1Box"
                                     Style="{StaticResource IpAddressTextBox}"
                                     Text="{Binding Octet1, RelativeSource={RelativeSource Mode=TemplatedParent}, UpdateSourceTrigger=PropertyChanged}" />
                            <TextBlock Grid.Column="1"
                                       Background="{TemplateBinding Background}"
                                       Text="." />
                            <TextBox x:Name="PART_Octet2Box"
                                     Style="{StaticResource IpAddressTextBox}"
                                     Grid.Column="2"
                                     Text="{Binding Octet2, RelativeSource={RelativeSource Mode=TemplatedParent}, UpdateSourceTrigger=PropertyChanged}" />
                            <TextBlock Grid.Column="3" Text="." />
                            <TextBox x:Name="PART_Octet3Box"
                                     Style="{StaticResource IpAddressTextBox}"
                                     Grid.Column="4"
                                     Text="{Binding Octet3, RelativeSource={RelativeSource Mode=TemplatedParent}, UpdateSourceTrigger=PropertyChanged}" />
                            <TextBlock Grid.Column="5" Text="." />
                            <TextBox x:Name="PART_Octet4Box"
                                     Style="{StaticResource IpAddressTextBox}"
                                     Grid.Column="6"
                                     Text="{Binding Octet4, RelativeSource={RelativeSource Mode=TemplatedParent}, UpdateSourceTrigger=PropertyChanged}" />
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>