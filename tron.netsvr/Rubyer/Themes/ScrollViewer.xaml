<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:rubyer="clr-namespace:Rubyer">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="pack://application:,,,/Rubyer;component/Themes/ScrollBar.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="RubyerScrollViewer" TargetType="{x:Type ScrollViewer}">
        <Setter Property="PanningMode" Value="Both" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="rubyer:ControlHelper.CornerRadius" Value="{DynamicResource AllControlCornerRadius}" />
        <Setter Property="rubyer:ControlHelper.MaskOpacity" Value="0.99" />
        <Setter Property="rubyer:ControlHelper.FocusedBrush" Value="{DynamicResource Mask}" />
        <Setter Property="rubyer:ScrollViewerHelper.ScrollBarBackground" Value="Transparent" />
        <Setter Property="rubyer:ScrollViewerHelper.ScrollBarForeground" Value="{DynamicResource ScrollBarBrush}" />
        <Setter Property="rubyer:ScrollViewerHelper.ScrollBarSize" Value="14" />
        <Setter Property="rubyer:ScrollViewerHelper.ShowArrowButton" Value="True" />
        <Setter Property="rubyer:ScrollViewerHelper.ArrowIconBrush" Value="{Binding Foreground, RelativeSource={RelativeSource Self}}" />
        <Setter Property="rubyer:ScrollViewerHelper.IsDynamicBarSize" Value="True" />
        <Setter Property="rubyer:ScrollViewerHelper.CannotScrollDisposeMouseWheel" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollViewer}">
                    <Grid x:Name="Grid" Background="{TemplateBinding Background}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Rectangle x:Name="Corner"
                                   Grid.Row="1"
                                   Grid.Column="1"
                                   Fill="Transparent" />

                        <ScrollContentPresenter x:Name="PART_ScrollContentPresenter"
                                                Margin="{TemplateBinding Padding}"
                                                CanContentScroll="{TemplateBinding CanContentScroll}"
                                                CanHorizontallyScroll="False"
                                                CanVerticallyScroll="False"
                                                Content="{TemplateBinding Content}"
                                                ContentTemplate="{TemplateBinding ContentTemplate}" />

                        <ScrollBar x:Name="PART_HorizontalScrollBar"
                                   Style="{StaticResource RubyerScrollBar}"
                                   Grid.Row="1"
                                   Grid.Column="0"
                                   Panel.ZIndex="100"
                                   AutomationProperties.AutomationId="HorizontalScrollBar"
                                   Cursor="Arrow"
                                   Maximum="{TemplateBinding ScrollableWidth}"
                                   Minimum="0"
                                   Orientation="Horizontal"
                                   ViewportSize="{TemplateBinding ViewportWidth}"
                                   Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                   Value="{Binding HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />

                        <ScrollBar x:Name="PART_VerticalScrollBar"
                                   Style="{StaticResource RubyerScrollBar}"
                                   Grid.Row="0"
                                   Grid.Column="1"
                                   Panel.ZIndex="100"
                                   AutomationProperties.AutomationId="VerticalScrollBar"
                                   Cursor="Arrow"
                                   Maximum="{TemplateBinding ScrollableHeight}"
                                   Minimum="0"
                                   ViewportSize="{TemplateBinding ViewportHeight}"
                                   Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                   Value="{Binding VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="rubyer:ScrollViewerHelper.IsDynamicBarSize" Value="True">
                            <Setter TargetName="PART_ScrollContentPresenter" Property="Grid.RowSpan" Value="2" />
                            <Setter TargetName="PART_ScrollContentPresenter" Property="Grid.ColumnSpan" Value="2" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  小滚动条  -->
    <Style x:Key="SmallScrollViewer"
           BasedOn="{StaticResource RubyerScrollViewer}"
           TargetType="{x:Type ScrollViewer}">
        <Setter Property="rubyer:ScrollViewerHelper.ScrollBarSize" Value="8" />
        <Setter Property="rubyer:ScrollViewerHelper.ShowArrowButton" Value="False" />
    </Style>

    <Style x:Key="RubyerScrollViewerArrowButton"
           BasedOn="{StaticResource RubyerScrollBarArrowButton}"
           TargetType="RepeatButton">
        <Setter Property="FontSize" Value="{Binding Path=FontSize, RelativeSource={RelativeSource AncestorType=ScrollViewer}}" />
        <Setter Property="Foreground" Value="{Binding Path=(rubyer:ScrollViewerHelper.ArrowIconBrush), RelativeSource={RelativeSource AncestorType=ScrollViewer}}" />
        <Setter Property="rubyer:ControlHelper.CornerRadius" Value="{Binding Path=(rubyer:ControlHelper.CornerRadius), RelativeSource={RelativeSource AncestorType=ScrollViewer}}" />
        <Setter Property="rubyer:ControlHelper.FocusedBrush" Value="{Binding Path=(rubyer:ControlHelper.FocusedBrush), RelativeSource={RelativeSource AncestorType=ScrollViewer}}" />
        <Setter Property="rubyer:ControlHelper.MaskOpacity" Value="{Binding Path=(rubyer:ControlHelper.MaskOpacity), RelativeSource={RelativeSource AncestorType=ScrollViewer}}" />
    </Style>

    <!--  只有箭头，没有滚动条  -->
    <Style x:Key="OnlyArrowScrollViewer" TargetType="{x:Type ScrollViewer}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="rubyer:ControlHelper.CornerRadius" Value="{DynamicResource AllControlCornerRadius}" />
        <Setter Property="rubyer:ControlHelper.MaskOpacity" Value="0.6" />
        <Setter Property="rubyer:ControlHelper.FocusedBrush" Value="{DynamicResource Mask}" />
        <Setter Property="rubyer:ScrollViewerHelper.ScrollBarBackground" Value="Transparent" />
        <Setter Property="rubyer:ScrollViewerHelper.ScrollBarForeground" Value="{DynamicResource BorderLighter}" />
        <Setter Property="rubyer:ScrollViewerHelper.ShowArrowButton" Value="True" />
        <Setter Property="rubyer:ScrollViewerHelper.IsOnlyArrow" Value="True" />
        <Setter Property="rubyer:ScrollViewerHelper.HorizontalDelta" Value="100" />
        <Setter Property="rubyer:ScrollViewerHelper.VerticalDelta" Value="100" />
        <Setter Property="rubyer:ScrollViewerHelper.ArrowIconBrush" Value="{Binding Foreground, RelativeSource={RelativeSource Self}}" />
        <Setter Property="rubyer:ScrollViewerHelper.ArrowButtonStyle" Value="{StaticResource RubyerScrollViewerArrowButton}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollViewer}">
                    <Grid x:Name="Grid" Background="{TemplateBinding Background}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <ScrollContentPresenter x:Name="PART_ScrollContentPresenter"
                                                Grid.Row="1"
                                                Grid.Column="1"
                                                Margin="{TemplateBinding Padding}"
                                                CanContentScroll="{TemplateBinding CanContentScroll}"
                                                CanHorizontallyScroll="False"
                                                CanVerticallyScroll="False"
                                                Content="{TemplateBinding Content}"
                                                ContentTemplate="{TemplateBinding ContentTemplate}" />

                        <RepeatButton x:Name="leftButton"
                                      Style="{Binding Path=(rubyer:ScrollViewerHelper.ArrowButtonStyle), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                      Grid.Row="1"
                                      Grid.Column="0"
                                      Height="Auto"
                                      VerticalAlignment="Stretch"
                                      Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}">
                            <rubyer:Icon Type="ArrowLeftSLine" />
                        </RepeatButton>

                        <RepeatButton x:Name="rightButton"
                                      Style="{Binding Path=(rubyer:ScrollViewerHelper.ArrowButtonStyle), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                      Grid.Row="1"
                                      Grid.Column="2"
                                      Height="Auto"
                                      VerticalAlignment="Stretch"
                                      Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}">
                            <rubyer:Icon Type="ArrowRightSLine" />
                        </RepeatButton>

                        <RepeatButton x:Name="upButton"
                                      Style="{Binding Path=(rubyer:ScrollViewerHelper.ArrowButtonStyle), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                      Grid.Row="0"
                                      Grid.Column="1"
                                      Width="Auto"
                                      VerticalAlignment="Stretch"
                                      Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}">
                            <rubyer:Icon Type="ArrowUpSLine" />
                        </RepeatButton>

                        <RepeatButton x:Name="downButton"
                                      Style="{Binding Path=(rubyer:ScrollViewerHelper.ArrowButtonStyle), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                      Grid.Row="2"
                                      Grid.Column="1"
                                      Width="Auto"
                                      VerticalAlignment="Stretch"
                                      Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}">
                            <rubyer:Icon Type="ArrowDownSLine" />
                        </RepeatButton>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="VerticalOffset" Value="0">
                            <Setter TargetName="upButton" Property="IsEnabled" Value="False" />
                        </Trigger>
                        <Trigger Property="HorizontalOffset" Value="0">
                            <Setter TargetName="leftButton" Property="IsEnabled" Value="False" />
                        </Trigger>
                        <DataTrigger Value="True">
                            <DataTrigger.Binding>
                                <MultiBinding Converter="{StaticResource IsEqualConverter}">
                                    <Binding Path="ScrollableHeight" RelativeSource="{RelativeSource Self}" />
                                    <Binding Path="VerticalOffset" RelativeSource="{RelativeSource Self}" />
                                </MultiBinding>
                            </DataTrigger.Binding>
                            <Setter TargetName="downButton" Property="IsEnabled" Value="False" />
                        </DataTrigger>
                        <DataTrigger Value="True">
                            <DataTrigger.Binding>
                                <MultiBinding Converter="{StaticResource IsEqualConverter}">
                                    <Binding Path="ScrollableWidth" RelativeSource="{RelativeSource Self}" />
                                    <Binding Path="HorizontalOffset" RelativeSource="{RelativeSource Self}" />
                                </MultiBinding>
                            </DataTrigger.Binding>
                            <Setter TargetName="rightButton" Property="IsEnabled" Value="False" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>