<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:rubyer="clr-namespace:Rubyer">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="pack://application:,,,/Rubyer;component/Themes/Button.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="RubyerMessageCard" TargetType="{x:Type rubyer:MessageCard}">
        <Setter Property="Background" Value="{DynamicResource FloatBackground}" />
        <Setter Property="BorderBrush" Value="{DynamicResource Border}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="CornerRadius" Value="{DynamicResource AllContainerCornerRadius}" />
        <Setter Property="Foreground" Value="{DynamicResource DefaultForeground}" />
        <Setter Property="ThemeColorBrush" Value="{DynamicResource DefaultForeground}" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="IsShow" Value="True" />
        <Setter Property="Margin" Value="2" />
        <Setter Property="MinWidth" Value="200" />
        <Setter Property="MaxWidth" Value="600" />
        <Setter Property="Padding" Value="5" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="TransitionType" Value="Zoom" />
        <Setter Property="TransitionInitialScale" Value="0.5" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type rubyer:MessageCard}">
                    <rubyer:Transition x:Name="Path_Transition"
                                       InitialScale="{TemplateBinding TransitionInitialScale}"
                                       IsShow="{TemplateBinding IsShow}"
                                       Type="{TemplateBinding TransitionType}">
                        <rubyer:Transition.CloseEasingFunction>
                            <CubicEase EasingMode="EaseIn" />
                        </rubyer:Transition.CloseEasingFunction>
                        <Border x:Name="rootBorder"
                                Margin="{TemplateBinding Margin}"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding ThemeColorBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{TemplateBinding CornerRadius}">
                            <Border.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform />
                                    <SkewTransform />
                                    <RotateTransform />
                                    <TranslateTransform />
                                </TransformGroup>
                            </Border.RenderTransform>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Border Grid.ColumnSpan="3"
                                        Padding="{TemplateBinding Padding}"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding ThemeColorBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="{TemplateBinding CornerRadius}"
                                        Effect="{DynamicResource AllDirectionEffect2}" />

                                <rubyer:Icon x:Name="icon"
                                             Grid.Column="0"
                                             Margin="8 0 0 0"
                                             Foreground="{TemplateBinding ThemeColorBrush}">
                                    <rubyer:Icon.Width>
                                        <MultiBinding Converter="{StaticResource MathAddConverter}">
                                            <Binding Path="FontSize" RelativeSource="{RelativeSource Self}" />
                                            <Binding Source="18" />
                                        </MultiBinding>
                                    </rubyer:Icon.Width>
                                </rubyer:Icon>
                                <ContentPresenter x:Name="contentPresenter"
                                                  Grid.Column="1"
                                                  Margin="8"
                                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  Focusable="False"
                                                  RecognizesAccessKey="True"
                                                  SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                                    <ContentPresenter.Resources>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="TextWrapping" Value="Wrap" />
                                        </Style>
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                                <Button x:Name="PART_CloseButton"
                                        Style="{StaticResource TextButton}"
                                        Grid.Column="2"
                                        Height="Auto"
                                        Padding="5"
                                        VerticalAlignment="Center"
                                        rubyer:ControlHelper.CornerRadius="{TemplateBinding CornerRadius}"
                                        Foreground="{TemplateBinding Foreground}"
                                        Visibility="{TemplateBinding IsClearable, Converter={StaticResource BooleanToVisibleConverter}}">
                                    <rubyer:Icon Type="CloseLine" />
                                </Button>
                            </Grid>
                        </Border>
                    </rubyer:Transition>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Type" Value="None">
                            <Setter TargetName="icon" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="Type" Value="Info">
                            <Setter TargetName="icon" Property="Type" Value="InformationFill" />
                            <Setter Property="ThemeColorBrush" Value="{DynamicResource Info}" />
                        </Trigger>
                        <Trigger Property="Type" Value="Warning">
                            <Setter TargetName="icon" Property="Type" Value="ErrorWarningFill" />
                            <Setter Property="ThemeColorBrush" Value="{DynamicResource Warning}" />
                        </Trigger>
                        <Trigger Property="Type" Value="Success">
                            <Setter TargetName="icon" Property="Type" Value="CheckboxCircleFill" />
                            <Setter Property="ThemeColorBrush" Value="{DynamicResource Success}" />
                        </Trigger>
                        <Trigger Property="Type" Value="Error">
                            <Setter TargetName="icon" Property="Type" Value="CloseCircleFill" />
                            <Setter Property="ThemeColorBrush" Value="{DynamicResource Error}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>