<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:rubyer="clr-namespace:Rubyer">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="pack://application:,,,/Rubyer;component/Themes/TextBox.xaml" />
        <ResourceDictionary Source="pack://application:,,,/Rubyer;component/Themes/Card.xaml" />
        <ResourceDictionary Source="pack://application:,,,/Rubyer;component/Themes/Calendar.xaml" />
        <ResourceDictionary Source="pack://application:,,,/Rubyer;component/Themes/Clock.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="RubyerDateTimePicker" TargetType="{x:Type rubyer:DateTimePicker}">
        <Setter Property="Background" Value="{DynamicResource ControlBackground}" />
        <Setter Property="BorderBrush" Value="{DynamicResource Border}" />
        <Setter Property="Foreground" Value="{DynamicResource DefaultForeground}" />
        <Setter Property="BorderThickness" Value="{DynamicResource DefaultBorderThickness}" />
        <Setter Property="Padding" Value="0 5" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="SelectedDateTimeFormat" Value="yyyy-MM-dd HH:mm" />
        <Setter Property="CalendarStyle" Value="{StaticResource RubyerCalendar}" />
        <Setter Property="ClockStyle" Value="{StaticResource RubyerClock}" />
        <Setter Property="Validation.ErrorTemplate" Value="{StaticResource RubyerValidationErrorTemplate}" />
        <Setter Property="rubyer:ControlHelper.CornerRadius" Value="{DynamicResource AllControlCornerRadius}" />
        <Setter Property="rubyer:ControlHelper.FocusBorderBrush" Value="{DynamicResource Primary}" />
        <Setter Property="rubyer:ControlHelper.MouseOverBrush" Value="{DynamicResource Primary}" />
        <Setter Property="rubyer:HeaderHelper.Background" Value="{DynamicResource Primary}" />
        <Setter Property="rubyer:HeaderHelper.Foreground" Value="{DynamicResource WhiteForeground}" />
        <Setter Property="rubyer:HeaderHelper.FontSize" Value="18" />
        <Setter Property="rubyer:HeaderHelper.FontWeight" Value="Normal" />
        <Setter Property="rubyer:HeaderHelper.HorizontalAlignment" Value="Center" />
        <Setter Property="rubyer:HeaderHelper.VerticalAlignment" Value="Center" />
        <Setter Property="rubyer:HeaderHelper.Padding" Value="5" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="rubyer:InputBoxHelper.SelectAllOnFocus" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type rubyer:DateTimePicker}">
                    <Grid x:Name="PART_Root">
                        <TextBox x:Name="PART_TextBox"
                                 Style="{StaticResource RubyerTextBox}"
                                 Grid.Row="0"
                                 Grid.Column="0"
                                 Height="Auto"
                                 Padding="{TemplateBinding Padding}"
                                 HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                 VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                 rubyer:ControlHelper.CornerRadius="{Binding Path=(rubyer:ControlHelper.CornerRadius), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                 rubyer:ControlHelper.FocusBorderBrush="{Binding Path=(rubyer:ControlHelper.FocusBorderBrush), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                 rubyer:ControlHelper.MouseOverBrush="{Binding Path=(rubyer:ControlHelper.MouseOverBrush), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                 rubyer:InputBoxHelper.InternalSpacing="{Binding Path=(rubyer:InputBoxHelper.InternalSpacing), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                 rubyer:InputBoxHelper.IsClearable="{Binding Path=(rubyer:InputBoxHelper.IsClearable), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                 rubyer:InputBoxHelper.PreContent="{Binding Path=(rubyer:InputBoxHelper.PreContent), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                 rubyer:InputBoxHelper.SelectAllOnFocus="{Binding Path=(rubyer:InputBoxHelper.SelectAllOnFocus), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                 rubyer:InputBoxHelper.Watermark="{Binding Path=(rubyer:InputBoxHelper.Watermark), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                 Background="{TemplateBinding Background}"
                                 BorderBrush="{TemplateBinding BorderBrush}"
                                 BorderThickness="{TemplateBinding BorderThickness}"
                                 Foreground="{TemplateBinding Foreground}">
                            <rubyer:InputBoxHelper.PostContent>
                                <Button x:Name="PART_Button"
                                        Style="{StaticResource TextButton}"
                                        Padding="2"
                                        rubyer:ButtonHelper.Shape="Circle"
                                        Focusable="False"
                                        Foreground="{DynamicResource SeconarydText}">
                                    <rubyer:Icon Type="CalendarTodoFill" />
                                </Button>
                            </rubyer:InputBoxHelper.PostContent>
                        </TextBox>
                        <Grid x:Name="PART_DisabledVisual"
                              Grid.Row="0"
                              IsHitTestVisible="False"
                              Opacity="0">
                            <Popup x:Name="PART_Popup"
                                   AllowsTransparency="True"
                                   Placement="Bottom"
                                   PlacementTarget="{Binding ElementName=PART_TextBox}"
                                   PopupAnimation="Fade"
                                   StaysOpen="False"
                                   VerticalOffset="5">
                                <rubyer:Card Margin="5"
                                             Padding="0"
                                             Background="{DynamicResource FloatBackground}"
                                             BorderThickness="0">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>

                                        <Border x:Name="title"
                                                Grid.Row="0"
                                                Padding="{Binding Path=(rubyer:HeaderHelper.Padding), RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                                CornerRadius="{DynamicResource TopContainerCornerRadius}" />

                                        <Border Grid.Row="1">
                                            <StackPanel Orientation="Horizontal">
                                                <Calendar x:Name="PART_Calendar"
                                                          Style="{TemplateBinding CalendarStyle}"
                                                          Margin="0"
                                                          rubyer:ControlHelper.CornerRadius="0"
                                                          rubyer:HeaderHelper.Background="Transparent"
                                                          rubyer:HeaderHelper.Foreground="{DynamicResource DefaultForeground}"
                                                          Background="Transparent" />
                                                <Border BorderBrush="{DynamicResource BorderLighter}" BorderThickness="1 0 0 0" />
                                                <rubyer:Clock x:Name="PART_Clock"
                                                              Style="{TemplateBinding ClockStyle}"
                                                              Height="{Binding ActualHeight, ElementName=PART_Calendar}"
                                                              Margin="0"
                                                              rubyer:ControlHelper.CornerRadius="0"
                                                              Background="Transparent"
                                                              IsShowConfirmButton="False" />
                                            </StackPanel>
                                        </Border>

                                        <Button x:Name="PART_ConfirmButton"
                                                Grid.Row="2"
                                                Margin="5"
                                                Content="{DynamicResource I18N_Clock_Ok}" />
                                    </Grid>
                                </rubyer:Card>
                            </Popup>
                        </Grid>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="rubyer:InputBoxHelper.IsRound" Value="True">
                            <Setter Property="rubyer:ControlHelper.CornerRadius" Value="{Binding ActualHeight, RelativeSource={RelativeSource Self}, Converter={StaticResource HalfOfDoubleConverter}}" />
                        </Trigger>
                        <Trigger Property="Validation.HasError" Value="True">
                            <Setter Property="BorderBrush" Value="{DynamicResource Error}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Filled DateTimePicker  -->
    <Style x:Key="FilledDateTimePicker"
           BasedOn="{StaticResource RubyerDateTimePicker}"
           TargetType="{x:Type rubyer:DateTimePicker}">
        <Setter Property="BorderBrush" Value="{DynamicResource BorderLighter}" />
        <Setter Property="Background" Value="{DynamicResource BorderLighter}" />
        <Setter Property="rubyer:ControlHelper.MouseOverBrush" Value="{DynamicResource BorderLight}" />
    </Style>
</ResourceDictionary>