<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:Rubyer.Converters"
    xmlns:rubyer="clr-namespace:Rubyer">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="pack://application:,,,/Rubyer;component/Themes/ScrollViewer.xaml" />
        <ResourceDictionary Source="pack://application:,,,/Rubyer;component/Themes/ListBox.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style
        x:Key="{x:Static GridView.GridViewScrollViewerStyleKey}"
        BasedOn="{StaticResource RubyerScrollViewer}"
        TargetType="ScrollViewer">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollViewer">
                    <Grid Background="{TemplateBinding Background}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <ScrollViewer
                            Grid.ColumnSpan="2"
                            Margin="{TemplateBinding Padding}"
                            DockPanel.Dock="Top"
                            Focusable="False"
                            HorizontalScrollBarVisibility="Hidden"
                            VerticalScrollBarVisibility="Hidden">
                            <GridViewHeaderRowPresenter
                                AllowsColumnReorder="{Binding Path=TemplatedParent.View.AllowsColumnReorder, RelativeSource={RelativeSource TemplatedParent}}"
                                ColumnHeaderContainerStyle="{Binding Path=TemplatedParent.View.ColumnHeaderContainerStyle, RelativeSource={RelativeSource TemplatedParent}}"
                                ColumnHeaderContextMenu="{Binding Path=TemplatedParent.View.ColumnHeaderContextMenu, RelativeSource={RelativeSource TemplatedParent}}"
                                ColumnHeaderTemplate="{Binding Path=TemplatedParent.View.ColumnHeaderTemplate, RelativeSource={RelativeSource TemplatedParent}}"
                                ColumnHeaderTemplateSelector="{Binding Path=TemplatedParent.View.ColumnHeaderTemplateSelector, RelativeSource={RelativeSource TemplatedParent}}"
                                ColumnHeaderToolTip="{Binding Path=TemplatedParent.View.ColumnHeaderToolTip, RelativeSource={RelativeSource TemplatedParent}}"
                                Columns="{Binding Path=TemplatedParent.View.Columns, RelativeSource={RelativeSource TemplatedParent}}"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </ScrollViewer>


                        <ScrollContentPresenter
                            Name="PART_ScrollContentPresenter"
                            Grid.Row="1"
                            Grid.RowSpan="2"
                            Grid.ColumnSpan="2"
                            CanContentScroll="True"
                            CanHorizontallyScroll="False"
                            CanVerticallyScroll="False"
                            KeyboardNavigation.DirectionalNavigation="Local" />

                        <ScrollBar
                            Name="PART_HorizontalScrollBar"
                            Grid.Row="2"
                            Maximum="{TemplateBinding ScrollableWidth}"
                            Orientation="Horizontal"
                            Style="{StaticResource RubyerScrollBar}"
                            ViewportSize="{TemplateBinding ViewportWidth}"
                            Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                            Value="{TemplateBinding HorizontalOffset}" />

                        <ScrollBar
                            Name="PART_VerticalScrollBar"
                            Grid.Row="1"
                            Grid.Column="1"
                            Maximum="{TemplateBinding ScrollableHeight}"
                            Style="{StaticResource RubyerScrollBar}"
                            ViewportSize="{TemplateBinding ViewportHeight}"
                            Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                            Value="{TemplateBinding VerticalOffset}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="GridViewColumnHeaderGripper" TargetType="{x:Type Thumb}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Cursor" Value="SizeWE" />
        <Setter Property="Width" Value="5" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border Padding="{TemplateBinding Padding}" Background="{TemplateBinding Background}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="{x:Type GridViewColumnHeader}" TargetType="GridViewColumnHeader">
        <Setter Property="Background" Value="{Binding Path=(rubyer:HeaderHelper.Background), RelativeSource={RelativeSource AncestorType=ItemsControl}}" />
        <Setter Property="Foreground" Value="{Binding Path=(rubyer:HeaderHelper.Foreground), RelativeSource={RelativeSource AncestorType=ItemsControl}}" />
        <Setter Property="HorizontalContentAlignment" Value="{Binding Path=(rubyer:HeaderHelper.HorizontalAlignment), RelativeSource={RelativeSource AncestorType=ItemsControl}}" />
        <Setter Property="VerticalContentAlignment" Value="{Binding Path=(rubyer:HeaderHelper.VerticalAlignment), RelativeSource={RelativeSource AncestorType=ItemsControl}}" />
        <Setter Property="Padding" Value="{Binding Path=(rubyer:HeaderHelper.Padding), RelativeSource={RelativeSource AncestorType=ItemsControl}}" />
        <Setter Property="FontFamily" Value="{Binding Path=(rubyer:HeaderHelper.FontFamily), RelativeSource={RelativeSource AncestorType=ItemsControl}}" />
        <Setter Property="FontSize" Value="{Binding Path=(rubyer:HeaderHelper.FontSize), RelativeSource={RelativeSource AncestorType=ItemsControl}}" />
        <Setter Property="FontWeight" Value="{Binding Path=(rubyer:HeaderHelper.FontWeight), RelativeSource={RelativeSource AncestorType=ItemsControl}}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GridViewColumnHeader">
                    <Grid>
                        <Border
                            x:Name="HeaderBorder"
                            Background="{TemplateBinding Background}"
                            BorderThickness="0">
                            <ContentPresenter
                                x:Name="HeaderContent"
                                Margin="{TemplateBinding Padding}"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                RecognizesAccessKey="True"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </Border>

                        <Thumb
                            x:Name="PART_HeaderGripper"
                            HorizontalAlignment="Right"
                            Style="{StaticResource GridViewColumnHeaderGripper}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="RubyerListViewItem" TargetType="{x:Type ListViewItem}">
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Padding" Value="{Binding Path=(rubyer:ItemsControlHelper.ItemPadding), RelativeSource={RelativeSource AncestorType=ListView}}" />
        <Setter Property="Margin" Value="{Binding Path=(rubyer:ItemsControlHelper.ItemMargin), RelativeSource={RelativeSource AncestorType=ListView}}" />
        <Setter Property="HorizontalContentAlignment" Value="{Binding HorizontalContentAlignment, FallbackValue=Stretch, RelativeSource={RelativeSource AncestorType=ListView}}" />
        <Setter Property="VerticalContentAlignment" Value="{Binding VerticalContentAlignment, FallbackValue=Center, RelativeSource={RelativeSource AncestorType=ListView}}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListViewItem}">
                    <Grid x:Name="root">
                        <rubyer:ControlMask
                            Background="{Binding Path=(rubyer:ControlHelper.FocusedBrush), RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                            CornerRadius="{Binding Path=(rubyer:ControlHelper.CornerRadius), RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                            IsActive="{TemplateBinding IsSelected}"
                            MaskOpacity="{Binding Path=(rubyer:ControlHelper.MaskOpacity), RelativeSource={RelativeSource AncestorType=ItemsControl}}" />

                        <Border
                            x:Name="Border"
                            Background="Transparent"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <GridViewRowPresenter
                                x:Name="content"
                                Margin="{TemplateBinding Padding}"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </Border>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="root" Property="Opacity" Value="{DynamicResource UnenableOpcity}" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Foreground" Value="{Binding Path=(rubyer:ControlHelper.FocusedForegroundBrush), RelativeSource={RelativeSource AncestorType=ListView}}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <converter:ListViewGridViewConverter
        x:Key="ListViewGridViewConverter"
        DefaultStyle="{StaticResource RubyerListBoxItem}"
        ViewStyle="{StaticResource RubyerListViewItem}" />

    <Style x:Key="RubyerListView" TargetType="{x:Type ListView}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{DynamicResource Border}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Foreground" Value="{DynamicResource DefaultForeground}" />
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="ScrollViewer.CanContentScroll" Value="True" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="ItemContainerStyle" Value="{Binding View, RelativeSource={RelativeSource Self}, Converter={StaticResource ListViewGridViewConverter}}" />
        <Setter Property="rubyer:HeaderHelper.Background" Value="{DynamicResource HeaderBackground}" />
        <Setter Property="rubyer:HeaderHelper.Foreground" Value="{DynamicResource DefaultForeground}" />
        <Setter Property="rubyer:HeaderHelper.FontSize" Value="{Binding FontSize, RelativeSource={RelativeSource Self}}" />
        <Setter Property="rubyer:HeaderHelper.FontWeight" Value="Normal" />
        <Setter Property="rubyer:HeaderHelper.HorizontalAlignment" Value="Stretch" />
        <Setter Property="rubyer:HeaderHelper.VerticalAlignment" Value="Top" />
        <Setter Property="rubyer:HeaderHelper.Padding" Value="5" />
        <Setter Property="rubyer:ControlHelper.FocusedBrush" Value="{DynamicResource Mask}" />
        <Setter Property="rubyer:ControlHelper.FocusedForegroundBrush" Value="{DynamicResource DefaultForeground}" />
        <Setter Property="rubyer:ControlHelper.CornerRadius" Value="0" />
        <Setter Property="rubyer:ControlHelper.MaskOpacity" Value="0.6" />
        <Setter Property="rubyer:ItemsControlHelper.ItemMargin" Value="0 1" />
        <Setter Property="rubyer:ItemsControlHelper.ItemPadding" Value="0 5" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListView">
                    <Border
                        Name="Border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                        <ScrollViewer Style="{DynamicResource {x:Static GridView.GridViewScrollViewerStyleKey}}">
                            <ItemsPresenter />
                        </ScrollViewer>
                    </Border>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsGrouping" Value="True" />
                                <Condition Property="VirtualizingPanel.IsVirtualizingWhenGrouping" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter Property="ScrollViewer.CanContentScroll" Value="False" />
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="{DynamicResource UnenableOpcity}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>