<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:converters="clr-namespace:Rubyer.Converters"
                    xmlns:rubyer="clr-namespace:Rubyer">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="pack://application:,,,/Rubyer;component/Themes/Resources/Default.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <converters:AutoToolTipPlacementToPlacementModeConverter x:Key="AutoToolTipPlacementToPlacementModeConverter" />

    <Style x:Key="RepeatButtonTransparent" TargetType="{x:Type RepeatButton}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <Rectangle Width="{TemplateBinding Width}"
                               Height="{TemplateBinding Height}"
                               Fill="{TemplateBinding Foreground}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Horizontal  -->
    <ControlTemplate x:Key="SliderThumbHorizontalDefault" TargetType="{x:Type Thumb}">
        <Grid HorizontalAlignment="Center"
              VerticalAlignment="Center"
              UseLayoutRounding="True">
            <Ellipse x:Name="grip"
                     Grid.Row="1"
                     Width="{Binding Path=(rubyer:SliderHelper.GripDiameter), RelativeSource={RelativeSource AncestorType=Slider}}"
                     Height="{Binding Path=(rubyer:SliderHelper.GripDiameter), RelativeSource={RelativeSource AncestorType=Slider}}"
                     VerticalAlignment="Center"
                     Effect="{DynamicResource AllDirectionEffect}"
                     Fill="{TemplateBinding Foreground}"
                     SnapsToDevicePixels="True" />
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetName="grip"
                                             Storyboard.TargetProperty="Effect.BlurRadius"
                                             To="10"
                                             Duration="0:0:0.2" />
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetName="grip"
                                             Storyboard.TargetProperty="Effect.BlurRadius"
                                             Duration="0:0:0.2" />
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.ExitActions>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetName="grip"
                                             Storyboard.TargetProperty="Effect.BlurRadius"
                                             To="2"
                                             Duration="0:0:0.2">
                                <DoubleAnimation.EasingFunction>
                                    <CubicEase EasingMode="EaseOut" />
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetName="grip"
                                             Storyboard.TargetProperty="Effect.BlurRadius"
                                             Duration="0:0:0.25">
                                <DoubleAnimation.EasingFunction>
                                    <CubicEase EasingMode="EaseOut" />
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.ExitActions>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SliderHorizontal" TargetType="{x:Type Slider}">
        <Border x:Name="border"
                Background="Transparent"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{TemplateBinding BorderThickness}"
                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" MinHeight="{TemplateBinding MinHeight}" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <!--  上标  -->
                <TickBar x:Name="TopTick"
                         Grid.Row="0"
                         Height="4"
                         Margin="0 0 0 4"
                         Fill="{TemplateBinding Foreground}"
                         Placement="Top"
                         Visibility="Collapsed" />
                <!--  下标  -->
                <TickBar x:Name="BottomTick"
                         Grid.Row="2"
                         Height="4"
                         Margin="0 2 0 0"
                         Fill="{TemplateBinding Foreground}"
                         Placement="Bottom"
                         Visibility="Collapsed" />
                <!--  轨道  -->
                <Border x:Name="TrackBackground"
                        Grid.Row="1"
                        Height="{Binding Path=(rubyer:SliderHelper.TrackThickness), RelativeSource={RelativeSource AncestorType=Slider}}"
                        Margin="0"
                        VerticalAlignment="center"
                        Background="{TemplateBinding Background}"
                        BorderThickness="0">
                    <Canvas>
                        <Button x:Name="PART_SelectionRange"
                                Height="{Binding Path=(rubyer:SliderHelper.TrackThickness), RelativeSource={RelativeSource AncestorType=Slider}}"
                                Background="{TemplateBinding Foreground}"
                                Visibility="Hidden" />
                        <Button x:Name="StartRangeButton"
                                Canvas.Left="{Binding Path=(Canvas.Left), ElementName=PART_SelectionRange}"
                                Height="{Binding Path=(rubyer:SliderHelper.GripDiameter), RelativeSource={RelativeSource AncestorType=Slider}}"
                                rubyer:ButtonHelper.Shape="Circle"
                                rubyer:ButtonHelper.ShowShadow="True"
                                Background="{TemplateBinding Foreground}"
                                ToolTipService.InitialShowDelay="10"
                                ToolTipService.PlacementTarget="{Binding RelativeSource={RelativeSource AncestorType=Slider}}"
                                Visibility="Collapsed">
                            <Button.RenderTransform>
                                <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Button}, Converter={StaticResource DivideConverter}, ConverterParameter=-2}">
                                    <TranslateTransform.Y>
                                        <MultiBinding Converter="{StaticResource MathSubtractConverter}">
                                            <Binding Converter="{StaticResource DivideConverter}"
                                                     ConverterParameter="-2"
                                                     Path="ActualHeight"
                                                     RelativeSource="{RelativeSource AncestorType=Button}" />
                                            <Binding Converter="{StaticResource DivideConverter}"
                                                     ConverterParameter="-2"
                                                     ElementName="PART_SelectionRange"
                                                     Path="ActualHeight" />
                                        </MultiBinding>
                                    </TranslateTransform.Y>
                                </TranslateTransform>
                            </Button.RenderTransform>
                            <Button.ToolTip>
                                <ToolTip x:Name="StartToolTip" IsOpen="False">
                                    <ToolTip.Placement>
                                        <MultiBinding Converter="{StaticResource AutoToolTipPlacementToPlacementModeConverter}">
                                            <Binding Path="AutoToolTipPlacement" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                            <Binding Path="Orientation" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                        </MultiBinding>
                                    </ToolTip.Placement>
                                    <MultiBinding Converter="{StaticResource GetPrecisionValueConverter}">
                                        <Binding Path="SelectionStart" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                        <Binding Path="AutoToolTipPrecision" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                    </MultiBinding>
                                </ToolTip>
                            </Button.ToolTip>
                        </Button>

                        <Button x:Name="EndRangeButton"
                                Height="{Binding Path=(rubyer:SliderHelper.GripDiameter), RelativeSource={RelativeSource AncestorType=Slider}}"
                                rubyer:ButtonHelper.Shape="Circle"
                                rubyer:ButtonHelper.ShowShadow="True"
                                Background="{TemplateBinding Foreground}"
                                ToolTipService.InitialShowDelay="10"
                                ToolTipService.PlacementTarget="{Binding RelativeSource={RelativeSource AncestorType=Slider}}"
                                Visibility="Collapsed">
                            <Button.RenderTransform>
                                <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Button}, Converter={StaticResource DivideConverter}, ConverterParameter=-2}">
                                    <TranslateTransform.Y>
                                        <MultiBinding Converter="{StaticResource MathSubtractConverter}">
                                            <Binding Converter="{StaticResource DivideConverter}"
                                                     ConverterParameter="-2"
                                                     Path="ActualHeight"
                                                     RelativeSource="{RelativeSource AncestorType=Button}" />
                                            <Binding Converter="{StaticResource DivideConverter}"
                                                     ConverterParameter="-2"
                                                     ElementName="PART_SelectionRange"
                                                     Path="ActualHeight" />
                                        </MultiBinding>
                                    </TranslateTransform.Y>
                                </TranslateTransform>
                            </Button.RenderTransform>
                            <Canvas.Left>
                                <MultiBinding Converter="{StaticResource MathAddConverter}">
                                    <Binding ElementName="PART_SelectionRange" Path="(Canvas.Left)" />
                                    <Binding ElementName="PART_SelectionRange" Path="ActualWidth" />
                                </MultiBinding>
                            </Canvas.Left>
                            <Button.ToolTip>
                                <ToolTip x:Name="EndToolTip" IsOpen="False">
                                    <ToolTip.Placement>
                                        <MultiBinding Converter="{StaticResource AutoToolTipPlacementToPlacementModeConverter}">
                                            <Binding Path="AutoToolTipPlacement" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                            <Binding Path="Orientation" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                        </MultiBinding>
                                    </ToolTip.Placement>
                                    <MultiBinding Converter="{StaticResource GetPrecisionValueConverter}">
                                        <Binding Path="SelectionEnd" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                        <Binding Path="AutoToolTipPrecision" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                    </MultiBinding>
                                </ToolTip>
                            </Button.ToolTip>
                        </Button>
                    </Canvas>
                </Border>

                <Track x:Name="PART_Track"
                       Grid.Row="1"
                       Cursor="Hand">
                    <Track.DecreaseRepeatButton>
                        <RepeatButton Style="{StaticResource RepeatButtonTransparent}"
                                      Height="{Binding Path=(rubyer:SliderHelper.TrackThickness), RelativeSource={RelativeSource AncestorType=Slider}}"
                                      Command="{x:Static Slider.DecreaseLarge}" />
                    </Track.DecreaseRepeatButton>
                    <Track.IncreaseRepeatButton>
                        <RepeatButton Style="{StaticResource RepeatButtonTransparent}"
                                      Height="{Binding Path=(rubyer:SliderHelper.TrackThickness), RelativeSource={RelativeSource AncestorType=Slider}}"
                                      Command="{x:Static Slider.IncreaseLarge}"
                                      Foreground="Transparent" />
                    </Track.IncreaseRepeatButton>
                    <Track.Thumb>
                        <Thumb x:Name="Thumb"
                               Width="{Binding Path=(rubyer:SliderHelper.GripDiameter), RelativeSource={RelativeSource AncestorType=Slider}}"
                               Height="{Binding Path=(rubyer:SliderHelper.GripDiameter), RelativeSource={RelativeSource AncestorType=Slider}}"
                               VerticalAlignment="Center"
                               Focusable="False"
                               Foreground="{TemplateBinding Foreground}"
                               OverridesDefaultStyle="True"
                               Template="{StaticResource SliderThumbHorizontalDefault}" />
                    </Track.Thumb>
                </Track>
            </Grid>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="TickPlacement" Value="TopLeft">
                <Setter TargetName="TopTick" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger Property="TickPlacement" Value="BottomRight">
                <Setter TargetName="BottomTick" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger Property="TickPlacement" Value="Both">
                <Setter TargetName="BottomTick" Property="Visibility" Value="Visible" />
                <Setter TargetName="TopTick" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger Property="IsSelectionRangeEnabled" Value="true">
                <Setter TargetName="PART_SelectionRange" Property="Visibility" Value="Visible" />
                <Setter TargetName="StartRangeButton" Property="Visibility" Value="Visible" />
                <Setter TargetName="EndRangeButton" Property="Visibility" Value="Visible" />
                <Setter TargetName="PART_Track" Property="Visibility" Value="Hidden" />
            </Trigger>
            <Trigger Property="AutoToolTipPlacement" Value="None">
                <Setter TargetName="StartRangeButton" Property="ToolTipService.IsEnabled" Value="False" />
                <Setter TargetName="EndRangeButton" Property="ToolTipService.IsEnabled" Value="False" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter TargetName="border" Property="Opacity" Value="{StaticResource UnenableOpcity}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  Vertical  -->
    <ControlTemplate x:Key="SliderThumbVerticalDefault" TargetType="{x:Type Thumb}">
        <Grid HorizontalAlignment="Center"
              VerticalAlignment="Center"
              UseLayoutRounding="True">
            <Ellipse x:Name="grip"
                     Width="{Binding Path=(rubyer:SliderHelper.GripDiameter), RelativeSource={RelativeSource AncestorType=Slider}}"
                     Height="{Binding Path=(rubyer:SliderHelper.GripDiameter), RelativeSource={RelativeSource AncestorType=Slider}}"
                     VerticalAlignment="Center"
                     Effect="{DynamicResource AllDirectionEffect}"
                     Fill="{TemplateBinding Foreground}"
                     SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetName="grip"
                                             Storyboard.TargetProperty="Effect.BlurRadius"
                                             To="10"
                                             Duration="0:0:0.2" />
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetName="grip"
                                             Storyboard.TargetProperty="Effect.BlurRadius"
                                             Duration="0:0:0.2" />
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.ExitActions>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetName="grip"
                                             Storyboard.TargetProperty="Effect.BlurRadius"
                                             To="2"
                                             Duration="0:0:0.2">
                                <DoubleAnimation.EasingFunction>
                                    <CubicEase EasingMode="EaseOut" />
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetName="grip"
                                             Storyboard.TargetProperty="Effect.BlurRadius"
                                             Duration="0:0:0.25">
                                <DoubleAnimation.EasingFunction>
                                    <CubicEase EasingMode="EaseOut" />
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.ExitActions>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SliderVertical" TargetType="{x:Type Slider}">
        <Border x:Name="border"
                Background="Transparent"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{TemplateBinding BorderThickness}"
                SnapsToDevicePixels="True">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" MinWidth="{TemplateBinding MinWidth}" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <TickBar x:Name="TopTick"
                         Grid.Column="0"
                         Width="4"
                         Margin="0 0 4 0"
                         Fill="{TemplateBinding Foreground}"
                         Placement="Left"
                         Visibility="Collapsed" />
                <TickBar x:Name="BottomTick"
                         Grid.Column="2"
                         Width="4"
                         Margin="2 0 0 0"
                         Fill="{TemplateBinding Foreground}"
                         Placement="Right"
                         Visibility="Collapsed" />
                <Border x:Name="TrackBackground"
                        Grid.Column="1"
                        Width="{Binding Path=(rubyer:SliderHelper.TrackThickness), RelativeSource={RelativeSource AncestorType=Slider}}"
                        Margin="0"
                        HorizontalAlignment="center"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0">
                    <Canvas Margin="0">
                        <Button x:Name="PART_SelectionRange"
                                Width="{Binding Path=(rubyer:SliderHelper.TrackThickness), RelativeSource={RelativeSource AncestorType=Slider}}"
                                Background="{TemplateBinding Foreground}"
                                Visibility="Hidden" />

                        <Button x:Name="StartRangeButton"
                                Height="{Binding Path=(rubyer:SliderHelper.GripDiameter), RelativeSource={RelativeSource AncestorType=Slider}}"
                                rubyer:ButtonHelper.Shape="Circle"
                                rubyer:ButtonHelper.ShowShadow="True"
                                Background="{TemplateBinding Foreground}"
                                ToolTipService.InitialShowDelay="10"
                                ToolTipService.PlacementTarget="{Binding RelativeSource={RelativeSource AncestorType=Slider}}"
                                Visibility="Collapsed">
                            <Button.RenderTransform>
                                <TranslateTransform Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Button}, Converter={StaticResource DivideConverter}, ConverterParameter=-2}">
                                    <TranslateTransform.X>
                                        <MultiBinding Converter="{StaticResource MathSubtractConverter}">
                                            <Binding Converter="{StaticResource DivideConverter}"
                                                     ConverterParameter="-2"
                                                     Path="ActualWidth"
                                                     RelativeSource="{RelativeSource AncestorType=Button}" />
                                            <Binding Converter="{StaticResource DivideConverter}"
                                                     ConverterParameter="-2"
                                                     ElementName="PART_SelectionRange"
                                                     Path="ActualWidth" />
                                        </MultiBinding>
                                    </TranslateTransform.X>
                                </TranslateTransform>
                            </Button.RenderTransform>
                            <Canvas.Top>
                                <MultiBinding Converter="{StaticResource MathAddConverter}">
                                    <Binding ElementName="PART_SelectionRange" Path="(Canvas.Top)" />
                                    <Binding ElementName="PART_SelectionRange" Path="ActualHeight" />
                                </MultiBinding>
                            </Canvas.Top>
                            <Button.ToolTip>
                                <ToolTip IsOpen="False">
                                    <ToolTip.Placement>
                                        <MultiBinding Converter="{StaticResource AutoToolTipPlacementToPlacementModeConverter}">
                                            <Binding Path="AutoToolTipPlacement" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                            <Binding Path="Orientation" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                        </MultiBinding>
                                    </ToolTip.Placement>
                                    <MultiBinding Converter="{StaticResource GetPrecisionValueConverter}">
                                        <Binding Path="SelectionStart" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                        <Binding Path="AutoToolTipPrecision" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                    </MultiBinding>
                                </ToolTip>
                            </Button.ToolTip>
                        </Button>

                        <Button x:Name="EndRangeButton"
                                Canvas.Top="{Binding Path=(Canvas.Top), ElementName=PART_SelectionRange}"
                                Height="{Binding Path=(rubyer:SliderHelper.GripDiameter), RelativeSource={RelativeSource AncestorType=Slider}}"
                                rubyer:ButtonHelper.Shape="Circle"
                                rubyer:ButtonHelper.ShowShadow="True"
                                Background="{TemplateBinding Foreground}"
                                ToolTipService.InitialShowDelay="10"
                                ToolTipService.PlacementTarget="{Binding RelativeSource={RelativeSource AncestorType=Slider}}"
                                Visibility="Collapsed">
                            <Button.RenderTransform>
                                <TranslateTransform Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Button}, Converter={StaticResource DivideConverter}, ConverterParameter=-2}">
                                    <TranslateTransform.X>
                                        <MultiBinding Converter="{StaticResource MathSubtractConverter}">
                                            <Binding Converter="{StaticResource DivideConverter}"
                                                     ConverterParameter="-2"
                                                     Path="ActualWidth"
                                                     RelativeSource="{RelativeSource AncestorType=Button}" />
                                            <Binding Converter="{StaticResource DivideConverter}"
                                                     ConverterParameter="-2"
                                                     ElementName="PART_SelectionRange"
                                                     Path="ActualWidth" />
                                        </MultiBinding>
                                    </TranslateTransform.X>
                                </TranslateTransform>
                            </Button.RenderTransform>
                            <Button.ToolTip>
                                <ToolTip IsOpen="False">
                                    <ToolTip.Placement>
                                        <MultiBinding Converter="{StaticResource AutoToolTipPlacementToPlacementModeConverter}">
                                            <Binding Path="AutoToolTipPlacement" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                            <Binding Path="Orientation" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                        </MultiBinding>
                                    </ToolTip.Placement>
                                    <MultiBinding Converter="{StaticResource GetPrecisionValueConverter}">
                                        <Binding Path="SelectionEnd" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                        <Binding Path="AutoToolTipPrecision" RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                    </MultiBinding>
                                </ToolTip>
                            </Button.ToolTip>
                        </Button>
                    </Canvas>
                </Border>
                <Track x:Name="PART_Track"
                       Grid.Column="1"
                       Cursor="Hand">
                    <Track.DecreaseRepeatButton>
                        <RepeatButton Style="{StaticResource RepeatButtonTransparent}"
                                      Width="{Binding Path=(rubyer:SliderHelper.TrackThickness), RelativeSource={RelativeSource AncestorType=Slider}}"
                                      Command="{x:Static Slider.DecreaseLarge}" />
                    </Track.DecreaseRepeatButton>
                    <Track.IncreaseRepeatButton>
                        <RepeatButton Style="{StaticResource RepeatButtonTransparent}"
                                      Width="{Binding Path=(rubyer:SliderHelper.TrackThickness), RelativeSource={RelativeSource AncestorType=Slider}}"
                                      Command="{x:Static Slider.IncreaseLarge}"
                                      Foreground="Transparent" />
                    </Track.IncreaseRepeatButton>
                    <Track.Thumb>
                        <Thumb x:Name="Thumb"
                               Width="{Binding Path=(rubyer:SliderHelper.GripDiameter), RelativeSource={RelativeSource AncestorType=Slider}}"
                               Height="{Binding Path=(rubyer:SliderHelper.GripDiameter), RelativeSource={RelativeSource AncestorType=Slider}}"
                               VerticalAlignment="Top"
                               Focusable="False"
                               Foreground="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Slider}}"
                               OverridesDefaultStyle="True"
                               Template="{StaticResource SliderThumbVerticalDefault}" />
                    </Track.Thumb>
                </Track>
            </Grid>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="TickPlacement" Value="TopLeft">
                <Setter TargetName="TopTick" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger Property="TickPlacement" Value="BottomRight">
                <Setter TargetName="BottomTick" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger Property="TickPlacement" Value="Both">
                <Setter TargetName="BottomTick" Property="Visibility" Value="Visible" />
                <Setter TargetName="TopTick" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger Property="IsSelectionRangeEnabled" Value="true">
                <Setter TargetName="PART_SelectionRange" Property="Visibility" Value="Visible" />
                <Setter TargetName="StartRangeButton" Property="Visibility" Value="Visible" />
                <Setter TargetName="EndRangeButton" Property="Visibility" Value="Visible" />
                <Setter TargetName="PART_Track" Property="Visibility" Value="Hidden" />
            </Trigger>
            <Trigger Property="AutoToolTipPlacement" Value="None">
                <Setter TargetName="StartRangeButton" Property="ToolTipService.IsEnabled" Value="False" />
                <Setter TargetName="EndRangeButton" Property="ToolTipService.IsEnabled" Value="False" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter TargetName="border" Property="Opacity" Value="{StaticResource UnenableOpcity}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="RubyerSlider" TargetType="{x:Type Slider}">
        <Setter Property="Background" Value="{DynamicResource BorderLight}" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Foreground" Value="{DynamicResource Primary}" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Stylus.IsPressAndHoldEnabled" Value="false" />
        <Setter Property="FocusVisualStyle" Value="{StaticResource FocusVisual}" />
        <Setter Property="Template" Value="{StaticResource SliderHorizontal}" />
        <Setter Property="rubyer:SliderHelper.GripDiameter" Value="15" />
        <Setter Property="rubyer:SliderHelper.TrackThickness" Value="4" />
        <Setter Property="rubyer:SliderHelper.IsSelectionRange" Value="{Binding IsSelectionRangeEnabled, RelativeSource={RelativeSource Self}}" />
        <Style.Triggers>
            <Trigger Property="Orientation" Value="Vertical">
                <Setter Property="Template" Value="{StaticResource SliderVertical}" />
            </Trigger>
        </Style.Triggers>
    </Style>
</ResourceDictionary>