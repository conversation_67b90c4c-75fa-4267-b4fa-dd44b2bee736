using Grpc.Core;
using GrpcGeyser;

namespace Tran.Abp.Ws.RaydiumCpmm.GRpc;

public class RaydiumCpmmPool : AsyncPeriodicBackgroundWorkerBase
{
    private IRedisClient _redisClient;
    private Geyser.GeyserClient client;
    private AsyncDuplexStreamingCall<SubscribeRequest, SubscribeUpdate> stream;
    public ILogger<RaydiumCpmmPool> Logger { get; set; }
    private SubscribeRequest request;
    private DateTime _lastMessageTime; // 标记最后收到消息的时间
    public RaydiumCpmmPool(IRedisClient redisClient, Geyser.GeyserClient client,AbpAsyncTimer timer, IServiceScopeFactory serviceScopeFactory): base(timer, serviceScopeFactory)
    {
        Timer.Period = 5*60*1000; //5s 执行一下次
        _redisClient = redisClient;
        this.client = client;
        _lastMessageTime=DateTime.Now;
    }
    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {
        var timeSinceLastMessage = DateTime.Now - _lastMessageTime;
        Logger.LogWarning($"ws 断开 1s 后连接 {_lastMessageTime}=>{DateTime.Now}=>{timeSinceLastMessage.TotalSeconds}");
        if (timeSinceLastMessage.TotalSeconds >= 60)
        {
            Logger.LogWarning($"ws 断开 1s 后连接");
            await UnSubscribe(new CancellationToken());
            await Task.Delay(1000);
            await Subscribe(new CancellationToken());
        }

    }

    private async Task Subscribe(CancellationToken cancellationToken)
    {
        request.Accounts.Add("cpmmpoll", new SubscribeRequestFilterAccounts()
        {
            Owner = { "CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C" },
            Filters =
            {
                new SubscribeRequestFilterAccountsFilter() { Datasize = 637 }
            },
            NonemptyTxnSignature = true
        });
        request.Accounts.Add("cpmmprice", new SubscribeRequestFilterAccounts()
        {
            Owner = { "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA" },
            Filters =
            {
                new SubscribeRequestFilterAccountsFilter()
                {
                    Datasize = 165,
                    Memcmp = new SubscribeRequestFilterAccountsFilterMemcmp()
                    {
                        Offset = 32, Base58 = "GpMZbSM2GgvTKHJirzeGfMFoaZ8UR2X7F4v8vHTvxFbL"
                    }
                }
            }
        });
        await stream.RequestStream.WriteAsync(request, cancellationToken);
    }
    private async Task UnSubscribe(CancellationToken cancellationToken)
    {
        request.Accounts.Clear();
        await stream.RequestStream.WriteAsync(request, cancellationToken);
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        stream = client.Subscribe();
        Task.Run(() => OnSubscribe(cancellationToken), cancellationToken);
        request = new SubscribeRequest
        {
            Commitment = CommitmentLevel.Processed
        };
        await Subscribe(cancellationToken);
        Logger.LogDebug("获取 cpmm pool  服务=>启动");
        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        stream.Dispose();
        Logger.LogDebug("获取 cpmm pool  服务=>停止");
        await base.StopAsync(cancellationToken);
    }

    private async Task PoolHandler(string pubkey, byte[] data)
    {
        var offset = 8;
        var ammConfig = Base58.Encode(data.AsSpan().Slice(offset, 32).ToArray());
        offset += 32;
        var poolCreator = Base58.Encode(data.AsSpan().Slice(offset, 32).ToArray());
        offset += 32;
        var token0Vault = Base58.Encode(data.AsSpan().Slice(offset, 32).ToArray());
        offset += 32;
        var token1Vault = Base58.Encode(data.AsSpan().Slice(offset, 32).ToArray());
        offset += 32;
        var lpMint = Base58.Encode(data.AsSpan().Slice(offset, 32).ToArray());
        offset += 32;
        var token0Mint = Base58.Encode(data.AsSpan().Slice(offset, 32).ToArray());
        offset += 32;
        var token1Mint = Base58.Encode(data.AsSpan().Slice(offset, 32).ToArray());
        offset += 32;
        var token0Program = Base58.Encode(data.AsSpan().Slice(offset, 32).ToArray());
        offset += 32; //token0Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA
        var token1Program = Base58.Encode(data.AsSpan().Slice(offset, 32).ToArray());
        offset += 32; //token1Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA
        var observationKey = Base58.Encode(data.AsSpan().Slice(offset, 32).ToArray());
        offset += 32;
        var authBump = (int)(data.AsSpan()[offset]);
        offset += 1;
        var status = (int)(data.AsSpan()[offset]);
        offset += 1;
        var lpMintDecimals = (int)(data.AsSpan()[offset]);
        offset += 1;
        var mint0Decimals = (int)(data.AsSpan()[offset]);
        offset += 1;
        var mint1Decimals = (int)(data.AsSpan()[offset]);
        offset += 1;
        var lpSupply = BitConverter.ToUInt64(data.AsSpan().Slice(offset, 8).ToArray(), 0);
        offset += 8;
        var protocolFeesToken0 = BitConverter.ToUInt64(data.AsSpan().Slice(offset, 8).ToArray(), 0);
        offset += 8;
        var protocolFeesToken1 = BitConverter.ToUInt64(data.AsSpan().Slice(offset, 8).ToArray(), 0);
        offset += 8;
        var fundFeesToken0 = BitConverter.ToUInt64(data.AsSpan().Slice(offset, 8).ToArray(), 0);
        offset += 8;
        var fundFeesToken1 = BitConverter.ToUInt64(data.AsSpan().Slice(offset, 8).ToArray(), 0);
        offset += 8;
        var mint = token0Mint != "So11111111111111111111111111111111111111112" ? token0Mint : token1Mint;
        Logger.LogDebug($"[cpmm pool]  {mint} {pubkey}");

        if (await _redisClient.HExistsAsync(RedisKey.SolanaRaydiumCpmmPool, mint))
        {
            var dict = await _redisClient.HGetAsync<Dictionary<string, Tron.Abp.Multiplex.Contracts.RaydiumCpmmPool>>(
                RedisKey.SolanaRaydiumCpmmPool, mint);
            dict[pubkey] = new Tron.Abp.Multiplex.Contracts.RaydiumCpmmPool(pubkey, ammConfig,
                poolCreator, token0Vault, token1Vault, lpMint, token0Mint, token1Mint, token0Program, token1Program,
                observationKey,
                authBump, status, lpMintDecimals, mint0Decimals, mint1Decimals, lpSupply, protocolFeesToken0,
                protocolFeesToken1, fundFeesToken0, fundFeesToken1);
            await _redisClient.HSetAsync(RedisKey.SolanaRaydiumCpmmPool, mint, dict);
        }
        else
        {
            var dict = new Dictionary<string, Tron.Abp.Multiplex.Contracts.RaydiumCpmmPool>();
            dict[pubkey] = new Tron.Abp.Multiplex.Contracts.RaydiumCpmmPool(pubkey, ammConfig,
                poolCreator, token0Vault, token1Vault, lpMint, token0Mint, token1Mint, token0Program, token1Program,
                observationKey,
                authBump, status, lpMintDecimals, mint0Decimals, mint1Decimals, lpSupply, protocolFeesToken0,
                protocolFeesToken1, fundFeesToken0, fundFeesToken1);
            await _redisClient.HSetAsync(RedisKey.SolanaRaydiumCpmmPool, mint, dict);
        }

        if (await _redisClient.HExistsAsync(RedisKey.SolanaTokenDexType, mint))
        {
            var dexType = await _redisClient.HGetAsync<string[]>(RedisKey.SolanaTokenDexType,
                              mint) ??
                          new string[] { };
            var arry = dexType;

            if (!Array.Exists(arry, it => it == DexType.RaydiumCpmm))
            {
                Array.Resize(ref arry, arry.Length + 1);
                arry[arry.Length - 1] = DexType.RaydiumCpmm;
                await _redisClient.HSetAsync(RedisKey.SolanaTokenDexType, mint, arry);
            }
        }
        else
        {
            await _redisClient.HSetAsync(RedisKey.SolanaTokenDexType, mint,
                new string[] { DexType.RaydiumCpmm });
        }
    }

    private async Task PriceHandler(string pubkey, byte[] data)
    {
        var offset = 0;
        var mint = Base58.Encode(data.AsSpan().Slice(offset, 32).ToArray()); //0 32
        offset += 32;
        var owner = Base58.Encode(data.AsSpan().Slice(offset, 32).ToArray()); //32 32
        offset += 32;
        ulong amount = BitConverter.ToUInt64(data.AsSpan().Slice(offset, 8).ToArray(), 0);

        Logger.LogDebug($"[cpmm price] {pubkey} {mint} {amount}");
        await _redisClient.HSetAsync(RedisKey.SolanaTokenRaydiumCpmmPrice, pubkey, amount);
    }

    /// <summary>
    /// 监听 来自 grpc 的消息
    /// </summary>
    /// <param name="cancellationToken"></param>
    private async Task OnSubscribe(CancellationToken cancellationToken)
    {
        while (await stream.ResponseStream.MoveNext(cancellationToken))
        {
            var data = stream.ResponseStream.Current;
            if (data.Account != null)
            {
                _lastMessageTime=DateTime.Now;
                //解析消息
                var accountData = data.Account.Account.Data.ToByteArray();
                var pubkey = Base58.Encode(data.Account.Account.Pubkey.ToByteArray());
                var type = data.Filters[0];

                switch (type)
                {
                    case "cpmmpoll":
                        await PoolHandler(pubkey, accountData);
                        break;
                    case "cpmmprice":
                        await PriceHandler(pubkey, accountData);
                        break;
                }
            }
        }
    }
}