// See https://aka.ms/new-console-template for more information


using NATS.Client.JetStream.Models;
using NATS.Net;

var url = "nats://192.168.31.23:4222";
await using var nc = new NatsClient(url);
var js = nc.CreateJetStreamContext();
var streamName = "EVENTS";


var stream = await js.CreateStreamAsync(new StreamConfig(streamName, subjects: ["events.>"])
{
    Retention = StreamConfigRetention.Workqueue,
});


await js.PublishAsync("events.us.page_loaded", "event-data");
await js.PublishAsync("events.us.mouse_clicked", "event-data");
await js.PublishAsync("events.us.input_focused", "event-data");
Console.WriteLine("published 3 messages");
Console.ReadLine();