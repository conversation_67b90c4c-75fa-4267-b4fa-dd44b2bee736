using System.Net.WebSockets;
using Microsoft.Extensions.Options;
using Solnet.Rpc.Core.Sockets;
using Solnet.Rpc.Messages;

namespace Tran.Abp.Ws.PumpSwap;

public class PumpPoolWorker : AsyncPeriodicBackgroundWorkerBase
{
    //private IWebSocketClient _webSocketClient;
    private string wsurl = "";
    private string WSOL = "So11111111111111111111111111111111111111112";
    private IRedisClient _redisClient;
    private DateTime _lastMessageTime; // 标记最后收到消息的时间
    public ILogger<PumpPoolWorker> Logger { get; set; }
    private IRpcClient _rpcClient;
    private IStreamingRpcClient _streamingRpcClient;

    public PumpPoolWorker(IOptions<SolanaOptions> solanaOptions, AbpAsyncTimer timer,
        IServiceScopeFactory serviceScopeFactory, IRedisClient redisClient, ISolanaApi solanaApi) : base(timer,
        serviceScopeFactory)
    {
        _redisClient = redisClient;

        Timer.Period = 60 * 1000;

        Logger = NullLogger<PumpPoolWorker>.Instance;
        wsurl = solanaOptions.Value.MainWsUri;
        _streamingRpcClient = Tran.Abp.Ws.Core.HeliusClientFactory.GetStreamingClient(wsurl, Logger);
        _rpcClient = ClientFactory.GetClient(solanaOptions.Value.MainUri, Logger);
        _streamingRpcClient.ConnectionStateChangedEvent += (sender, state) =>
        {
            if (state == WebSocketState.Open) _lastMessageTime = DateTime.Now;
        };
    }

    async void Callback(SubscriptionState state, ResponseValue<AccountKeyPair> value)
    {
        _lastMessageTime = DateTime.Now;
        var pubkey = value.Value.PublicKey;
        var accountData = new ReadOnlySpan<byte>(Convert.FromBase64String(value.Value.Account.Data[0]));
        Console.WriteLine($"{accountData.Length} bytes");
        //过滤掉非 pool池子信息
        if(accountData.Length>300)return;
        //Logger.LogDebug($"[pool pump]{pubkey}");
        var offset = 8 + 1 + 2;
        string creator = Base58.Encode(accountData.Slice(offset, 32).ToArray());
        offset += 32;
        string baseMint = Base58.Encode(accountData.Slice(offset, 32).ToArray());
        offset += 32;
        string quoteMint = Base58.Encode(accountData.Slice(offset, 32).ToArray());
        offset += 32;
        //此处有一个lp_mint
        offset += 32;
        string poolBaseToken = Base58.Encode(accountData.Slice(offset, 32).ToArray());
        offset += 32;
        string poolQuoteToken = Base58.Encode(accountData.Slice(offset, 32).ToArray());
        offset += 32;
        //此处 lp_supply
        offset += 8;
        string coinCreator = Base58.Encode(accountData.Slice(offset, 32).ToArray());
        Logger.LogDebug($"[pump amm pool ] {baseMint}=>{pubkey}");
        var swapPool =
            new Tron.Abp.Multiplex.Contracts.PumpSwapPool(pubkey, creator, baseMint, quoteMint, poolBaseToken,
                poolQuoteToken,coinCreator);
        //2025 0402 更改 pool存储 =》字典
        if (await _redisClient.HExistsAsync(RedisKey.SolanaPumpSwapPool, baseMint))
        {
            var dict =
                await _redisClient.HGetAsync<Dictionary<string, Tron.Abp.Multiplex.Contracts.PumpSwapPool>>(
                    RedisKey.SolanaPumpSwapPool, baseMint);
            dict[pubkey] = swapPool;
            await _redisClient.HSetAsync<Dictionary<string, Tron.Abp.Multiplex.Contracts.PumpSwapPool>>(
                RedisKey.SolanaPumpSwapPool, baseMint, dict);
        }
        else
        {
            var dict = new Dictionary<string, Tron.Abp.Multiplex.Contracts.PumpSwapPool>() { [pubkey] = swapPool };
            await _redisClient.HSetAsync<Dictionary<string, Tron.Abp.Multiplex.Contracts.PumpSwapPool>>(
                RedisKey.SolanaPumpSwapPool, baseMint, dict);
        }

        if (await _redisClient.HExistsAsync(RedisKey.SolanaTokenDexType, baseMint))
        {
            var dexType = await _redisClient.HGetAsync<string[]>(RedisKey.SolanaTokenDexType, baseMint) ??
                          new string[] { };
            var arry = dexType;

            if (!Array.Exists(arry, it => it == DexType.PumpSwapAmm))
            {
                Array.Resize(ref arry, arry.Length + 1);
                arry[arry.Length - 1] = DexType.PumpSwapAmm;
                await _redisClient.HSetAsync(RedisKey.SolanaTokenDexType, baseMint, arry);
            }
        }
        else
        {
            await _redisClient.HSetAsync(RedisKey.SolanaTokenDexType, baseMint, new string[] { DexType.PumpSwapAmm });
        }

        await _redisClient.PublishAsync(RedisKey.SolanaSubscriberTokenPumpPoolPrice,
            JsonSerializer.Serialize(swapPool));
    }

    private async Task InitSubscribeStream()
    {
        _lastMessageTime = DateTime.Now;
        var list = new List<MemCmp>()
        {
            new MemCmp() { Bytes = "DZqzZov6PB7XYjeNPcP34uJgPGE4tRLsgzDFej8Upump", Offset = 8 + 1 + 2 + 32 }
        };
        
        await _streamingRpcClient.SubscribeProgramAsync("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA",
            Callback, dataSize: 300, commitment: Commitment.Confirmed, memCmpList: null);
    }

    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {
        var timeSinceLastMessage = DateTime.Now - _lastMessageTime;
        Logger.LogWarning($"ws 断开 1s 后连接 {_lastMessageTime}=>{DateTime.Now}=>{timeSinceLastMessage.TotalSeconds}");
        if (timeSinceLastMessage.TotalSeconds >= 30)
        {
            Logger.LogWarning($"ws 断开 1s 后连接");
            await _streamingRpcClient.DisconnectAsync();
            await Task.Delay(1000);
            await _streamingRpcClient.ConnectAsync();
            await InitSubscribeStream();
        }
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        await _streamingRpcClient.ConnectAsync();
        await InitSubscribeStream();
        _lastMessageTime = DateTime.Now;

        Logger.LogDebug("获取 pump pool  服务=>启动");
        await base.StartAsync(cancellationToken);
        //await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        await _streamingRpcClient.DisconnectAsync();
        _streamingRpcClient.Dispose();
        Logger.LogDebug("获取 pump pool  服务=>停止");
        await base.StopAsync(cancellationToken);
        //await base.StopAsync(cancellationToken);
    }
}