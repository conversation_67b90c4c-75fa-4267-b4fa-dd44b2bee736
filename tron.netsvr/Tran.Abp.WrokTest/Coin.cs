namespace Tran.Abp.WrokTest;

public class Coin
{
    public string Mint { get; set; }
    public string Name { get; set; }
    public string Symbol { get; set; }
    public string Description { get; set; }
    public string ImageUri { get; set; }
    public string MetadataUri { get; set; }
    public string Twitter { get; set; }
    public string Telegram { get; set; }
    public string BondingCurve { get; set; }
    public string AssociatedBondingCurve { get; set; }
    public string Creator { get; set; }
    public ulong CreatedTimestamp { get; set; }
    public string RaydiumPool { get; set; }
    public bool Complete { get; set; }
    public ulong VirtualSolReserves { get; set; }
    public ulong VirtualTokenReserves { get; set; }
    public bool? Hidden { get; set; }
    public ulong TotalSupply { get; set; }
    public string Website { get; set; }
    public bool ShowName { get; set; }
    public ulong? LastTradeTimestamp { get; set; }
    public ulong? KingOfTheHillTimestamp { get; set; }
    public decimal MarketCap { get; set; }
    public bool Nsfw { get; set; }
    public string MarketId { get; set; }
    public bool? Inverted { get; set; }
    public ulong RealSolReserves { get; set; }
    public ulong RealTokenReserves { get; set; }
    public ulong LivestreamBanExpiry { get; set; }
    public ulong? LastReply { get; set; }
    public int ReplyCount { get; set; }
    public bool IsBanned { get; set; }
    public bool IsCurrentlyLive { get; set; }
    public bool Initialized { get; set; }
    public string VideoUri { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string PumpSwapPool { get; set; }
    public decimal? AthMarketCap { get; set; }
    public ulong? AthMarketCapTimestamp { get; set; }
    public string BannerUri { get; set; }
    public bool HideBanner { get; set; }
    public ulong LivestreamDownrankScore { get; set; }
    public decimal UsdMarketCap { get; set; }
}