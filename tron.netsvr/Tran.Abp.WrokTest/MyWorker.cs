using Solnet.AABot.Pumpfun;
using Solnet.Wallet;
using Tran.Abp.WrokTest;
using Tron.Abp.Domain;
using Tron.Abp.SqlSugar;

namespace Tran.Abp.Ws.RaydiumCpmm;

public class MyWorker : BackgroundWorkerBase
{
    private IRedisClient _redisClient;
    private Dictionary<string, WorkQueue<CTTask>> dictionary;
    private readonly SqlSugarRepository<UserAllTraderLog> _userAllTraderLogRepository;
    ISolanaApi _solanaApi;
    public IRpcClient _rpcClient;
    public ILogger<MyWorker> Logger { get; set; }

    public MyWorker(IRedisClient redisClient, SqlSugarRepository<UserAllTraderLog> userAllTraderLogRepository, ISolanaApi solanaApi) 
    {
        _redisClient = redisClient;
        _userAllTraderLogRepository = userAllTraderLogRepository;
        _solanaApi = solanaApi;
        Logger = NullLogger<MyWorker>.Instance;
        dictionary = new Dictionary<string, WorkQueue<CTTask>>();

        _rpcClient = _solanaApi.RpcClient;
    }

    private async Task PriceMonitor(CTTask item)
    {
        var i = 100000;
        while (true)
        {
            i -= 1;
            if(i<=0) break;
            Logger.LogDebug($"{item.TaskId} {item.Mint} {Thread.CurrentThread.ManagedThreadId}");
            var log =await _userAllTraderLogRepository.CopyNew().AsQueryable().Where(it=>it.Id==item.TaskId).FirstAsync();
            var mintInfo = await _solanaApi.GetMint(item.Mint);
            await _userAllTraderLogRepository.CopyNew().AsUpdateable()
                .SetColumns(it => it.CompleteTime == DateTime.Now)
                .Where(it => it.Id == item.TaskId)
                .ExecuteCommandAsync();
            
            await Task.Delay(800);
        }
    }

    private async Task Test()
    {
        var uid = 10001;
        var list = await _userAllTraderLogRepository.AsQueryable().ToListAsync();
        for (int i = 0; i < list.Count; i++)
        {
            var log = list[i];
            if (i == 100 || i == 200 || i == 300 ) uid+=1;
            WorkQueue<CTTask> workQueue;
            if (dictionary.TryGetValue($"{uid}_{log.Id}", out workQueue))
            {
                //if (await IsInWorkQueue($"{uid}", logID, mint)) return;
                var task = new CTTask(log.Id, uid, log.TokenAddress, "", "", "");
                workQueue.Enqueue(task);
                Logger.LogDebug($"  {log.Id}=>{uid} 装入{log.TokenAddress} 合计:{workQueue.Count}");
            }
            else
            {
                Logger.LogDebug($"新增 {log.Id}=>{uid} 装入{log.TokenAddress}");
                workQueue = QueueManager.CreateQueue<CTTask>(PriceMonitor, 100);
                dictionary[$"{uid}_{log.Id}"] = workQueue;
                var task = new CTTask(log.Id, uid, log.TokenAddress, "", "", "");
                workQueue.Enqueue(task);
            }
        }
    }
    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {

        /*var price = await _solanaApi.GetMintPrice("CLuCKaxQAah1YG4Qaqzvm4MjJ65ifzaiXqugSCn9WXUf"
            , DexType.PumpSwapAmm, "DxztYgAr5GrRS2gt3sC71WKxhUG6QGocRZGQUAVuX9e9");
        var account =
            Account.FromSecretKey(
                "3cF5AaSAsCtYtNrJVW44hxEDYJEFmiXUvANZJ83R6SFsKGLGXyFL1Mcdd14QHFL9JJPCu5aCgtSTbqfVs9VbzF78");
        var pump = new PumpFunImpl(_rpcClient, account, Logger);
       await pump.Create();
       await Task.Delay(1000);*/

        /*var bindDict = new Dictionary<string, string>()
        {
            ["bindData"] = "{\"Group\":14,\"MinAmount\":0.01,\"MaxAmount\":0.01}",
            ["uid"] = "100002",
            ["mint"] = "9DuCc1rpEWBL8XMNp2pjzYNeyb5yZ9M9Jujax9Jpump",
            ["DexType"] = DexType.Pumpfun,
        };
        await _redisClient.XAddAsync(StreamKey.BindTradeStream, bindDict);*/
        var keys = new string[] {"8PH3BRTXCbac8xWy24eiVGHfyiDYMtFW1XwAoTF4bonk","FAPwRXZRRfPaw5DWtRRHFZ6fbmtQtJizcRLkAmAjpump" };

        var mints = keys;
        var mintCreateTimeDict=new Dictionary<string, DateTime>();
        using (var pipe=_redisClient.StartPipe())
        {
            foreach (var mint in mints)
            {
                var mintInfoval = pipe.HGet<Dictionary<string, JupAgStreamResult.PoolData>>(RedisKey.SolanaTokenInfo, mint);
            }
            var mintInfos=pipe.EndPipe();
            foreach (Dictionary<string, JupAgStreamResult.PoolData> o in mintInfos)
            {
                if(o==null) continue;
                var first=o.Values.OrderBy(it=>it.CreatedAt).First();
                mintCreateTimeDict[first.BaseAsset.Id] = first.CreatedAt;
            }
        }

       
        
        await base.StartAsync(cancellationToken);
    }
    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {

        if (dictionary.Any())
        {
            foreach (var workQueue in dictionary)
            {
                await workQueue.Value.StopAsync();
            }
        }

        Logger.LogDebug($"跟单交易 子任务服务 停止");
        await base.StopAsync(cancellationToken);
    }

}