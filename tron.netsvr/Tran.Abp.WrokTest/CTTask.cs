namespace Tran.Abp.WrokTest;

public class CTTask
{
    /// <summary>
    /// 跟单 子任务初始化
    /// </summary>
    /// <param name="taskId"></param>
    /// <param name="uId"></param>
    /// <param name="mint"></param>
    /// <param name="isPump"></param>
    /// <param name="bondingAddress"></param>
    public CTTask(int taskId, int uId, string mint, string dexType, string bondingAddress, string poolId)
    {
        TaskId = taskId;
        UId = uId;
        Mint = mint;
        DexType= dexType;
        BondingAddress = bondingAddress;
        PoolId = poolId;
    }
    /// <summary>
    /// 快速交易 子任务初始化
    /// </summary>
    /// <param name="logId"></param>
    /// <param name="uId"></param>
    /// <param name="mint"></param>
    /// <param name="isPump"></param>
    /// <param name="bondingAddress"></param>
    public CTTask(string logId, int uId, string mint, string dexType, string bondingAddress, string poolId)
    {
        LogId = logId;
        UId = uId;
        Mint = mint;
        DexType= dexType;
        BondingAddress = bondingAddress;
        PoolId = poolId;
    }
    public string LogId { get; set; }
    public int TaskId { get; set; }
    public int UId { get; set; }
    public string Mint { get; set; }
    public string BondingAddress { get; set; }
    public string DexType { get; set; }
    public string PoolId { get; set; }
}