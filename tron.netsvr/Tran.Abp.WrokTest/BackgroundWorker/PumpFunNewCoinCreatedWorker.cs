using Microsoft.Extensions.Primitives;
using NATS.Client.Core;
using NATS.Client.Serializers.Json;
using NATS.Net;

namespace Tran.Abp.WrokTest.BackgroundWorker;

public class PumpFunNewCoinCreatedWorker : BackgroundWorkerBase
{
    private NatsClient _natsPumpFunClient;
    private NatsClient _natsClient;
    public ILogger<PumpFunNewCoinCreatedWorker> Logger { get; set; }

    public PumpFunNewCoinCreatedWorker(NatsClient natsClient)
    {
        _natsPumpFunClient = natsClient;
        _natsClient = natsClient;
        Logger = NullLogger<PumpFunNewCoinCreatedWorker>.Instance;
    }

    private async Task SubscribePumpFunNewCoinCreated()
    {
        await foreach (var msg in _natsPumpFunClient.SubscribeAsync<Coin>("newCoinCreated.prod"))
        {
            //Console.WriteLine($"[{DateTime.Now:HH:mm:ss,fff}]: {msg.Subject}:  {msg.Data.Mint} ");
            try
            {
                await _natsClient.PublishAsync(NatsKeys.UserSubscribeNewCoinCreated, msg.Data);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }
    }

    private async Task SubscribeUserPublish()
    {
        await foreach (var msg in _natsClient.SubscribeAsync<MsgType>(NatsKeys.UserPublish))
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss,fff}]: {msg.Subject}: {msg.Data.msgType} {msg.Data.msgData} ");
        }
    }


    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        var url = "wss://prod-v2.nats.realtime.pump.fun/";
        var opts = NatsOpts.Default with
        {
            Url = url,
            SerializerRegistry = NatsJsonSerializerRegistry.Default,
            AuthOpts = new NatsAuthOpts()
            {
                Username = "subscriber",
                Password = "lW5a9y20NceF6AE9"
            },
            WebSocketOpts = NatsWebSocketOpts.Default with
            {
                RequestHeaders = new Dictionary<string, StringValues>()
                {
                    { "Origin", "https://pump.fun" },
                    {
                        "Sec-Websocket-Extensions",
                        "permessage-deflate; client_no_context_takeover; server_no_context_takeover;client_max_window_bits"
                    },
                    { "User-Agent", "Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/20100101 Firefox/128.0" }
                }
            }
        };
        _natsPumpFunClient = new NatsClient(opts);
        //await client.ConnectAsync();

        // await _natsPumpFunClient.ConnectAsync();
        Task.Run(async () => await SubscribePumpFunNewCoinCreated(), cancellationToken);
        Task.Run(async () => await SubscribeUserPublish(), cancellationToken);

        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        await _natsClient.DisposeAsync();
        await _natsPumpFunClient.DisposeAsync();
        await base.StopAsync(cancellationToken);
    }
}