using Microsoft.Extensions.Primitives;
using NATS.Client.Core;
using NATS.Client.Serializers.Json;
using NATS.Net;
using Tron.Abp.Nats;

namespace Tran.Abp.WrokTest.BackgroundWorker;

public class MyNatsWorker : BackgroundWorkerBase
{
    NatsClient _natsClient;
    public ILogger<MyNatsWorker> Logger { get; set; }
    private INats _nats;

    public MyNatsWorker(INats nats)
    {
        _nats = nats;
        Logger = NullLogger<MyNatsWorker>.Instance;
    }

    private async Task SubscribeUserPublish(string channel)
    {
        await foreach (var msg in _natsClient.SubscribeAsync<NatsTronMsgType<object>>(channel))
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss,fff}]: {msg.Subject}: {msg.Data.MsgType} {msg.Data.MsgData} ");
        }
    }

    private async Task Init()
    {
        var url = "nats://192.168.31.23:4222";
        var opts = NatsOpts.Default with
        {
            Url = url,
            SerializerRegistry = NatsJsonSerializerRegistry.Default,
            AuthOpts = new NatsAuthOpts()
            {
                Username = "ruser",
                Password = "T0pS3cr3t"
            },
            /*WebSocketOpts = NatsWebSocketOpts.Default with
            {
                RequestHeaders = new Dictionary<string, StringValues>()
                {
                    { "Origin", "https://pump.fun" },
                    {
                        "Sec-Websocket-Extensions",
                        "permessage-deflate; client_no_context_takeover; server_no_context_takeover;client_max_window_bits"
                    },
                    { "User-Agent", "Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/20100101 Firefox/128.0" }
                }
            }*/
        };
        await Task.Delay(1000);
        _natsClient = new NatsClient(opts);
        var clientId = Guid.NewGuid().ToString();
        var channel = $"tron.trade.{clientId}";
        await _natsClient.PublishAsync(NatsKeys.SysSubscribeTronMsgType,
            new NatsTronMsgType<NatsMsgTronOn>(NatsMsgType.TronOn, clientId,
                new NatsMsgTronOn(channel, "100001")));
        Task.Run(async () => await SubscribeUserPublish(channel));
        Task.Run(async () =>
        {
            for (int i = 0; i < 100; i++)
            {
                var uid = "100001";
                await _nats.PublishAsync<NatsTronMsgType<object>>(channel, 
                    new NatsTronMsgType<object>()
                    );
                await Task.Delay(1000);
            }
        });
    }

    public override async Task StartAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        Task.Run(async () => await Init(), cancellationToken);
        Task.Run(async () => { await _nats.SubscribeAsync(NatsKeys.SysSubscribeTronMsgType); });

        

        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        await _natsClient.DisposeAsync();
        await base.StopAsync(cancellationToken);
    }
}