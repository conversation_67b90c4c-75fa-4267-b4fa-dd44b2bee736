using Solnet.Rpc.Builders;
using Solnet.Rpc.Utilities;
using Solnet.Wallet;
using Solnet.Wallet.Utilities;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Solnet.Rpc.Messages.HeliusTransactionInfo;

namespace Solnet.Rpc.Models
{
    public class MessageV0
    {
        /// <summary>
        /// 消息头部，包含签名和账户访问权限信息
        /// </summary>
        public MessageV0Header Header { get; set; }
        
        /// <summary>
        /// 交易中包含的静态账户密钥列表
        /// </summary>
        public List<PublicKey> StaticAccountKeys { get; set; }
        
        /// <summary>
        /// 最近区块哈希，用于防止交易重放
        /// </summary>
        public string RecentBlockhash { get; set; }
        
        /// <summary>
        /// 编译后的指令列表
        /// </summary>
        public List<CompiledInstructionV0> CompiledInstructions { get; set; }
        
        /// <summary>
        /// 地址表查找信息列表
        /// </summary>
        public List<Message.MessageAddressTableLookup> AddressTableLookups { get; set; }

        /// <summary>
        /// Serialize the message into the wire format.
        /// </summary>
        /// <returns>A byte array corresponding to the serialized message.</returns>
        public byte[] Serialize()
        {
            byte[] accountAddressesLength = ShortVectorEncoding.EncodeLength(StaticAccountKeys.Count);
            byte[] instructionsLength = ShortVectorEncoding.EncodeLength(CompiledInstructions.Count);
            int accountKeysBufferSize = StaticAccountKeys.Count * 32;

            MemoryStream accountKeysBuffer = new(accountKeysBufferSize);

            foreach (PublicKey key in StaticAccountKeys)
            {
                accountKeysBuffer.Write(key.KeyBytes);
            }

            var data = new List<byte>();

            data.AddRange(new[] { Header.NumRequiredSignatures, Header.NumReadonlySignedAccounts, Header.NumReadonlyUnsignedAccounts });
            data.AddRange(accountAddressesLength);
            data.AddRange(accountKeysBuffer.ToArray());
            data.AddRange(Encoders.Base58.DecodeData(RecentBlockhash));
            data.AddRange(instructionsLength);

            foreach (CompiledInstructionV0 compiledInstruction in CompiledInstructions)
            {
                data.Add(compiledInstruction.ProgramIdIndex);
                data.AddRange(compiledInstruction.AccountKeyIndexes);
                data.AddRange(compiledInstruction.Data);
            }
            return data.ToArray();
        }
    }
    
    public class MessageV0Header
    {
        /// <summary>
        /// 交易所需的签名数量
        /// </summary>
        public byte NumRequiredSignatures { get; set; }
        
        /// <summary>
        /// 只读已签名账户的数量
        /// </summary>
        public byte NumReadonlySignedAccounts { get; set; }
        
        /// <summary>
        /// 只读未签名账户的数量
        /// </summary>
        public byte NumReadonlyUnsignedAccounts { get; set; }
    }
    
    public class CompiledInstructionV0
    {
        /// <summary>
        /// 程序ID在账户密钥列表中的索引
        /// </summary>
        public byte ProgramIdIndex { get; set; }
        
        /// <summary>
        /// 指令所使用的账户在账户密钥列表中的索引
        /// </summary>
        public byte[] AccountKeyIndexes { get; set; }
        
        /// <summary>
        /// 指令的数据
        /// </summary>
        public byte[] Data { get; set; }
    }

    public class VersionedTransactionV0
    {
        /// <summary>
        /// 签名信息
        /// </summary>
        public List<SignaturePubKeyPair> Signatures { get; set; }

        /// <summary>
        /// 消息体
        /// </summary>
        public MessageV0 Message { get; set; }

        public static VersionedTransactionV0 CompileMessage(MessageV0 Message, List<Account> signers)
        {
            VersionedTransactionV0 tx = new VersionedTransactionV0()
            {
                Signatures = new List<SignaturePubKeyPair>(),
                Message = Message,
            };

            IEnumerable<Account> uniqueSigners = DeduplicateSigners(signers);
            byte[] serializedMessage = Message.Serialize();

            foreach (Account account in uniqueSigners)
            {
                byte[] signatureBytes = account.Sign(serializedMessage);
                tx.Signatures.Add(new SignaturePubKeyPair { PublicKey = account.PublicKey, Signature = signatureBytes });
            }

            return tx;
        }

        /// <summary>
        /// Deduplicate the list of given signers.
        /// </summary>
        /// <param name="signers">The signer accounts.</param>
        /// <returns>The signer accounts with removed duplicates</returns>
        private static IEnumerable<Account> DeduplicateSigners(IEnumerable<Account> signers)
        {
            List<Account> uniqueSigners = new();
            HashSet<Account> seen = new();

            foreach (Account account in signers)
            {
                if (seen.Contains(account)) continue;

                seen.Add(account);
                uniqueSigners.Add(account);
            }

            return uniqueSigners;
        }
    }
}
