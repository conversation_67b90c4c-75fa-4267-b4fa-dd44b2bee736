{"version": 3, "targets": {"net8.0-android": {}}, "libraries": {}, "projectFileDependencyGroups": {"net8.0-android": ["Acr.UserDialogs.Maui >= 9.1.0", "CommunityToolkit.Maui >= 8.0.1", "CommunityToolkit.Mvvm >= 8.3.1", "FreakyControls >= 0.5.0-pre", "Microsoft.Extensions.Logging.Debug >= 8.0.0", "Microsoft.Maui.Controls", "Microsoft.Maui.Controls.Compatibility", "TouchSocket >= 2.1.2"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\projects\\luoshan-fire-protection-master\\App\\LuoShanXiaoFang.csproj", "projectName": "LuoShanXiaoFang", "projectPath": "E:\\projects\\luoshan-fire-protection-master\\App\\LuoShanXiaoFang.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\projects\\luoshan-fire-protection-master\\App\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-android"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-android": {"targetAlias": "net8.0-android", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-android": {"targetAlias": "net8.0-android", "dependencies": {"Acr.UserDialogs.Maui": {"target": "Package", "version": "[9.1.0, )"}, "CommunityToolkit.Maui": {"target": "Package", "version": "[8.0.1, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.3.1, )"}, "FreakyControls": {"target": "Package", "version": "[0.5.0-pre, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "(, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "(, )"}, "TouchSocket": {"target": "Package", "version": "[2.1.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.408/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1012", "level": "Error", "message": "Platform version is not present for one or more target frameworks, even though they have specified a platform: net8.0-android"}]}