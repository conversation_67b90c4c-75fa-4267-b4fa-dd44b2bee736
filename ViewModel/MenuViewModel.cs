using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.Input;
using System.Diagnostics;
using Acr.UserDialogs;

namespace LuoShanXiaoFang.ViewModel
{
    public partial class MenuViewModel : BaseViewModel
    {
        private readonly IUserDialogs _userDialogs;
        public MenuViewModel(INavigationService navigationService, IUserDialogs userDialogs) : base(navigationService)
        {
            _userDialogs = userDialogs;

            if (!Preferences.ContainsKey("isopen"))
            {
                Preferences.Set("isopen", "0");
            }
        }

        [RelayCommand]
        private async Task Send(string cmd)
        {
            await Udp.SendAsync("192.168.1.255", 9901, cmd);
            await Task.Delay(200);
            await Udp.SendAsync("192.168.0.255", 9901, cmd);
            await Task.CompletedTask;

        }
        [RelayCommand]
        private async Task CloseAll(string cmd)
        {
            var flag = await _userDialogs.ConfirmAsync("确认要执行该操作吗？", "操作提示", "确认", "取消");
            if (flag)
            {
                Preferences.Set("isopen", "0");
                _userDialogs.ShowLoading("指令执行中...");
                await Udp.SendAsync("192.168.1.255", 9901, "o108");
                await Task.Delay(200);
                await Udp.SendAsync("192.168.0.255", 9901, "o108");
                await Task.Delay(5000);
                await Udp.SendAsync("192.168.1.255", 9901, "o106");
                await Task.Delay(200);
                await Udp.SendAsync("192.168.0.255", 9901, "o106");
                await Task.Delay(3000);
                await Udp.SendAsync("192.168.1.255", 9901, "o104");
                await Task.Delay(200);
                await Udp.SendAsync("192.168.0.255", 9901, "o104");
                await Task.Delay(1000);
                _userDialogs.HideLoading();
            }

            Debug.WriteLine(cmd);
            await Task.CompletedTask;
        }
        [RelayCommand]
        private async Task OpenAll(string cmd)
        {
            var flag = await _userDialogs.ConfirmAsync("确认要执行该操作吗？", "操作提示", "确认", "取消");
            if (flag)
            {
                if (Preferences.ContainsKey("isopen"))
                {
                    var isopen = Preferences.Get("isopen","0");
                    if (isopen == "0")
                    {
                        Preferences.Set("isopen", "1");
                        _userDialogs.ShowLoading("指令执行中...");
                        await Udp.SendAsync("192.168.1.255", 9901, "o105");
                        await Task.Delay(200);
                        await Udp.SendAsync("192.168.0.255", 9901, "o105");
                        await Task.Delay(3000);
                        await Udp.SendAsync("192.168.1.255", 9901, "o103");
                        await Task.Delay(200);
                        await Udp.SendAsync("192.168.0.255", 9901, "o103");
                        await Task.Delay(3000);
                        await Udp.SendAsync("192.168.1.255", 9901, "o107");
                        await Task.Delay(200);
                        await Udp.SendAsync("192.168.0.255", 9901, "o107");
                        await Task.Delay(1000);
                        _userDialogs.HideLoading();
                    }
                }
            }

            Debug.WriteLine(cmd);
            await Task.CompletedTask;
        }

        [RelayCommand]
        private async Task GoTo(string cmd)
        {
            Debug.WriteLine(cmd);
            await NavigationService.NavigateToAsync(cmd);
            await Task.CompletedTask;
        }
    }
}
