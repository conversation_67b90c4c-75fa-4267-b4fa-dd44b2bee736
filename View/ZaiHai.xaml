<?xml version="1.0" encoding="utf-8"?>

<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:freakyControls="clr-namespace:Maui.FreakyControls;assembly=Maui.FreakyControls"
             x:Class="LuoShanXiaoFang.View.ZaiHai" BackgroundImageSource="/Images/bg03.png" Shell.NavBarIsVisible="False">
    <ContentPage.Content>
        <!--灾害案例-->
        <Grid RowDefinitions="75,*,*,*,*,*,*,*">
            <Label Text="灾害案例" TextColor="White" VerticalTextAlignment="Center" HorizontalTextAlignment="Center" Grid.Row="1" FontSize="32"></Label>
            <!--视频-->
            <StackLayout Orientation="Vertical" Grid.Row="2"  Margin="0, 0, 0 ,5">
                <StackLayout Orientation="Horizontal" VerticalOptions="Center" HorizontalOptions="Center" >
                    <freakyControls:FreakyButton
                        Margin="5,10"
                        Animation="FadeAndScale"
                        WidthRequest="160"
                        BackgroundColor="{StaticResource Primary}"
                        BorderColor="Black"
                        BusyColor="White"
                        Command="{Binding SendCmdCommand}"
                        CommandParameter="z101"
                        HorizontalTextAlignment="Center"
                        LineBreakMode="CharacterWrap"
                        VerticalTextAlignment="Center">
                        <Grid>
                            <Image Source="/Images/btn02.png" />
                            <Label
                                FontSize="18"
                                HorizontalTextAlignment="Center"
                                Text="视频一"
                                FontAttributes="Bold"
                                TextColor="#e6ce00"
                                VerticalTextAlignment="Center" />
                        </Grid>
                    </freakyControls:FreakyButton>
                    <freakyControls:FreakyButton
                        Margin="5,10"
                        Animation="FadeAndScale"
                        WidthRequest="160"
                        BackgroundColor="{StaticResource Primary}"
                        BorderColor="Black"
                        BusyColor="White"
                        Command="{Binding SendCmdCommand}"
                        CommandParameter="z102"
                        HorizontalTextAlignment="Center"
                        LineBreakMode="CharacterWrap"
                        VerticalTextAlignment="Center">
                        <Grid>
                            <Image Source="/Images/btn02.png" />
                            <Label
                                FontSize="18"
                                FontAttributes="Bold"
                                HorizontalTextAlignment="Center"
                                Text="视频二"
                                TextColor="#e6ce00"
                                VerticalTextAlignment="Center" />
                        </Grid>
                    </freakyControls:FreakyButton>
                    <freakyControls:FreakyButton
                        Margin="5,10"
                        Animation="FadeAndScale"
                        WidthRequest="160"
                        BackgroundColor="{StaticResource Primary}"
                        BorderColor="Black"
                        BusyColor="White"
                        Command="{Binding SendCmdCommand}"
                        CommandParameter="z103"
                        HorizontalTextAlignment="Center"
                        LineBreakMode="CharacterWrap"
                        VerticalTextAlignment="Center">
                        <Grid>
                            <Image Source="/Images/btn02.png" />
                            <Label
                                FontSize="18"
                                HorizontalTextAlignment="Center"
                                Text="视频三"
                                FontAttributes="Bold"
                                TextColor="#e6ce00"
                                VerticalTextAlignment="Center" />
                        </Grid>
                    </freakyControls:FreakyButton>
                </StackLayout>
                <StackLayout Orientation="Horizontal" VerticalOptions="Center" HorizontalOptions="Center" >

                    <freakyControls:FreakyButton
     Margin="5,10"
     Animation="FadeAndScale"
     WidthRequest="160"
     BackgroundColor="{StaticResource Primary}"
     BorderColor="Black"
     BusyColor="White"
     Command="{Binding SendCmdCommand}"
     CommandParameter="z104"
     HorizontalTextAlignment="Center"
     LineBreakMode="CharacterWrap"
     VerticalTextAlignment="Center">
                        <Grid>
                            <Image Source="/Images/btn02.png" />
                            <Label
             FontSize="18"
             HorizontalTextAlignment="Center"
             Text="视频四"
             FontAttributes="Bold"
             TextColor="#e6ce00"
             VerticalTextAlignment="Center" />
                        </Grid>
                    </freakyControls:FreakyButton>
                    <freakyControls:FreakyButton
     Margin="5,10"
     WidthRequest="160"
     Animation="FadeAndScale"
     BackgroundColor="{StaticResource Primary}"
     BorderColor="Black"
     BusyColor="White"
     Command="{Binding SendCmdCommand}"
     CommandParameter="z105"
     HorizontalTextAlignment="Center"
     LineBreakMode="CharacterWrap"
     VerticalTextAlignment="Center">
                        <Grid>
                            <Image Source="/Images/btn02.png" />
                            <Label
             FontSize="18"
             FontAttributes="Bold"
             HorizontalTextAlignment="Center"
             Text="视频五"
             TextColor="#e6ce00"
             VerticalTextAlignment="Center" />
                        </Grid>
                    </freakyControls:FreakyButton>
                    <freakyControls:FreakyButton
     Margin="5,10"
     Animation="FadeAndScale"
     WidthRequest="160"
     BackgroundColor="{StaticResource Primary}"
     BorderColor="Black"
     BusyColor="White"
     Command="{Binding SendCmdCommand}"
     CommandParameter="z106"
     HorizontalTextAlignment="Center"
     LineBreakMode="CharacterWrap"
     VerticalTextAlignment="Center">
                        <Grid>
                            <Image Source="/Images/btn02.png" />
                            <Label
             FontSize="18"
             HorizontalTextAlignment="Center"
             Text="视频六"
             FontAttributes="Bold"
             TextColor="#e6ce00"
             VerticalTextAlignment="Center" />
                        </Grid>
                    </freakyControls:FreakyButton>
                </StackLayout>
            </StackLayout>
            <!--控制-->
            <StackLayout Orientation="Vertical" Grid.Row="3"  Margin="0, 10, 0 ,5">

                <StackLayout Orientation="Horizontal" VerticalOptions="Center" HorizontalOptions="Center" >
                    <freakyControls:FreakyButton
            Margin="5,10"
            Animation="FadeAndScale"
            BackgroundColor="{StaticResource Primary}"
            BorderColor="Black"
            BusyColor="White"
            WidthRequest="160"
            Command="{Binding SendCmdCommand}"
            CommandParameter="z107"
            HorizontalTextAlignment="Center"
            LineBreakMode="CharacterWrap"
            VerticalTextAlignment="Center">
                        <Grid>
                            <Image Source="/Images/btn02.png" />
                            <Label
                    FontSize="18"
                    HorizontalTextAlignment="Center"
                    Text="播放"
                    FontAttributes="Bold"
                    TextColor="#e6ce00"
                    VerticalTextAlignment="Center" />
                        </Grid>
                    </freakyControls:FreakyButton>
                    <freakyControls:FreakyButton
            Margin="5,10"
            Animation="FadeAndScale"
            WidthRequest="160"
            BackgroundColor="{StaticResource Primary}"
            BorderColor="Black"
            BusyColor="White"
            Command="{Binding SendCmdCommand}"
            CommandParameter="z108"
            HorizontalTextAlignment="Center"
            LineBreakMode="CharacterWrap"
            VerticalTextAlignment="Center">
                        <Grid>
                            <Image Source="/Images/btn02.png" />
                            <Label
                    FontSize="18"
                    FontAttributes="Bold"
                    HorizontalTextAlignment="Center"
                    Text="暂停"
                    TextColor="#e6ce00"
                    VerticalTextAlignment="Center" />
                        </Grid>
                    </freakyControls:FreakyButton>
                    <freakyControls:FreakyButton
                        Margin="5,10"
                        Animation="FadeAndScale"
                        WidthRequest="160"
                        BackgroundColor="{StaticResource Primary}"
                        BorderColor="Black"
                        BusyColor="White"
                        Command="{Binding SendCmdCommand}"
                        CommandParameter="z109"
                        HorizontalTextAlignment="Center"
                        LineBreakMode="CharacterWrap"
                        VerticalTextAlignment="Center">
                        <Grid>
                            <Image Source="/Images/btn02.png" />
                            <Label
                                FontSize="18"
                                HorizontalTextAlignment="Center"
                                Text="停止"
                                FontAttributes="Bold"
                                TextColor="#e6ce00"
                                VerticalTextAlignment="Center" />
                        </Grid>
                    </freakyControls:FreakyButton>
                </StackLayout>
                <StackLayout Orientation="Horizontal" VerticalOptions="Center" HorizontalOptions="Center" >

                    <freakyControls:FreakyButton
                        Margin="5,10"
                        WidthRequest="160"
                        Animation="FadeAndScale"
                        BackgroundColor="{StaticResource Primary}"
                        BorderColor="Black"
                        BusyColor="White"
                        Command="{Binding SendCmdCommand}"
                        CommandParameter="z110"
                        HorizontalTextAlignment="Center"
                        LineBreakMode="CharacterWrap"
                        VerticalTextAlignment="Center">
                        <Grid>
                            <Image Source="/Images/btn02.png" />
                            <Label
                                FontSize="18"
                                FontAttributes="Bold"
                                HorizontalTextAlignment="Center"
                                Text="音量+"
                                TextColor="#e6ce00"
                                VerticalTextAlignment="Center" />
                        </Grid>
                    </freakyControls:FreakyButton>
                    <freakyControls:FreakyButton
                        Margin="5,10"
                        Animation="FadeAndScale"
                        BackgroundColor="{StaticResource Primary}"
                        BorderColor="Black"
                        BusyColor="White"
                        WidthRequest="160"
                        Command="{Binding SendCmdCommand}"
                        CommandParameter="z111"
                        HorizontalTextAlignment="Center"
                        LineBreakMode="CharacterWrap"
                        VerticalTextAlignment="Center">
                        <Grid>
                            <Image Source="/Images/btn02.png" />
                            <Label
                                FontSize="18"
                                FontAttributes="Bold"
                                HorizontalTextAlignment="Center"
                                Text="音量-"
                                TextColor="#e6ce00"
                                VerticalTextAlignment="Center" />
                        </Grid>
                    </freakyControls:FreakyButton>
                </StackLayout>
            </StackLayout>


            <StackLayout Orientation="Vertical" Grid.Row="6"  Margin="0, 5, 0 ,0">
                <freakyControls:FreakyButton
            Margin="5,10"
            Animation="FadeAndScale"
            BackgroundColor="{StaticResource Primary}"
            BorderColor="Black"
            BusyColor="White"
            Command="{Binding GoBackCommand}"
            CommandParameter="closeall"
            HorizontalTextAlignment="Center"
            LineBreakMode="CharacterWrap"
            VerticalTextAlignment="Center">
                    <Grid>
                        <Image Source="/Images/btn02.png" />
                        <Label
                    FontSize="18"
                    HorizontalTextAlignment="Center"
                    Text="返回"
                    FontAttributes="Bold"
                    TextColor="#e6ce00"
                    VerticalTextAlignment="Center" />
                    </Grid>
                </freakyControls:FreakyButton>
            </StackLayout>
        </Grid>
    </ContentPage.Content>
</ContentPage>